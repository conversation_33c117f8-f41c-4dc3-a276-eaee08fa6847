{"name": "rany-admin", "type": "module", "version": "1.3.7", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "test": "vitest dev", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify-json/ry": "workspace:*", "@iconify/vue": "4.1.2", "@originjs/vite-plugin-commonjs": "^1.0.3", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.44.0", "@vue-flow/minimap": "^1.5.3", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pptx": "^1.0.0", "@vue/test-utils": "^2.4.6", "@vueuse/core": "11.1.0", "blitzar": "^1.2.4", "clipboard": "2.0.11", "component-library": "workspace:*", "dayjs": "1.11.13", "echarts": "5.5.1", "html-to-image": "^1.11.13", "json-editor-vue3": "^1.1.1", "json5": "2.2.3", "jspdf": "^3.0.1", "naive-ui": "2.40.1", "nprogress": "0.2.0", "pinia": "2.2.4", "pinia-plugin-persistedstate": "^4.1.3", "pinyin-pro": "^3.26.0", "sortablejs": "^1.15.6", "tailwind-merge": "2.5.3", "vue": "3.5.11", "vue-demi": "0.14.6", "vue-draggable-plus": "0.5.3", "vue-i18n": "10.0.4", "vue-router": "4.4.5", "vue3-pdfjs": "^0.1.6"}, "devDependencies": {"@iconify/json": "2.2.258", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.4.1", "@types/node": "22.7.5", "@types/nprogress": "0.2.3", "@types/sortablejs": "^1.15.8", "@unocss/eslint-config": "0.55.7", "@unocss/postcss": "0.55.7", "@unocss/preset-icons": "0.55.7", "@unocss/preset-uno": "0.55.7", "@unocss/transformer-directives": "0.55.7", "@unocss/transformer-variant-group": "0.55.7", "@unocss/vite": "0.55.7", "@vitejs/plugin-vue": "5.1.4", "@vitejs/plugin-vue-jsx": "4.0.1", "autoprefixer": "^10.4.21", "eslint": "9.12.0", "eslint-plugin-vue": "9.28.0", "jsdom": "^25.0.1", "lint-staged": "15.2.10", "postcss": "^8.5.6", "sass": "1.79.4", "simple-git-hooks": "2.11.1", "tsx": "4.19.1", "typescript": "5.6.3", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "0.19.3", "unplugin-vue-components": "0.27.4", "vite": "5.4.8", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.4.6", "vitest": "^2.1.3", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.1.6"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://admin.soybeanjs.cn"}