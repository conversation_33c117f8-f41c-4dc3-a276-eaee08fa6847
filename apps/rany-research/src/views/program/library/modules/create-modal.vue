<script setup lang="ts">
import { defineEmits, defineProps } from 'vue';

const props = defineProps<{
  data: any[];
}>();

const emit = defineEmits<{
  confirmed: [item: any];
}>();

const visible = ref(false);
const actionLoading = ref(false);
const selectedItem = ref<any>(null);

function handleCancel() {
  visible.value = false;
  selectedItem.value = null;
}

async function handleConfirm() {
  if (!selectedItem.value) {
    window.$message?.warning('请选择一个方案模板');
    return;
  }

  actionLoading.value = true;

  try {
    // 触发父组件的确认事件
    emit('confirmed', selectedItem.value);

    // 关闭模态框
    visible.value = false;
    selectedItem.value = null;
  } finally {
    actionLoading.value = false;
  }
}

function selectItem(item: any) {
  selectedItem.value = item;
}

defineExpose({
  show: () => {
    visible.value = true;
    selectedItem.value = null;
  }
});
</script>

<template>
  <NModal
    v-model:show="visible"
    :mask-closable="false"
    preset="card"
    class="w-800px"
    :segmented="{
      content: true,
      footer: true
    }"
    :content-style="{
      padding: '0px'
    }"
  >
    <template #header>
      <div class="flex items-center gap-3">
        <SvgIcon icon="tabler:plus" class="h-5 w-5 text-primary" />
        <span class="text-lg font-semibold">新建方案</span>
      </div>
    </template>

    <div>
      <!-- 选择卡片网格 -->
      <div class="grid grid-cols-3 max-h-400px gap-4 overflow-y-auto p-4">
        <!-- 新建空白方案 -->
        <div
          class="group relative cursor-pointer transition-all duration-200 hover:scale-102"
          @click="selectItem({ id: 'blank', name: '新建空白方案', type: 'blank' })"
        >
          <NCard
            class="h-120px border-2 transition-all duration-200"
            :class="[
              selectedItem?.id === 'blank'
                ? 'border-primary bg-primary/5 shadow-lg shadow-primary/20'
                : 'border-gray-200 hover:border-primary/50 hover:shadow-md border-dashed'
            ]"
            :hoverable="true"
          >
            <div class="h-full flex flex-col items-center justify-center text-center">
              <!-- 加号图标 -->
              <div
                class="mb-3 h-10 w-10 flex items-center justify-center rounded-lg transition-colors duration-200"
                :class="[
                  selectedItem?.id === 'blank'
                    ? 'bg-primary text-white'
                    : 'bg-gray-100 text-gray-600 group-hover:bg-primary/10 group-hover:text-primary'
                ]"
              >
                <SvgIcon icon="tabler:plus" class="h-6 w-6" />
              </div>

              <!-- 名称 -->
              <div
                class="font-medium transition-colors duration-200"
                :class="[selectedItem?.id === 'blank' ? 'text-primary' : 'text-gray-700 group-hover:text-primary']"
              >
                新建空白方案
              </div>

              <!-- 描述 -->
              <div class="mt-1 text-xs text-gray-500">从头开始创建新方案</div>
            </div>

            <!-- 选中状态指示器 -->
            <div
              v-if="selectedItem?.id === 'blank'"
              class="absolute h-6 w-6 flex items-center justify-center rounded-full bg-primary shadow-lg -right-1 -top-1"
            >
              <SvgIcon icon="tabler:check" class="h-4 w-4 text-white" />
            </div>
          </NCard>
        </div>

        <!-- 现有方案模板 -->
        <div
          v-for="item in props.data"
          :key="item.id"
          class="group relative cursor-pointer transition-all duration-200 hover:scale-102"
          @click="selectItem(item)"
        >
          <NCard
            class="h-120px border-2 transition-all duration-200"
            :class="[
              selectedItem?.id === item.id
                ? 'border-primary bg-primary/5 shadow-lg shadow-primary/20'
                : 'border-gray-200 hover:border-primary/50 hover:shadow-md'
            ]"
            :hoverable="true"
          >
            <div class="h-full flex flex-col items-center justify-center text-center">
              <!-- 图标 -->
              <div
                class="mb-3 h-10 w-10 flex items-center justify-center rounded-lg transition-colors duration-200"
                :class="[
                  selectedItem?.id === item.id
                    ? 'bg-primary text-white'
                    : 'bg-gray-100 text-gray-600 group-hover:bg-primary/10 group-hover:text-primary'
                ]"
              >
                <SvgIcon icon="tabler:file-text" class="h-5 w-5" />
              </div>

              <!-- 名称 -->
              <div
                class="font-medium transition-colors duration-200"
                :class="[selectedItem?.id === item.id ? 'text-primary' : 'text-gray-700 group-hover:text-primary']"
              >
                {{ item.name }}
              </div>

              <!-- 描述 -->
              <div v-if="item.description" class="line-clamp-2 mt-1 text-xs text-gray-500">
                {{ item.description }}
              </div>
            </div>

            <!-- 选中状态指示器 -->
            <div
              v-if="selectedItem?.id === item.id"
              class="absolute h-6 w-6 flex items-center justify-center rounded-full bg-primary shadow-lg -right-1 -top-1"
            >
              <SvgIcon icon="tabler:check" class="h-4 w-4 text-white" />
            </div>
          </NCard>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-if="!props.data || props.data.length === 0"
        class="flex flex-col items-center justify-center px-4 py-12 text-gray-500"
      >
        <SvgIcon icon="tabler:folder-open" class="mb-3 h-12 w-12 text-gray-300" />
        <div class="text-sm">暂无方案模板，但您可以创建空白方案</div>
      </div>
    </div>

    <template #action>
      <div class="flex justify-end gap-3">
        <NButton size="medium" @click="handleCancel">取消</NButton>
        <NButton type="primary" size="medium" :loading="actionLoading" :disabled="!selectedItem" @click="handleConfirm">
          确定创建
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<style lang="scss" scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
