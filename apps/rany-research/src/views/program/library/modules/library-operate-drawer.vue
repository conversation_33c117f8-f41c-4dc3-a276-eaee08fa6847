<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useProgramStore } from '@/store/modules/program';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Program.ProgramItem | null;
}
const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}
const emit = defineEmits<Emits>();

const programStore = useProgramStore();
const { programModal: model } = storeToRefs(programStore);

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新建方案',
    edit: '编辑方案'
  };
  return titles[props.operateType];
});

const riskIds = ref<string[]>([]);

type RuleKey = Extract<Api.Program.ProgramItem, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule[]> = {
  name: [defaultRequiredRule]
};

async function handleInitModel() {
  programStore.restModel();
  riskIds.value = [];
  if (props.operateType === 'edit' && props.rowData) {
    await programStore.getProgramDetail(props.rowData?.id);
    const riskId = model.value.riskId;
    if (riskId) riskIds.value = riskId.split(',');
  }
}

function closeDrawer() {
  visible.value = false;
  riskIds.value = [];
  programStore.restModel();
}

function handleRiskChange(newVal: string | string[] | undefined) {
  riskIds.value = newVal as string[];
  model.value.riskId = riskIds.value.join(',');
}

async function handleSubmit(saveAndEnable?: boolean) {
  await validate();
  if (saveAndEnable) {
    model.value.status = programStore.Status.published;
  }
  if (props.operateType === 'edit') {
    await programStore.updateProgram();
  }

  if (props.operateType === 'add') {
    await programStore.addProgram();
  }
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="top">
        <h2 class="font-700">基础信息</h2>
        <NFormItem path="name" label="名称" required>
          <NInput v-model:value="model.name" placeholder="请输入方案名称" />
        </NFormItem>
        <NFormItem path="gender" label="疾病分型">
          <DiseaseSelect v-model:value="model.diseaseId" />
        </NFormItem>
        <NFormItem path="riskId" label="危险度分型">
          <RiskCascader v-model:value="riskIds" :multiple="true" @update:value="handleRiskChange" />
        </NFormItem>
        <NFormItem path="comment" label-placement="top" label="注意事项">
          <NInput
            v-model:value="model.comment"
            placeholder="请输入注意事项"
            class="w-full"
            type="textarea"
            :autosize="{
              minRows: 3
            }"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit(false)">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
