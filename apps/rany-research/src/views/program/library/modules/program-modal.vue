<script setup lang="tsx">
import { computed, ref, watchEffect } from 'vue';
import { storeToRefs } from 'pinia';
import { useItemStore } from '@/store/modules/program/item';
import { useProgramStore } from '@/store/modules/program';
import { ProgramDataSource } from '@/store/modules/program/programDataSource';

const store = useItemStore();
const programStore = useProgramStore();
const { programModal, actionLoading } = storeToRefs(programStore);
const editMode = computed(() => Boolean(programModal.value.id));
const visible = ref(false);

const emit = defineEmits<{
  (e: 'submitted'): void;
}>();

defineExpose({
  show: () => {
    visible.value = true;
  }
});

watchEffect(async () => {
  if (visible.value && programModal.value.id) {
    store.dataSource = new ProgramDataSource();
  }
});

const handleCancel = () => {
  visible.value = false;
};

const handleConfirm = async () => {
  programModal.value.status = programStore.Status.published;
  await programStore.updateProgram();

  handleCancel();
  emit('submitted');
};

const programTableRef = ref();

const hasTableData = computed(() => {
  return programTableRef.value?.hasData ?? false;
});
</script>

<template>
  <NModal v-model:show="visible" size="small" class="mx-5" title="方案设置" :mask-closable="false" preset="card">
    <ProgramTable ref="programTableRef" class="py-2" :edit-mode="editMode" :solu-id="programModal.id" />
    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" @click="handleCancel">取消</NButton>
      <NButton type="primary" :loading="actionLoading" :disabled="!hasTableData" class="w-24" @click="handleConfirm">
        发布
      </NButton>
    </div>
  </NModal>
</template>

<style lang="scss" scoped>
:deep(.n-data-table-th .remove-action-btn) {
  opacity: 0;
  transition: all 0.3s;
  position: absolute;
  right: 0;
  top: 50%;
  translate: 0 -50%;
}
:deep(.n-data-table-th:hover .n-data-table-th__title-wrapper .remove-action-btn) {
  opacity: 1;
}
</style>
