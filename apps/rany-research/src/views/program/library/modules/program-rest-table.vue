<script setup lang="tsx">
import type { DataTableColumn, DataTableColumnGroup } from 'naive-ui';
import { ref, toRefs, watchEffect } from 'vue';

interface Props {
  headers?: Api.Program.Header[];
  tableData?: Api.Program.TableDatum[];
}

const props = defineProps<Props>();
const { headers, tableData } = toRefs(props);

const tableColumns = ref<DataTableColumn[]>([]);

watchEffect(() => {
  const col = headers.value;
  if (col) {
    tableColumns.value = (col as DataTableColumnGroup[]).map(column => {
      column.align = 'center';
      if (column.title === '天数') {
        column.width = 60;
      }
      return column;
    });
  }
});
</script>

<template>
  <div class="data-common-table table-container">
    <NDataTable
      class="data-table-sm data-table-bordered"
      max-height="500"
      :bordered="true"
      size="small"
      :single-line="false"
      :data="tableData"
      :columns="tableColumns"
    />
  </div>
</template>

<style lang="scss" scoped>
/* 使用统一的 n-data-table 样式，无需自定义样式 */
</style>
