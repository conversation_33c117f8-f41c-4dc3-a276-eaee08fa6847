<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import type { SelectOption } from 'naive-ui';
import { useProgramStore } from '@/store/modules/program';

interface Props {
  searchParams?: any;
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({
    pageNo: 1,
    riskId: [],
    status: null,
    pageSize: 10,
    restriction: 1,
    name: '',
    userName: '',
    userId: ''
  })
});

interface Emits {
  (e: 'filter-change', params: any): void;
}

const emit = defineEmits<Emits>();

const programStore = useProgramStore();

// 发布状态选择器选项
const statusOptions = ref<SelectOption[]>([]);
const selectedStatus = ref<string | null>(props.searchParams?.status || null);

// 危险度分型选择器选项
const selectedRiskId = ref<string[]>(props.searchParams?.riskId || []);

// 初始化状态选项
function initStatusOptions() {
  statusOptions.value = programStore.statusOptions;
}

// 处理状态筛选变化
const handleStatusChange = (value: string | null) => {
  selectedStatus.value = value;
  emit('filter-change', { status: value });
};

// 处理危险度分型筛选变化
const handleRiskChange = (value: string | string[] | undefined) => {
  let riskIds: string[] = [];
  if (Array.isArray(value)) {
    riskIds = value;
  } else if (value) {
    riskIds = [value];
  }
  selectedRiskId.value = riskIds;
  emit('filter-change', { riskId: riskIds });
};

// 监听外部搜索参数变化
watch(
  () => props.searchParams?.status,
  newValue => {
    selectedStatus.value = newValue || null;
  }
);

watch(
  () => props.searchParams?.riskId,
  newValue => {
    selectedRiskId.value = newValue || [];
  }
);

onMounted(() => {
  initStatusOptions();
});
</script>

<template>
  <div class="flex gap-16px">
    <div class="filter-item">
      <div class="filter-label">发布状态：</div>
      <div class="filter-control">
        <NSelect
          v-model:value="selectedStatus"
          class="filter-select"
          placeholder="请选择发布状态"
          :options="statusOptions"
          clearable
          @update:value="handleStatusChange"
        />
      </div>
    </div>

    <div class="filter-item">
      <div class="filter-label">危险度分型：</div>
      <div class="filter-control">
        <RiskCascader
          v-model:value="selectedRiskId"
          class="filter-cascader"
          placeholder="请选择危险度分型"
          @update:value="handleRiskChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  white-space: nowrap;
  font-size: 14px;
  color: var(--text-color-2);
}

.filter-select {
  min-width: 120px;
}

.filter-cascader {
  min-width: 160px;
}
</style>
