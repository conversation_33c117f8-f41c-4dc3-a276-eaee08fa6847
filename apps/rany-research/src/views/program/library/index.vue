<script setup lang="tsx">
import { NButton, NSpace, NTag, useDialog } from 'naive-ui';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useTabsStore } from 'component-library';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { fetchPostProgramList } from '@/service/api/program';
import { tableColumnArrayStringRender } from '@/utils/table';
import { useProgramStore } from '@/store/modules/program';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import LibraryOperateDrawer from './modules/library-operate-drawer.vue';
import ProgramModal from './modules/program-modal.vue';
import LibrarySearch from './modules/library-search.vue';
import CreateModal from './modules/create-modal.vue';

const router = useRouter();

const appStore = useAppStore();
const store = useProgramStore();

const tabsStore = useTabsStore();
const programStore = useProgramStore();

const { programModal } = storeToRefs(programStore);

const { statusLabel, Status } = store;
const copyLoadingId = ref<string>('');

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination, searchParams } = useTable({
  apiFn: fetchPostProgramList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    riskId: [],
    status: null,
    pageSize: 10,
    restriction: 1,
    name: '',
    userName: '',
    userId: ''
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center'
    },
    {
      title: '方案名称',
      key: 'name',
      ellipsis: true,
      resizable: true,
      render: row => (
        <n-button
          class="text-primary"
          text
          onClick={() => {
            useTabsStore().addTab({
              title: row.name,
              path: `/program/operate/show/${row.id}`,
              query: {
                id: row.id
              },
              type: 'rany-research',
              closable: true,
              label: row.name,
              id: row.id
            });
            router.push(`/program/operate/show/${row.id}`);
          }}
        >
          {row.name}
        </n-button>
      )
    },
    {
      title: '方案天数',
      key: 'dayCount'
    },
    {
      title: '危险度分型',
      key: 'riskNames',
      render: tableColumnArrayStringRender('riskNames')
    },
    {
      title: '疾病分型',
      key: 'diseaseName'
    },
    {
      title: '方案概要',
      key: 'itemNames',
      ellipsis: true,
      render: tableColumnArrayStringRender('itemNames')
    },
    {
      title: '创建时间',
      key: 'createTime'
    },
    {
      title: '更新时间',
      key: 'updateTime'
    },
    {
      title: '状态',
      key: 'status',
      align: 'center',
      render: row => (
        <NSpace>
          <NTag type="info" bordered={false}>
            {statusLabel(row.status)}
          </NTag>
        </NSpace>
      )
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      width: 280,
      render: row => (
        <div class="flex-center gap-4px">
          {row.status !== Status.published ? (
            <div class="flex gap-4px">
              <NButton type="primary" ghost size="small" onClick={() => edit(row)}>
                {$t('common.edit')}
              </NButton>
              {/* <NButton type="primary" ghost size="small" onClick={() => handleOpenProgramModal(row)}>
                方案
              </NButton> */}
              <NButton type="error" ghost size="small" onClick={() => handleConfirm(row.id, Status.deleted)}>
                删除
              </NButton>
            </div>
          ) : null}

          {row.status === Status.disabled ? (
            <NButton type="success" ghost size="small" onClick={() => handleConfirm(row.id, Status.published)}>
              发布
            </NButton>
          ) : null}
          {row.status === Status.published ? (
            <NButton type="warning" ghost size="small" onClick={() => handleConfirm(row.id, Status.disabled)}>
              停用
            </NButton>
          ) : null}

          <NButton
            type="primary"
            ghost
            size="small"
            loading={copyLoadingId.value === row.id}
            onClick={() => handleCopyProgram(row.id)}
          >
            复制
          </NButton>
        </div>
      )
    }
  ]
});
const { drawerVisible, operateType, editingData } = useTableOperate(data, getData);

function edit(vo: Api.Program.ProgramItem) {
  handleOpenProgramModal(vo);
  useTabsStore().addTab({
    title: vo.name,
    label: '编辑方案',
    path: `/program/operate/edit/${vo.id}`,
    type: 'rany-research',
    closable: true,
    id: `program/operate/edit/${vo.id}`
  });
  router.push(`/program/operate/edit/${vo.id}`);
}

const handleAdd = async () => {
  handleOpenCreateModal();
};

// 处理创建模态框确认事件
async function handleCreateConfirmed(selectedItem: any) {
  if (selectedItem.type === 'blank') {
    // 新建空白方案
    await store.restModel();
    const programId = await store.addProgram();
    if (programId) {
      await edit({ id: programId });
    }
  } else {
    // 复制现有方案
    const copiedProgramId = await store.copyProgram({ solutionId: selectedItem.id });
    if (copiedProgramId) {
      await edit({ id: copiedProgramId });
    }
    // 刷新列表
    getDataByPage(searchParams.pageNo);
  }
  // 更新标签的名称
  setTimeout(() => {
    tabsStore.updateTabLabel(tabsStore.activeTab, programModal.value.name);
  }, 500);
}

async function handleCopyProgram(id?: string) {
  if (id) {
    copyLoadingId.value = id;
    try {
      await store.copyProgram({ solutionId: id });
      getDataByPage(searchParams.pageNo);
    } finally {
      copyLoadingId.value = '';
    }
  }
}

async function handleUpdateStatus(id?: string, status?: number) {
  if (id) {
    store.programModal.id = id;
    store.programModal.status = status;
    await store.updateProgramStatus();
    getDataByPage(searchParams.pageNo);
  }
}

async function handleDelete(id?: string) {
  if (id) {
    await store.deleteProgram(id);
    getDataByPage(searchParams.pageNo);
  }
}

const dialog = useDialog();
function handleConfirm(id?: string, status?: number) {
  if (status === Status.disabled) {
    dialog.warning({
      title: '停用',
      content: () => (
        <div class="text-center">
          <h1 class="font-700">您确认要停用吗?</h1>
          <h1 class="mt-1 text-xs">停用后不可使用该方案，历史数据可以查询！</h1>
        </div>
      ),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleUpdateStatus(id, status)
    });
  }
  if (status === Status.published) {
    dialog.warning({
      title: '发布',
      content: () => (
        <div class="text-center">
          <h1 class="font-700">是否确认发布？</h1>
        </div>
      ),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleUpdateStatus(id, status)
    });
  }
  if (status === Status.deleted) {
    dialog.warning({
      title: '删除',
      content: () => (
        <div class="text-center">
          <h1 class="font-700">您确认要删除吗?</h1>
          <h1 class="mt-1 text-xs">删除后记录不可查询！</h1>
        </div>
      ),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleDelete(id)
    });
  }
}

const modalRef = ref<InstanceType<typeof ProgramModal>>();
function handleOpenProgramModal(vo: Api.Program.ProgramItem) {
  store.programModal = vo;
  modalRef.value?.show();
}

const createModalRef = ref<InstanceType<typeof CreateModal>>();
function handleOpenCreateModal() {
  createModalRef.value?.show();
}

const handleSubmitted = () => {
  getDataByPage(searchParams.pageNo);
};

// 处理搜索事件
function handleSearch(value: string) {
  searchParams.name = value || '';
  getDataByPage();
}

// 处理筛选变化事件
function handleFilterChange(filterParams: any) {
  Object.assign(searchParams, filterParams);
  getDataByPage();
}
</script>

<template>
  <div class="flex-col-stretch flex-1 overflow-hidden lt-sm:overflow-auto">
    <ProgramModal ref="modalRef" @submitted="handleSubmitted" />
    <CreateModal ref="createModalRef" :data="data" @confirmed="handleCreateConfirmed" />
    <NCard
      :bordered="false"
      size="small"
      class="data-common-table sm:flex-1-hidden"
      content-style="padding:16px 24px;display:flex;flex-direction:column; "
    >
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :disabled-delete="true"
        :loading="loading"
        search-placeholder="请输入方案名称"
        @add="handleAdd"
        @refresh="getData"
        @search="handleSearch"
      >
        <template #filters>
          <LibrarySearch :search-params="searchParams" @filter-change="handleFilterChange" />
        </template>
      </TableHeaderOperation>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <LibraryOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
