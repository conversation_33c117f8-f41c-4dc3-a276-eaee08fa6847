<script setup lang="ts">
import { defineEmits, defineProps, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { NButton, NForm, NFormItem, NInput, NModal, NSpace } from 'naive-ui';
import { useTabsStore } from 'component-library';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useProgramStore } from '@/store/modules/program';
import DiseaseSelect from '@/components/common/disease-select.vue';
import RiskCascader from '@/components/common/risk-cascader.vue';

interface Props {
  show: boolean;
}

interface Emits {
  (e: 'update:show', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const tabsStore = useTabsStore();

const { formRef, validate, restoreValidation } = useNaiveForm();

const store = useProgramStore();
const { programModal } = storeToRefs(store);

const { defaultRequiredRule } = useFormRules();

const riskIds = ref<string[]>([]);

// 备份原始数据，用于取消时恢复
const originalData = ref<any>({});

type RuleKey = Extract<Api.Program.ProgramItem, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule[]> = {
  name: [defaultRequiredRule]
};

function handleRiskChange(newVal: string | string[] | undefined) {
  riskIds.value = newVal as string[];
  programModal.value.riskId = riskIds.value.join(',');
}

// 监听programModal的变化，初始化riskIds
watch(
  () => programModal.value.riskId,
  newRiskId => {
    if (newRiskId) {
      riskIds.value = newRiskId.split(',');
    } else {
      riskIds.value = [];
    }
  },
  { immediate: true }
);

// 监听弹窗显示状态，备份数据和重置表单验证
watch(
  () => props.show,
  newShow => {
    if (newShow) {
      // 弹窗打开时备份当前数据
      originalData.value = JSON.parse(JSON.stringify(programModal.value));
      restoreValidation();
    }
  }
);

// 处理取消操作
const handleCancel = () => {
  // 恢复原始数据
  Object.assign(programModal.value, originalData.value);
  // 重置riskIds
  if (originalData.value.riskId) {
    riskIds.value = originalData.value.riskId.split(',');
  } else {
    riskIds.value = [];
  }
  // 清除表单验证
  restoreValidation();
  // 关闭弹窗
  emit('update:show', false);
};

// 处理确认操作
const handleConfirm = async () => {
  try {
    await validate();
    // 验证通过，保存数据
    await store.updateProgram(false);
    await emit('update:show', false);
    // 更新标签的名称
    tabsStore.updateTabLabel(tabsStore.activeTab, programModal.value.name);
  } catch {
    // 验证失败，不关闭弹窗
    console.log('表单验证失败');
  }
};
</script>

<template>
  <NModal
    :show="show"
    preset="card"
    title="基础信息"
    size="medium"
    :mask-closable="false"
    class="w-[400px]"
    @update:show="emit('update:show', $event)"
  >
    <template #header>
      <div class="text-lg font-medium">基础信息</div>
    </template>

    <NForm ref="formRef" :model="programModal" :rules="rules" label-placement="top">
      <NFormItem path="name" label="名称" required>
        <NInput v-model:value="programModal.name" size="medium" placeholder="请输入方案名称" />
      </NFormItem>
      <NFormItem path="gender" label="疾病分型">
        <DiseaseSelect v-model:value="programModal.diseaseId" size="medium" />
      </NFormItem>
      <NFormItem path="riskId" label="危险度分型">
        <RiskCascader v-model:value="riskIds" size="medium" :multiple="true" @update:value="handleRiskChange" />
      </NFormItem>
      <NFormItem path="comment" label-placement="top" label="注意事项">
        <NInput
          v-model:value="programModal.comment"
          placeholder="请输入注意事项"
          class="w-full"
          type="textarea"
          :rows="4"
        />
      </NFormItem>
    </NForm>

    <template #footer>
      <NSpace justify="end">
        <NButton @click="handleCancel">取消</NButton>
        <NButton type="primary" @click="handleConfirm">确认</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
