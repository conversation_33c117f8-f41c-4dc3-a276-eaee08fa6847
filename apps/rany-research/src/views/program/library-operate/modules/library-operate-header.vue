<script setup lang="ts">
import { NButton, NIcon, NTag, useDialog } from 'naive-ui';
import { h, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useRouter } from 'vue-router';
import { useTabsStore } from 'component-library';
import { useProgramStore } from '@/store/modules/program';
import BaseModal from './base-modal.vue';

const tabsStore = useTabsStore();
const store = useProgramStore();
const { programModal, statusOptions, Status } = storeToRefs(store);
const router = useRouter();
const dialog = useDialog();

const props = defineProps<{
  operateMode: 'show' | 'edit';
}>();

// 弹窗控制
const showBaseModal = ref(false);
const copyLoading = ref(false);

// 状态颜色映射
const statusColorMap: Record<number, 'warning' | 'success' | 'error'> = {
  0: 'warning', // 草稿
  1: 'success', // 已发布
  2: 'error' // 已停用
};

const handleCancel = async () => {
  const tabId = JSON.parse(localStorage.getItem('tabs') || '{}').activeTab;
  await tabsStore.closeTab(tabId);
  const tabs = JSON.parse(localStorage.getItem('tabs') || '{}');
  if (tabs.tabs.length > 0) {
    router.push(tabs.tabs[tabs.tabs.length - 1].path);
  }
};

const handleSave = async () => {
  handleUpdateStatus(Status.value.drafted);
};

async function handleUpdateStatus(status?: number) {
  if (programModal.value.id) {
    programModal.value.status = status;
    await store.updateProgram(false);
    handleCancel();
  }
}

const handlePublish = () => {
  handleUpdateStatus(Status.value.published);
};

// 停用/发布操作
const handleToggleStatus = () => {
  if (programModal.value.status === Status.value.published) {
    // 当前是已发布状态，执行停用操作
    dialog.warning({
      title: '停用',
      content: () =>
        h('div', { class: 'text-center' }, [
          h('h1', { class: 'font-700' }, '您确认要停用吗?'),
          h('h1', { class: 'mt-1 text-xs' }, '停用后不可使用该方案，历史数据可以查询！')
        ]),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleUpdateStatus(Status.value.disabled)
    });
  } else {
    // 其他状态执行发布操作
    dialog.warning({
      title: '发布',
      content: () => h('div', { class: 'text-center' }, [h('h1', { class: 'font-700' }, '是否确认发布？')]),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleUpdateStatus(Status.value.published)
    });
  }
};

// 删除操作
const handleDelete = () => {
  dialog.warning({
    title: '删除',
    content: () =>
      h('div', { class: 'text-center' }, [
        h('h1', { class: 'font-700' }, '您确认要删除吗?'),
        h('h1', { class: 'mt-1 text-xs' }, '删除后记录不可查询！')
      ]),
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      if (programModal.value.id) {
        await store.deleteProgram(programModal.value.id);
        handleCancel();
      }
    }
  });
};

// 编辑操作
const handleEdit = () => {
  if (programModal.value.id) {
    tabsStore.addTab({
      title: programModal.value.name,
      label: '编辑方案',
      path: `/program/operate/edit/${programModal.value.id}`,
      type: 'rany-research',
      closable: true,
      id: `program/operate/edit/${programModal.value.id}`
    });
    router.push(`/program/operate/edit/${programModal.value.id}`);
  }
};

// 复制操作
const handleCopy = async () => {
  if (programModal.value.id) {
    copyLoading.value = true;
    try {
      const copiedProgramId = await store.copyProgram({ solutionId: programModal.value.id });
      if (copiedProgramId) {
        // 跳转到编辑页面
        tabsStore.addTab({
          title: `${programModal.value.name}(副本)`,
          label: '编辑方案',
          path: `/program/operate/edit/${copiedProgramId}`,
          type: 'rany-research',
          closable: true,
          id: `program/operate/edit/${copiedProgramId}`
        });
        router.push(`/program/operate/edit/${copiedProgramId}`);
      }
    } finally {
      copyLoading.value = false;
    }
  }
};
</script>

<template>
  <!-- 主标题区域 -->
  <div class="flex items-center justify-between">
    <!-- 左侧标题和状态 -->
    <div class="flex items-center space-x-6">
      <!-- 方案名称和编辑按钮 -->
      <div class="flex items-center space-x-2">
        <span class="text-lg text-gray-900 font-medium">{{ programModal?.name || '未命名方案' }}</span>
        <NIcon
          size="16"
          class="cursor-pointer text-gray-600 transition-colors hover:text-blue-600"
          @click="showBaseModal = true"
        >
          <icon-local-write />
        </NIcon>
      </div>
      <div v-if="props.operateMode === 'show'" class="flex items-center space-x-3">
        <span class="text-sm text-gray-500">状态:</span>
        <NTag :type="statusColorMap[programModal?.status ?? 0]" size="small" class="px-3 py-1 font-medium">
          {{ statusOptions.find(item => item.value === programModal?.status)?.label }}
        </NTag>
      </div>
      <!-- 时间信息 -->

      <div v-if="operateMode === 'show'" class="flex items-center text-sm text-gray-600 space-x-6">
        <div class="flex items-center space-x-2">
          <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6l4 2"></path>
            <circle cx="12" cy="12" r="10"></circle>
          </svg>
          <span class="font-medium">创建时间:</span>
          <span class="text-[#7a7a7a]">{{ programModal.createTime }}</span>
        </div>
        <div class="h-4 w-px bg-gray-300"></div>
        <div class="flex items-center space-x-2">
          <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
            ></path>
          </svg>
          <span class="font-medium">更新时间:</span>
          <span class="text-[#7a7a7a]">{{ programModal.updateTime }}</span>
        </div>
      </div>
    </div>

    <!-- 右侧操作信息 -->
    <div class="flex items-center space-x-8">
      <!-- 快捷操作按钮 -->
      <div class="flex items-center space-x-2">
        <!-- show模式下的按钮 -->
        <template v-if="operateMode === 'show'">
          <!-- 停用/发布按钮 -->
          <NButton
            v-if="programModal?.status === Status.published"
            size="small"
            type="warning"
            ghost
            @click="handleToggleStatus"
          >
            停用
          </NButton>
          <NButton
            v-else-if="programModal?.status !== Status.published"
            size="small"
            type="success"
            ghost
            @click="handleToggleStatus"
          >
            发布
          </NButton>

          <!-- 删除按钮 -->
          <NButton size="small" type="error" ghost @click="handleDelete">删除</NButton>

          <!-- 编辑按钮 -->
          <NButton size="small" type="primary" ghost @click="handleEdit">编辑</NButton>

          <!-- 复制按钮 -->
          <NButton size="small" type="info" ghost :loading="copyLoading" @click="handleCopy">复制</NButton>
        </template>

        <!-- edit模式下的按钮 -->
        <template v-else>
          <NButton size="small" type="info" ghost @click="handleSave">保存</NButton>
          <NButton size="small" color="#0085FF" text-color="#fff" @click="handlePublish">发布</NButton>
        </template>
      </div>
    </div>
  </div>

  <!-- 基础信息弹窗 -->
  <BaseModal v-model:show="showBaseModal" />
</template>

<style scoped></style>
