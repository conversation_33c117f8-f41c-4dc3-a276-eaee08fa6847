<script setup lang="ts">
import { useItemStore } from '@/store/modules/program/item';
import { useProgramStore } from '@/store/modules/program';

const itemStore = useItemStore();
const programStore = useProgramStore();

const { comments } = storeToRefs(itemStore);
const { programModal } = storeToRefs(programStore);
</script>

<template>
  <div class="p-4">
    <header class="mb-4 flex justify-between">
      <h1 class="text-base text-black font-400">注意事项</h1>
    </header>
    <div>
      <h2>项目注释</h2>
      <NCard size="medium" class="mt-2" content-style="padding: 0;min-height: 200px;">
        <div class="px-2 py-1">
          <div class="text-sm text-gray-500">
            <NScrollbar height="200px">
              <div class="h-200px flex flex-col gap-1">
                <span v-for="(comment, index) in comments" :key="comment">{{ index + 1 }}. {{ comment }}</span>
              </div>
            </NScrollbar>
          </div>
        </div>
      </NCard>
      <h2 class="mt-4">方案注意事项</h2>
      <NCard class="mt-2" content-style="padding: 0;min-height: 200px;">
        <div class="px-2 py-1">
          <NScrollbar height="200px">
            <div class="h-200px">
              <div class="whitespace-pre-wrap text-sm text-gray-500">{{ programModal.comment }}</div>
            </div>
          </NScrollbar>
        </div>
      </NCard>
    </div>
  </div>
</template>

<style scoped></style>
