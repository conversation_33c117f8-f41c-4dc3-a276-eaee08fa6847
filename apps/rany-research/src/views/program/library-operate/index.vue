<script setup lang="ts">
import { computed, onMounted, onUnmounted } from 'vue';
import { useRoute } from 'vue-router';
import { useProgramStore } from '@/store/modules/program';
import { useItemStore } from '@/store/modules/program/item';
import { ProgramDataSource } from '@/store/modules/program/programDataSource';
import LibraryOperateHeader from './modules/library-operate-header.vue';
import LibraryOperateSetting from './modules/library-operate-setting.vue';
import LibraryOperateRemark from './modules/library-operate-remark.vue';

const route = useRoute();
const store = useProgramStore();

// 获取操作模式 (show | edit)
const operateMode = computed(() => route.params.action as 'show' | 'edit');

onMounted(() => {
  // 初始化数据
  if (route.params.id) {
    store.getProgramDetail(route.params.id as string);
  }
});
const itemStore = useItemStore();
watchEffect(async () => {
  if (store.programModal.id) {
    itemStore.dataSource = new ProgramDataSource();
  }
});

onUnmounted(() => {
  store.restModel();
});
</script>

<template>
  <div class="flex flex-col flex-1 overflow-hidden lt-sm:overflow-auto">
    <!-- 头部区域 -->
    <div class="mb-1 bg-white px-6 py-4 shadow-[2px_2px_10px_0px_rgba(160,209,255,0.15)]">
      <LibraryOperateHeader :operate-mode="operateMode" />
    </div>

    <!-- 主体内容区域 -->
    <div class="flex sm:flex-1-hidden">
      <div class="w-1/10">
        <!-- <LibraryOperateBaseInfo /> -->
      </div>
      <!-- 方案设置 -->
      <LibraryOperateSetting :module-id="route.params.id as string" :operate-mode="operateMode" />

      <!-- 右侧注意事项 -->
      <div class="w-1/4 shadow-[2px_2px_12px_0px_rgba(160,209,255,0.15)]">
        <LibraryOperateRemark />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
