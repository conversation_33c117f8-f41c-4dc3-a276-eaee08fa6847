<script setup lang="tsx">
import { NButton, NDropdown, NTag, useDialog } from 'naive-ui';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useTabsStore } from 'component-library';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { tableColumnArrayStringRender } from '@/utils/table';
import { fetchPostClinicList } from '@/service/api/clinicPlan';
import { usePlanStore } from '@/store/modules/plan';
import { useProgramStore } from '@/store/modules/program';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import OperateDrawer from './modules/operate-drawer.vue';
import ProgramModal from './modules/plan-modal.vue';
import AddItemModal from './modules/add-item-modal.vue';
import Search from './modules/plan-search.vue';
import PlanCreateModal from './modules/plan-create-modal.vue';

const appStore = useAppStore();
const store = usePlanStore();
const { statusLabel, Status } = useProgramStore();
const router = useRouter();
const copyLoadingId = ref<string>('');

const handleFlowChartEdit = async (row: Api.ClinicPlan.Record) => {
  await useTabsStore().addTab({
    id: row.id,
    title: row.name,
    label: row.name,
    path: `/program/plan_operate/edit/${row.id}`,
    closable: true,
    key: row.id,
    type: 'rany-research'
  });
  router.push({
    name: 'program_plan-operate',
    params: {
      action: 'edit',
      id: row.id
    }
  });
};

const getOperationOptions = (row: any) => {
  const options = [
    {
      label: '删除',
      key: 'delete',
      props: {
        onClick: () => handleConfirm(row.id, Status.deleted)
      }
    },
    {
      label: '复制',
      key: 'copy',
      props: {
        onClick: () => handleCopy(row.id)
      }
    }
  ];

  // Only show publish/disable option when status is disabled or published
  if (row.status === Status.disabled || row.status === Status.published) {
    options.splice(1, 0, {
      label: row.status === Status.disabled ? '发布' : '停用',
      key: 'status',
      props: {
        onClick: () => handleConfirm(row.id, row.status === Status.disabled ? Status.published : Status.disabled)
      }
    });
  }

  return options;
};

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination, searchParams } = useTable({
  apiFn: fetchPostClinicList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    name: '',
    userName: '',
    userId: '',
    restriction: 1,
    status: undefined
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      minWidth: 64
    },
    {
      title: '诊疗计划名称',
      ellipsis: true,
      maxWidth: 500,
      resizable: true,
      key: 'name',
      render: row => (
        <n-button
          text
          class="text-primary"
          onClick={() => {
            useTabsStore().addTab({
              title: row.name,
              path: `/program/plan_operate/show/${row.id}`,
              type: 'rany-research',
              closable: true,
              label: row.name,
              id: row.id
            });
            router.push({
              name: 'program_plan-operate',
              params: {
                action: 'show',
                id: row.id || ''
              }
            });
          }}
        >
          {row.name}
        </n-button>
      )
    },
    {
      align: 'center',
      title: '计划周数',
      key: 'weekCount'
    },
    {
      title: '危险度分型',
      key: 'riskName',
      render: tableColumnArrayStringRender('riskName')
    },
    {
      title: '疾病分型',
      key: 'diseaseName',
      ellipsis: true
    },
    {
      title: '关联临床实验',
      key: 'projectName',
      ellipsis: true
    },
    {
      title: '创建时间',
      key: 'createTime',
      maxWidth: 120,
      ellipsis: true
    },
    {
      title: '更新时间',
      key: 'updateTime',
      maxWidth: 120,
      ellipsis: true
    },
    {
      title: '状态',
      key: 'status',
      align: 'center',
      render: row => (
        <NTag bordered={false} type="info">
          {statusLabel(row.status)}
        </NTag>
      )
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 280,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleOpenProgramModal(row)}>
            计划
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => handleAddItem(row)}>
            项目
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => handleFlowChartEdit(row)}>
            编辑
          </NButton>
          <NDropdown options={getOperationOptions(row)}>
            <NButton type="primary" ghost size="small">
              ...
            </NButton>
          </NDropdown>
        </div>
      )
    }
  ]
});
const { drawerVisible, operateType, editingData } = useTableOperate(data, getData);

// 处理新增按钮点击
const handleAdd = async () => {
  handleOpenCreateModal();
};

// 处理创建模态框确认事件
async function handleCreateConfirmed(selectedItem: any) {
  if (selectedItem.type === 'blank') {
    // 新建空白计划
    store.$reset();
    store.formModel.name = '未命名计划';
    const planId = await store.addPlan();
    if (planId) {
      // 构造新建的计划对象并跳转到编辑页面
      await useTabsStore().addTab({
        id: String(planId),
        title: '未命名计划',
        label: '未命名计划',
        path: `/program/plan_operate/edit/${planId}`,
        closable: true,
        key: String(planId),
        type: 'rany-research'
      });
      router.push({
        name: 'program_plan-operate',
        params: {
          action: 'edit',
          id: String(planId)
        }
      });
    }
  } else {
    // 复制现有计划
    await store.copyPlan(selectedItem.id);
    // 重新获取数据
    await getDataByPage();
    // 获取最新的计划（复制后的计划）
    if (data.value && data.value.length > 0) {
      const copiedPlan = data.value[0];
      handleFlowChartEdit(copiedPlan);
    }
  }
}

async function handleCopy(id?: string) {
  if (id) {
    copyLoadingId.value = id;
    try {
      await store.copyPlan(id);
      getDataByPage();
    } finally {
      copyLoadingId.value = '';
    }
  }
}

async function handleUpdateStatus(id?: string, status?: number) {
  if (id) {
    store.formModel.id = id;
    store.formModel.status = status;
    await store.updatePlanStatus();
    getDataByPage();
  }
}

async function handleDelete(id?: string) {
  if (id) {
    await store.deletePlan(id);
    getDataByPage();
  }
}

const dialog = useDialog();
function handleConfirm(id?: string, status?: number) {
  if (status === Status.disabled) {
    dialog.warning({
      title: '停用',
      content: () => (
        <div class="text-center">
          <h1 class="font-700">您确认要停用吗?</h1>
          <h1 class="mt-1 text-xs">停用后不可使用该计划，历史数据可以查询！</h1>
        </div>
      ),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleUpdateStatus(id, status)
    });
  }
  if (status === Status.published) {
    dialog.warning({
      title: '发布',
      content: () => (
        <div class="text-center">
          <h1 class="font-700">是否确认发布？</h1>
        </div>
      ),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleUpdateStatus(id, status)
    });
  }
  if (status === Status.deleted) {
    dialog.warning({
      title: '删除',
      content: () => (
        <div class="text-center">
          <h1 class="font-700">您确认要删除吗?</h1>
          <h1 class="mt-1 text-xs">删除后记录不可查询！</h1>
        </div>
      ),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleDelete(id)
    });
  }
}

const modalRef = ref<InstanceType<typeof ProgramModal>>();
function handleOpenProgramModal(vo: Api.ClinicPlan.Record) {
  store.formModel = vo;
  modalRef.value?.show();
}

const moduleId = ref<string | undefined>('');
const addItemModalRef = ref<InstanceType<typeof AddItemModal>>();
function handleAddItem(row: Api.ClinicPlan.Record) {
  moduleId.value = row.id;
  addItemModalRef.value?.show();
}

const createModalRef = ref<InstanceType<typeof PlanCreateModal>>();
function handleOpenCreateModal() {
  createModalRef.value?.show();
}

// 处理搜索事件
function handleSearch(value: string) {
  searchParams.name = value || '';
  getDataByPage();
}

// 处理筛选变化事件
function handleFilterChange(filterParams: any) {
  Object.assign(searchParams, filterParams);
  getDataByPage();
}
</script>

<template>
  <div class="flex-col-stretch flex-1 overflow-hidden lt-sm:overflow-auto">
    <ProgramModal ref="modalRef" />
    <AddItemModal ref="addItemModalRef" :module-id="moduleId" />
    <PlanCreateModal ref="createModalRef" :data="data" @confirmed="handleCreateConfirmed" />
    <NCard
      :bordered="false"
      size="small"
      class="data-common-table sm:flex-1-hidden"
      content-style="padding:16px 24px;display:flex;flex-direction:column;"
    >
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :disabled-delete="true"
        :loading="loading"
        search-placeholder="请输入计划名称"
        @add="handleAdd"
        @refresh="getData"
        @search="handleSearch"
      >
        <template #filters>
          <Search :search-params="searchParams" @filter-change="handleFilterChange" />
        </template>
      </TableHeaderOperation>
      <NDataTable
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
