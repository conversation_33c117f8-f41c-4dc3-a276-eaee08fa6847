<script setup lang="ts">
import { ref } from 'vue';
import { fetchPostClinicStageAddProgramOrRest } from '@/service/api/clinicPlan';
import { useMessage } from 'naive-ui';

const emit = defineEmits<{
  (e: 'confirm', restData: { days: number; stageId: string ;stageSoluId:string}): void;
}>();

const showModal = ref(false);
const days = ref(1);
const loading = ref(false);
const currentStageId = ref<string>('');
const orderNumber = ref(0);
const message = useMessage();

const handleCancel = () => {
  showModal.value = false;
  days.value = 1;
  currentStageId.value = '';
};

const handleConfirm = async () => {
  if (!currentStageId.value) {
    message.error('阶段ID不能为空');
    return;
  }

  loading.value = true;
  try {
    // 调用API添加休疗
    const { error,data } = await  fetchPostClinicStageAddProgramOrRest({
      stageId: currentStageId.value,
      type: 2, // 休疗类型
      relation: days.value.toString(),
      orderNumber: orderNumber.value // 使用传入的排序号
    });

    if (!error) {
      message.success('休疗添加成功');
      emit('confirm', { days: days.value, stageId: currentStageId.value ,stageSoluId:data});
      handleCancel();
    }
  } catch (error) {
    console.error('添加休疗失败:', error);
    message.error('添加休疗失败');
  } finally {
    loading.value = false;
  }
};

defineExpose({
  show: (stageId: string, orderNum: number) => {
    currentStageId.value = stageId;
    orderNumber.value = orderNum;
    showModal.value = true;
  }
});
</script>

<template>
  <NModal v-model:show="showModal" title="添加休疗" class="w-2xl" size="small" preset="card" @close="handleCancel">
    <NInputNumber v-model:value="days" placeholder="输入天数" class="p-4" />
    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" :disabled="loading" @click="handleCancel">取消</NButton>
      <NButton type="primary" :loading="loading" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>

<style scoped>
/* Add any necessary styles here */
</style>
