<script setup lang="tsx">
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { type DataTableColumn } from 'naive-ui';
import { useSuiteStore } from '@/store/modules/database/suite';
import { useItemStore } from '@/store/modules/program/item';
import ItemModal from '@/components/advanced/program-table/item-value/item-modal.vue';

const itemStore = useItemStore();

const props = defineProps<{
  moduleId: string | undefined;
}>();

const suiteStore = useSuiteStore();
const { suiteItemList } = storeToRefs(suiteStore);

const handleDeleteRowItem = (id: string) => {
  window.$dialog?.warning({
    title: '删除方案项',
    content: () => (
      <div class="text-center">
        <h1 class="font-700">确定要删除吗?</h1>
        <h1 class="mt-1 text-xs">删除后不可恢复！</h1>
      </div>
    ),
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      await suiteStore.deleteSuiteItem(id);
      loadTableData();
    }
  });
};

const itemModalRef = ref<InstanceType<typeof ItemModal>>();

const handleEditRowItem = async (id: string) => {
  // store.programModal.id = id;
  await itemStore.getProgramItemDetailOfAll(id);
  itemModalRef.value?.show();
};

const tableColumns: DataTableColumn<Api.Suite.SuiteItem>[] = [
  {
    title: '名称',
    key: 'name',
    width: 200
  },
  {
    title: '方案概要',
    key: 'comment',
    width: 200
  },
  {
    title: '操作',
    key: 'delete',
    align: 'center',
    width: 60,
    render: row => (
      <div class="flex justify-center gap-2">
        <n-button class="p-0" quaternary circle type="info" onClick={() => handleEditRowItem(row.id!)}>
          {{
            icon: () => (
              <n-icon>
                <icon-ant-design-edit-outlined class="p-0 text-icon color-coolGray" />
              </n-icon>
            )
          }}
        </n-button>
        <n-button class="p-0" quaternary circle type="error" onClick={() => handleDeleteRowItem(row.id!)}>
          {{
            icon: () => (
              <n-icon>
                <icon-ant-design-close-outlined class="p-0 text-icon color-coolGray" />
              </n-icon>
            )
          }}
        </n-button>
      </div>
    )
  }
];

function handleAddItem() {
  itemModalRef.value?.show();
}

function loadTableData() {
  if (props.moduleId) {
    suiteStore.getSuiteItemList(props.moduleId);
  }
}

watch(
  () => props.moduleId,
  () => {
    loadTableData();
  },
  { immediate: true }
);
</script>

<template>
  <div>
    <ItemModal ref="itemModalRef" :module-id="props.moduleId" @submitted="loadTableData()" />
    <NFlex class="mb-2">
      <NButton class="mb-2" type="primary" @click="handleAddItem">添加项目</NButton>
    </NFlex>
    <NDataTable :data="suiteItemList" :columns="tableColumns" min-height="500px" />
  </div>
</template>
