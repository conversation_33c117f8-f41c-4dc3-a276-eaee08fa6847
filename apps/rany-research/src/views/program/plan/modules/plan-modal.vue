<script setup lang="tsx">
import { computed, ref } from 'vue';
import { NButton, NSpin } from 'naive-ui';
import { storeToRefs } from 'pinia';
import { usePlanStore } from '@/store/modules/plan';
import { useProgramStore } from '@/store/modules/program';
import PlanTabs from './plan-tabs.vue';

const visible = ref(false);
const planStore = usePlanStore();
const planTabsRef = ref<InstanceType<typeof PlanTabs>>();

const { Status } = useProgramStore();
const { loading } = storeToRefs(planStore);

const handleCancel = () => {
  visible.value = false;
};

const handleConfirm = async () => {
  visible.value = false;
};
const handleConfirmAndUse = async () => {
  visible.value = false;
  planStore.formModel.status = Status.published;
  await planStore.updatePlanStatus();
};

// Add computed property for button disabled state
const saveAndUseDisabled = computed(() => {
  return !planTabsRef.value?.panels.length;
});

defineExpose({
  show: () => {
    visible.value = true;
  }
});
</script>

<template>
  <NModal v-model:show="visible" size="small" class="mx-2" title="计划阶段" :mask-closable="false" preset="card">
    <NSpin :show="loading">
      <div class="py-2">
        <PlanTabs ref="planTabsRef" />
      </div>
      <template #description>加载中...</template>
    </NSpin>

    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" @click="handleCancel">取消</NButton>
      <NButton type="primary" class="w-24" @click="handleConfirm">确定</NButton>
      <NButton type="primary" class="w-24" :disabled="saveAndUseDisabled" @click="handleConfirmAndUse">
        保存并启用
      </NButton>
    </div>
  </NModal>
</template>

<style lang="scss" scoped>
.n-spin-container {
  min-height: 200px;
}
</style>
