<script setup lang="ts">
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useProgramStore } from '@/store/modules/program';
import { useStageStore } from '@/store/modules/plan/stage';

interface Emits {
  (e: 'submitted'): void;
}
const emit = defineEmits<Emits>();

const programStore = useProgramStore();
const stageStore = useStageStore();
const { programModal: model } = storeToRefs(programStore);
const { currentStageTab } = storeToRefs(stageStore);

const visible = ref(false);
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const riskIds = ref<string[]>([]);

type RuleKey = Extract<Api.Program.ProgramItem, 'name'>;

const rules: Record<RuleKey, App.Global.FormRule[]> = {
  name: [defaultRequiredRule]
};

async function handleInitModel() {
  programStore.restModel();
  riskIds.value = [];
  if (currentStageTab.value?.id) {
    model.value.stageId = currentStageTab.value.id;
  }
}

function handleClose() {
  visible.value = false;
  riskIds.value = [];
  programStore.restModel();
}

function handleRiskChange(newVal: string | string[] | undefined) {
  riskIds.value = newVal as string[];
  model.value.riskId = riskIds.value.join(',');
}

async function handleSubmit() {
  await validate();
  model.value.restriction = 0;
  await programStore.addProgram();
  handleClose();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

defineExpose({
  show: () => {
    visible.value = true;
  }
});
</script>

<template>
  <NModal v-model:show="visible" title="新建方案" class="w-xl" size="small" preset="card" @close="handleClose">
    <NForm ref="formRef" :model="model" :rules="rules" label-placement="left" label-width="80">
      <NFormItem path="name" label="名称" required>
        <NInput v-model:value="model.name" placeholder="请输入方案名称" />
      </NFormItem>
      <NFormItem path="gender" label="疾病分型">
        <DiseaseSelect v-model:value="model.diseaseId" />
      </NFormItem>
      <NFormItem path="riskId" label="危险度分型">
        <RiskCascader v-model:value="riskIds" :multiple="true" @update:value="handleRiskChange" />
      </NFormItem>
      <NFormItem path="comment" label="注意事项">
        <NInput
          v-model:value="model.comment"
          placeholder="请输入注意事项"
          class="w-full"
          type="textarea"
          :autosize="{
            minRows: 3
          }"
        />
      </NFormItem>
    </NForm>
    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" @click="handleClose">取消</NButton>
      <NButton type="primary" class="w-24" @click="handleSubmit">确定</NButton>
    </div>
  </NModal>
</template>

<style scoped></style>
