<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { usePlanStore } from '@/store/modules/plan';
import { useProgramStore } from '@/store/modules/program';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.ClinicPlan.ClinicPlanItem | null;
}
const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}
const emit = defineEmits<Emits>();

const store = usePlanStore();
const { formModel: model, loading } = storeToRefs(store);

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新建计划',
    edit: '编辑计划'
  };
  return titles[props.operateType];
});
const riskIds = ref<string[]>([]);

type RuleKey = Extract<Api.Program.ProgramItem, 'name' | 'diseaseId'>;

const rules: Record<RuleKey, App.Global.FormRule[]> = {
  name: [defaultRequiredRule],
  diseaseId: [defaultRequiredRule]
};

async function handleInitModel() {
  store.$reset();
  if (props.operateType === 'edit' && props.rowData) {
    await store.getDetail(props.rowData?.id);
    riskIds.value = model.value.riskId?.split(',') || [];
  }
}
function closeModal() {
  visible.value = false;
  store.$reset();
}

const { Status, statusLabel } = useProgramStore();

async function handleSubmit(saveAndEnable?: boolean) {
  await validate();
  if (saveAndEnable) {
    store.formModel.status = Status.published;
  }
  if (props.operateType === 'edit') {
    //更新头部区域的title
    store.updatePlanName(model.value.name);
    await store.updatePlan();
  }

  if (props.operateType === 'add') {
    await store.addPlan();
  }

  closeModal();
  emit('submitted');
}

function handleRiskChange(newVal: string | string[] | undefined) {
  riskIds.value = newVal as string[];
  model.value.riskId = riskIds.value.join(',');
}

watch(visible, () => {
  riskIds.value = [];
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NModal v-model:show="visible" preset="dialog" :title="title" class="w-[480px]">
    <div class="px-2">
      <NForm ref="formRef" :model="model" :rules="rules" label-placement="top">
        <h2 class="font-700 mb-2">基础信息</h2>
        <NFormItem path="name" label="名称" required>
          <NInput v-model:value="model.name" placeholder="请输入方案名称" />
        </NFormItem>
        <NFormItem path="diseaseId" label="疾病分型">
          <DiseaseSelect v-model:value="model.diseaseId" />
        </NFormItem>
        <NFormItem path="riskId" label="危险度分型">
          <RiskCascader v-model:value="riskIds" :multiple="true" @update:value="handleRiskChange" />
        </NFormItem>
        <!--
 <NFormItem path="riskId" label="关联临床实验">
          <ExperimentSelect v-model:value="model.projectId" />
        </NFormItem>
-->
        <!-- 编辑时显示方案状态和创建时间以及更新时间的文字 -->
        <!-- <NFormItem v-if="props.operateType === 'edit'" path="status" label="方案状态">
          <NText class="ml-2">{{ statusLabel(model.status) }}</NText>
        </NFormItem>
        <NFormItem v-if="props.operateType === 'edit'" path="status" label="创建时间">
          <NText class="ml-2">{{ model.createTime }}</NText>
        </NFormItem>
        <NFormItem v-if="props.operateType === 'edit'" path="status" label="更新时间">
          <NText class="ml-2">{{ model.updateTime }}</NText>
        </NFormItem> -->
      </NForm>
    </div>

    <template #action>
      <NSpace :size="16">
        <NButton @click="closeModal">{{ $t('common.cancel') }}</NButton>
        <NButton :loading="loading" type="primary" @click="handleSubmit(false)">{{ $t('common.confirm') }}</NButton>
        <!-- <NButton :loading="loading" type="primary" @click="handleSubmit(true)">保存并启用</NButton> -->
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
