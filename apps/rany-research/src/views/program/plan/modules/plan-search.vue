<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import type { SelectOption } from 'naive-ui';
import { useProgramStore } from '@/store/modules/program';

interface Props {
  searchParams?: any;
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({
    pageNo: 1,
    pageSize: 10,
    name: '',
    userName: '',
    userId: '',
    restriction: 1,
    status: 1
  })
});

interface Emits {
  (e: 'filter-change', params: any): void;
}

const emit = defineEmits<Emits>();

const programStore = useProgramStore();

// 启用状态选择器选项
const statusOptions = ref<SelectOption[]>([]);
const selectedStatus = ref<string | null>(props.searchParams?.status || null);

// 危险度分型选择器选项
const selectedRiskId = ref<string | undefined>(props.searchParams?.riskId || undefined);

// 疾病分型选择器选项
const selectedDiseaseId = ref<string | null>(props.searchParams?.diseaseId || null);

// 初始化状态选项
function initStatusOptions() {
  statusOptions.value = programStore.statusOptions;
}

// 处理状态筛选变化
const handleStatusChange = (value: string | null) => {
  selectedStatus.value = value;
  emit('filter-change', { status: value });
};

// 处理危险度分型筛选变化
const handleRiskChange = (value: string | string[] | undefined) => {
  selectedRiskId.value = typeof value === 'string' ? value : undefined;
  emit('filter-change', { riskId: selectedRiskId.value });
};

// 处理疾病分型筛选变化
const handleDiseaseChange = (value: string | number | null | undefined) => {
  const diseaseId = value ? String(value) : null;
  selectedDiseaseId.value = diseaseId;
  emit('filter-change', { diseaseId });
};

// 监听外部搜索参数变化
watch(
  () => props.searchParams?.status,
  newValue => {
    selectedStatus.value = newValue || null;
  }
);

watch(
  () => props.searchParams?.riskId,
  newValue => {
    selectedRiskId.value = newValue || undefined;
  }
);

watch(
  () => props.searchParams?.diseaseId,
  newValue => {
    selectedDiseaseId.value = newValue || null;
  }
);

onMounted(() => {
  initStatusOptions();
});
</script>

<template>
  <div class="flex gap-16px">
    <div class="filter-item">
      <div class="filter-label">启用状态：</div>
      <div class="filter-control">
        <NSelect
          v-model:value="selectedStatus"
          class="filter-select"
          placeholder="请选择启用状态"
          :options="statusOptions"
          clearable
          @update:value="handleStatusChange"
        />
      </div>
    </div>

    <div class="filter-item">
      <div class="filter-label">危险度分型：</div>
      <div class="filter-control">
        <RiskCascader
          v-model:value="selectedRiskId"
          :multiple="false"
          class="filter-cascader"
          placeholder="请选择危险度分型"
          @update:value="handleRiskChange"
        />
      </div>
    </div>

    <div class="filter-item">
      <div class="filter-label">疾病分型：</div>
      <div class="filter-control">
        <DiseaseSelect
          v-model:value="selectedDiseaseId"
          class="filter-select"
          :multiple="false"
          placeholder="请选择疾病分型"
          @update:value="handleDiseaseChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.filter-item {
  display: flex;
  align-items: center;
  margin-right: 0px;
  width: fit-content;
}

.filter-label {
  white-space: nowrap;
  font-size: 14px;
  color: var(--text-color-2);
}

.filter-select {
  min-width: 120px;
}

.filter-cascader {
  min-width: 120px;
}
</style>
