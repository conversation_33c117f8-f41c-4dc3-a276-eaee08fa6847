<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NSpin } from 'naive-ui';
import { storeToRefs } from 'pinia';
import { usePlanStore } from '@/store/modules/plan';
import AddItemTable from './add-item-table.vue';

const props = defineProps<{
  moduleId: string | undefined;
}>();

const visible = ref(false);
const planStore = usePlanStore();
const { loading } = storeToRefs(planStore);

const handleCancel = () => {
  visible.value = false;
};

const handleConfirm = async () => {
  visible.value = false;
};

defineExpose({
  show: () => {
    visible.value = true;
  }
});
</script>

<template>
  <NModal v-model:show="visible" size="small" class="mx-2" title="整体项目设置" :mask-closable="false" preset="card">
    <NSpin :show="loading">
      <div class="py-2">
        <AddItemTable :module-id="props.moduleId" />
      </div>
      <template #description>加载中...</template>
    </NSpin>

    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" @click="handleCancel">取消</NButton>
      <NButton type="primary" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>

<style lang="scss" scoped>
.n-spin-container {
  min-height: 200px;
}
</style>
