<script setup lang="tsx">
import { storeToRefs } from 'pinia';
import { onMounted, onUnmounted, ref } from 'vue';
import { type DataTableColumn } from 'naive-ui';
import Sortable from 'sortablejs';
import type { SortableEvent } from 'sortablejs';
import { useStageStore } from '@/store/modules/plan/stage';
import { usePlanStore } from '@/store/modules/plan';
import PlanModalAddProgram from './plan-modal-add-program.vue';
import PlanModalAddRest from './plan-modal-add-rest.vue';
import CreateProgramModal from './add-program-modal.vue';

const store = useStageStore();
const { currentStageTab, loading, StageType } = storeToRefs(store);

const planStore = usePlanStore();
const { load, panels, deleteClinicStageByName, setCurrentStageByName, editStage } = planStore.getDetailPreview(
  planStore.formModel.id
);

const handleAdd = () => {
  panels.value.push({
    name: '',
    temporaryName: `新阶段${panels.value.length.toString()}`,
    isEditing: true,
    id: undefined,
    orderNumber: panels.value.length
  }); // Add a new panel object
};

const handleClose = (name?: string) => {
  if (!name) {
    panels.value.splice(panels.value.length - 1, 1);
  } else {
    window.$dialog?.create({
      title: '提示',
      type: 'warning',
      content: '是否删除该阶段?',
      positiveText: '确定',
      onPositiveClick: async () => {
        deleteClinicStageByName(name);
      }
    });
  }
};

const handleEdit = async (panel: Api.ClinicPlan.PanelType, index: number) => {
  if (panels.value.find((item, i) => i !== index && item.temporaryName === panel.temporaryName)) {
    window.$dialog?.warning({ content: '阶段名称重复', title: '提示' });
    return;
  }
  currentStageTab.value = panel;
  editStage(panel.name, index);
};

const handleDeleteRowItem = (id: string) => {
  window.$dialog?.warning({
    title: '删除方案项',
    content: () => (
      <div class="text-center">
        <h1 class="font-700">确定要删除吗?</h1>
        <h1 class="mt-1 text-xs">删除后不可恢复！</h1>
      </div>
    ),
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      await store.deleteClinicStageItem(id);
      load(currentStageTab.value?.name);
    }
  });
};

const tableColumns: DataTableColumn<Api.ClinicPlan.ClinicSolutionItem>[] = [
  {
    title: '',
    key: 'delete',
    align: 'center',
    width: 60,
    render: row => (
      <n-button class="p-0" quaternary circle type="error" onClick={() => handleDeleteRowItem(row.stageSoluId)}>
        {{
          icon: () => (
            <n-icon>
              <icon-ant-design-close-outlined class="p-0 text-icon color-coolGray" />
            </n-icon>
          )
        }}
      </n-button>
    )
  },
  {
    title: '序号',
    key: 'orderNumber'
  },
  {
    title: '名称',
    key: 'soluName'
  },
  {
    title: '方案天数',
    key: 'weekCount',
    render: row => (row.soluType === StageType.value.rest ? row.restDayCount : row.dayCount)
  },
  {
    title: '方案概要',
    key: 'soluSummary'
  }
];

const addProgramRef = ref();
const addRestRef = ref();
const handleAddProgram = () => {
  addProgramRef.value.show();
};

const createProgramModalRef = ref();
const handleCreateProgram = () => {
  createProgramModalRef.value.show();
};

const handleTabChange = (value: string) => setCurrentStageByName(value);

let sortable: any;
const initSortable = () => {
  const el = document.querySelector('.n-tabs-nav .n-tabs-wrapper') as HTMLElement;
  if (!el) return;

  sortable = Sortable.create(el, {
    animation: 150,
    draggable: '.n-tabs-tab-wrapper',
    onStart: () => {
      document.body.style.cursor = 'grabbing';
    },
    onEnd: async (evt: SortableEvent) => {
      const { oldIndex, newIndex } = evt;
      if (oldIndex === undefined || newIndex === undefined) return;
      if (oldIndex === newIndex) return;

      // 更新排序后的面板数据
      const updatedPanels = [...panels.value];
      const [movedPanel] = updatedPanels.splice(oldIndex - 1, 1);
      updatedPanels.splice(newIndex - 1, 0, movedPanel);

      // 批量更新排序
      const updatePromises = updatedPanels.map((panel: any, index) => {
        return store.updateClinicStageOrder({
          ...panel,
          orderNumber: index + 1,
          type: 0,
          relation: panel.name
        });
      });

      try {
        await Promise.all(updatePromises);
        panels.value = updatedPanels;
      } catch (error) {
        console.error('更新排序失败:', error);
      } finally {
        document.body.style.cursor = 'default';
      }
    }
  });
};

onMounted(() => {
  initSortable();
});

onUnmounted(() => {
  if (sortable) {
    sortable.destroy();
    sortable = null;
  }
});

defineExpose({
  panels
});
</script>

<template>
  <div>
    <PlanModalAddProgram ref="addProgramRef" @confirm="load(currentStageTab?.name)" />
    <CreateProgramModal ref="createProgramModalRef" @confirm="load(currentStageTab?.name)" />
    <PlanModalAddRest ref="addRestRef" @confirm="load(currentStageTab?.name)" />
    <NTabs
      type="card"
      :addable="true"
      :closable="true"
      :value="currentStageTab?.name"
      animated
      tab-style="min-width: 80px;"
      @update:value="handleTabChange"
      @close="handleClose"
      @add="handleAdd"
    >
      <NTabPane v-for="(panel, index) in panels" :key="panel.id" :name="panel.name">
        <template #tab>
          <NInput
            v-show="panel.isEditing"
            v-model:value="panel.temporaryName"
            :loading="loading"
            @keydown.enter="handleEdit(panel, index)"
          />
          <span v-show="!panel.isEditing" @dblclick="panel.isEditing = true">{{ panel.name }}</span>
        </template>
        <NFlex class="mb-2">
          <NButton type="default" @click="handleAddProgram">添加方案</NButton>
          <NButton type="default" @click="addRestRef.show()">添加休疗</NButton>
          <NButton type="default" @click="handleCreateProgram">新建方案</NButton>
        </NFlex>
        <NDataTable :data="panel.solutions" :columns="tableColumns" />
      </NTabPane>
    </NTabs>
    <NEmpty
      v-if="!panels.length"
      class="absolute-center mt-8"
      description="还未创建任何计划，点击左上角创建计划"
    ></NEmpty>
  </div>
</template>
