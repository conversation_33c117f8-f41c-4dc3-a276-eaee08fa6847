<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import ProgramSelect from '@/components/common/program-select.vue';
import { useStageStore } from '@/store/modules/plan/stage';
const emit = defineEmits<{
  (e: 'confirm'): void;
}>();
const showModal = ref(false);
const selected = ref('');

const store = useStageStore();
const { loading } = storeToRefs(store);

const handleCancel = () => {
  showModal.value = false;
};

const handleConfirm = async () => {
  await store.addClinicStageProgram(selected.value);
  handleCancel();
  emit('confirm');
};

defineExpose({
  show: () => {
    showModal.value = true;
  }
});
</script>

<template>
  <NModal v-model:show="showModal" title="添加方案" class="w-2xl" size="small" preset="card" @close="handleCancel">
    <ProgramSelect v-model:value="selected" placeholder="输入" class="p-4" />
    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" :disabled="loading" @click="handleCancel">取消</NButton>
      <NButton type="primary" :loading="loading" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>

<style scoped>
/* Add any necessary styles here */
</style>
