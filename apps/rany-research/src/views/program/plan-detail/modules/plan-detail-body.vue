
<script setup lang="ts">
const props = defineProps<{
  loading?: boolean;
  anchorItems?: any[];
  data?: any;
}>();
</script>

<template>
  <div class="flex-col-stretch gap-16px flex-1 overflow-hidden">
      <NCard :loading="loading" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
        <NSkeleton v-if="loading" text :repeat="6" />

        <div v-else>
          <div class="flex">
            <CommonAnchor v-if="anchorItems" :anchor-items="anchorItems" />
            <div class="document-scroll-container flex-1">
              <div v-for="stage in data?.clinicPlan.stages" :key="stage.id">
                <p :id="`${stage.id}.stage`" class="mt-1 color-info-300 font-bold">{{ stage.name }}</p>
                <div
                  v-for="program in stage.solutions"
                  :id="`${stage.id}${program.solutionId}.program`"
                  :key="program.solutionId"
                >
                  <p class="mt-1 color-info-300 font-bold">方案名称：{{ program.soluName }}</p>
                  <ProgramTable
                    :edit-mode="false"
                    :loading="loading"
                    :clinic-id="data?.clinicPlan.id"
                    :solu-id="program.solutionId"
                  />
                </div>
              </div>
              <div class="h-[50vh]"></div>
            </div>
          </div>
        </div>
      </NCard>
    </div>
</template>


<style scoped>

</style>
