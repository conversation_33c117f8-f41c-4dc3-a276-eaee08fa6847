
<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';
import PlanTimeline from './PlanTimeline.vue';

// 定义时间线数据接口
interface TimelineStage {
  id: string;
  title: string;
  solutions: Array<{
    solutionId: string;
    soluName: string;
    weekCount: number;
    dayCount: number | null;
    restDayCount: number | null;
    soluType: number;
    soluSummary: string[] | string | any;
    comment: string;
  }>;
}

const props = defineProps<{
  loading?: boolean;
  anchorItems?: any[];
  data?: any;
}>();

// 响应式数据
const activeStageId = ref<string>('');
const highlightStageId = ref<string>('');
const scrollContainerRef = ref<HTMLElement>();

// 转换数据格式为时间线数据
const timelineData = computed<TimelineStage[]>(() => {
  if (props.data?.clinicPlan?.stages) {
    return props.data.clinicPlan.stages.map((stage: any) => ({
      id: stage.id,
      title: stage.name,
      solutions: stage.solutions.map((solution: any) => ({
        solutionId: solution.solutionId,
        soluName: solution.soluName,
        weekCount: solution.weekCount || 0,
        dayCount: solution.dayCount || null,
        restDayCount: solution.restDayCount || null,
        soluType: solution.soluType || 1,
        soluSummary: solution.soluSummary || [],
        comment: solution.comment || ''
      }))
    }));
  }
  return [];
});

// 处理时间线点击事件
const handleTimelineClick = async (stageId: string) => {
  // 切换展开/收起状态
  if (activeStageId.value === stageId) {
    activeStageId.value = ''; // 如果已选中，则收起
    return; // 收起时不执行滚动
  } else {
    activeStageId.value = stageId; // 如果未选中，则展开
  }

  highlightStageId.value = stageId;

  // 滚动到对应的阶段
  await nextTick();
  const targetElement = document.getElementById(`${stageId}.stage`);
  if (targetElement && scrollContainerRef.value) {
    const containerRect = scrollContainerRef.value.getBoundingClientRect();
    const targetRect = targetElement.getBoundingClientRect();
    const scrollTop = scrollContainerRef.value.scrollTop + targetRect.top - containerRect.top - 20; // 添加20px的偏移

    scrollContainerRef.value.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    });
  }

  // 高亮效果持续2秒后消失
  setTimeout(() => {
    highlightStageId.value = '';
  }, 2000);
};

// 监听时间线数据变化，设置默认高亮
watch(timelineData, (newData) => {
  if (newData.length > 0 && !activeStageId.value) {
    // 只在没有选中任何项目时才设置默认高亮
    nextTick(() => {
      activeStageId.value = newData[0].id;
    });
  }
}, { immediate: true });
</script>

<template>
      <div  class="flex h-full overflow-hidden">
        <!-- 左侧时间线区域 -->
        <PlanTimeline
          v-if="timelineData.length > 0"
          :timeline-data="timelineData"
          :active-stage-id="activeStageId"
          @timeline-click="handleTimelineClick"
        />

        <!-- 右侧内容区域 -->
        <div ref="scrollContainerRef" class="document-scroll-container flex-1 overflow-y-auto">
          <div v-for="stage in data?.clinicPlan.stages" :key="stage.id" class="stage-section">
            <p :id="`${stage.id}.stage`" class="mt-1 color-info-300 font-bold stage-title">
              {{ stage.name }}
            </p>
            <div
              v-for="program in stage.solutions"
              :id="`${stage.id}${program.solutionId}.program`"
              :key="program.solutionId"
              class="program-section"
            >
              <p class="mt-1 color-info-300 font-bold program-title">
                方案名称：{{ program.soluName }}
              </p>
              <ProgramTable
                :edit-mode="false"
                :loading="loading"
                :clinic-id="data?.clinicPlan.id"
                :solu-id="program.solutionId"
              />
            </div>
          </div>
          <div class="h-[50vh]"></div>
        </div>
      </div>
</template>

<style scoped>
.card-wrapper {
  height: 100%;
}

.document-scroll-container {
  padding: 16px 24px;
  height: 100%;
}

.stage-section {
  margin-bottom: 32px;
}

.stage-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  padding: 8px 0;
  border-bottom: 2px solid #e0e0e0;
}

.program-section {
  margin-bottom: 24px;
  padding-left: 16px;
}

.program-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #666;
}

/* 自定义滚动条 */
.document-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.document-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.document-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.document-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
