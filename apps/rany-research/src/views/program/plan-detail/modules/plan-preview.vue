<template>
  <div class="flex h-full overflow-hidden bg-[#f9f9f9]">
    <!-- 左侧时间线区域 -->
    <PlanTimeline
      :timeline-data="timelineData"
      :active-stage-id="activeStageId"
      @timeline-click="enhancedHandleTimelineClick"
    />

    <!-- 右侧表格区域 -->
    <PlanTable
      ref="tableRef"
      :timeline-data="timelineData"
      :active-stage-id="activeStageId"
      :highlight-stage-id="highlightStageId"
      @scroll-sync="handleScrollSync"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';
import PlanTimeline from './PlanTimeline.vue';
import PlanTable from './PlanTable.vue';

// 定义 Props 接口
interface Props {
  anchorItems?: Array<{
    title: string;
    href: string;
    children: Array<{
      title: string;
      href: string;
      weekCount: number;
      days: number | null;
      soluSummary: string[] | string | any;
    }>;
  }>;
}

// 定义时间线数据接口
interface TimelineStage {
  id: string;
  title: string;
  solutions: Array<{
    solutionId: string;
    soluName: string;
    weekCount: number;
    dayCount: number | null;
    restDayCount: number | null;
    soluType: number;
    soluSummary: string[] | string | any;
    comment: string;
  }>;
}

const props = withDefaults(defineProps<Props>(), {
  anchorItems: () => []
});

// 响应式数据
const activeStageId = ref<string>('');
const highlightStageId = ref<string>('');
const tableRef = ref<InstanceType<typeof PlanTable>>();

// 转换数据格式
const timelineData = computed<TimelineStage[]>(() => {
  if (props.anchorItems && props.anchorItems.length > 0) {
    return props.anchorItems.map(item => ({
      id: item.href.replace('#', '').replace('.stage', ''),
      title: item.title,
      solutions: item.children.map(child => ({
        solutionId: child.href.replace('#', '').replace('.program', ''),
        soluName: child.title,
        weekCount: child.weekCount,
        dayCount: child.days,
        restDayCount: child.days,
        soluType: 1,
        soluSummary: Array.isArray(child.soluSummary) ? child.soluSummary : (child.soluSummary ? [child.soluSummary] : []),
        comment: ''
      }))
    }));
  }
  return [];
});

// 处理时间线点击事件
const handleTimelineClick = async (stageId: string) => {
  // 切换展开/收起状态
  if (activeStageId.value === stageId) {
    activeStageId.value = ''; // 如果已选中，则收起
    return; // 收起时不执行滚动
  } else {
    activeStageId.value = stageId; // 如果未选中，则展开
  }

  highlightStageId.value = stageId;

  // 使用表格组件的滚动方法
  if (tableRef.value) {
    await tableRef.value.scrollToStage(stageId);
  }

  // 高亮效果持续2秒后消失
  setTimeout(() => {
    highlightStageId.value = '';
  }, 2000);
};

// 监听时间线数据变化，设置默认高亮
watch(timelineData, (newData) => {
  if (newData.length > 0 && !activeStageId.value) {
    // 只在没有选中任何项目时才设置默认高亮
    nextTick(() => {
      activeStageId.value = newData[0].id;
    });
  }
}, { immediate: true });

// 处理滚动同步事件
const handleScrollSync = (stageId: string) => {
  // 可以在这里添加滚动同步逻辑
  console.log('Scroll sync:', stageId);
};

// 使用增强版的点击处理函数
const enhancedHandleTimelineClick = handleTimelineClick;
</script>

<style scoped>
/* 主容器样式 */
</style>
