<template>
  <div class="w-200px my-20 flex-shrink-0 overflow-y-auto">
    <div class="p-4">
      <n-timeline>
        <n-timeline-item
          v-for="(stage, index) in timelineData"
          :key="stage.id"
          :type="getTimelineType(index)"
          :color="getTimelineColor(index)"
          :title="stage.title"
          class="cursor-pointer timeline-item"
          :class="{ 'timeline-item-active': activeStageId === stage.id }"
          :line-type="activeStageId === stage.id ? 'dashed' : 'default'"
          @click="handleTimelineClick(stage.id)"
        >
          <template #icon>
            <div
              class="timeline-icon"
              :class="{ 'timeline-icon-active': activeStageId === stage.id }"
              :style="{
                backgroundColor: activeStageId === stage.id ? getTimelineColor(index) : 'transparent',
                borderColor: activeStageId === stage.id ? getTimelineColor(index) : '#B8B8B8'
              }"
            ></div>
          </template>
          <template #header>
            <div
              class="timeline-header"
              :class="{ 'timeline-header-active': activeStageId === stage.id }"
              :style="{
                backgroundColor: activeStageId === stage.id ? getTimelineColor(index): 'transparent',
                color: activeStageId === stage.id ? '#fff' : '#B8B8B8'
              }"
            >
              {{ stage.title }}
            </div>
          </template>
          <div
            class="timeline-content"
            :class="{
              'timeline-content-expanded': activeStageId === stage.id,
              'timeline-content-collapsed': activeStageId !== stage.id
            }"
          >
            <div v-for="solution in stage.solutions" :key="solution.solutionId" class="solution-item">
              <div class="text-sm font-medium text-gray-700">{{ solution.soluName }}</div>
            </div>
          </div>
        </n-timeline-item>
      </n-timeline>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义时间线数据接口
interface TimelineStage {
  id: string;
  title: string;
  solutions: Array<{
    solutionId: string;
    soluName: string;
    weekCount: number;
    dayCount: number | null;
    restDayCount: number | null;
    soluType: number;
    soluSummary: string[] | string | any;
    comment: string;
  }>;
}

// 定义 Props
interface Props {
  timelineData: TimelineStage[];
  activeStageId: string;
}

// 定义 Emits
interface Emits {
  (e: 'timeline-click', stageId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 时间线节点颜色配置
const timelineColors = ['#868686', '#FFA438', '#638CFD', '#AB9EFF'];

// 获取时间线节点颜色
const getTimelineColor = (index: number): string => {
  return index < timelineColors.length ? timelineColors[index] : timelineColors[timelineColors.length - 1];
};

// 获取时间线节点类型
const getTimelineType = (index: number): 'default' | 'info' | 'success' | 'warning' | 'error' => {
  const types = ['default', 'warning', 'info', 'success'] as const;
  return types[index % types.length];
};

// 处理时间线点击事件
const handleTimelineClick = (stageId: string) => {
  emit('timeline-click', stageId);
};
</script>

<style scoped>
/* 时间线样式 */
.timeline-item {
  transition: all 0.3s ease;
}

/* 移除整体背景色变化 */
.timeline-item-active {
  position: relative;
}

/* 自定义时间线图标 */
.timeline-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid;
  transition: all 0.3s ease;
}

.timeline-icon-active {
  box-shadow: inset 0 0 0 10px currentColor;
}

/* 时间线标题样式 */
.timeline-header {
  color: #B8B8B8;
  font-family: "Source Han Sans CN";
  font-size: 14px;
  font-style: normal;
  font-weight: 350;
  line-height: normal;
  transition: all 0.3s ease;
  padding: 2px 10px;
  border-radius: 6px;
  margin: -4px 0;
  display: inline-block;
  max-width: 260px;
  word-wrap: break-word;
}

.timeline-header-active {
  font-weight: 600;
}

/* 时间线内容展开/收起动画 */
.timeline-content {
  color: #666;
  transition: all 0.4s ease;
  overflow: hidden;
}

.timeline-content-collapsed {
  max-height: 0;
  opacity: 0;
  margin-top: 0;
  margin-bottom: 0;
}

.timeline-content-expanded {
  max-height: 500px;
  opacity: 1;
  margin-top: 8px;
  margin-bottom: 8px;
}

/* 方案项样式 */
.solution-item {
  padding: 6px 0;
  padding-left: 24px;
  transition: all 0.3s ease;
}

.solution-item .text-sm {
  color: #000;
  font-family: "Source Han Sans CN";
  font-size: 12px;
  font-style: normal;
  font-weight: 350;
  line-height: normal;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  display: none;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 5px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 时间线连接线颜色同步 - 默认状态统一使用灰色 */
:deep(.n-timeline .n-timeline-item .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #B8B8B8, #B8B8B8);
}

/* 选中状态的实线连接线 - 使用对应的主题颜色 */
:deep(.n-timeline .n-timeline-item:nth-child(1).timeline-item-active .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #868686, #868686);
}

:deep(.n-timeline .n-timeline-item:nth-child(2).timeline-item-active .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #FFA438, #FFA438);
}

:deep(.n-timeline .n-timeline-item:nth-child(3).timeline-item-active .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #638CFD, #638CFD);
}

:deep(.n-timeline .n-timeline-item:nth-child(4).timeline-item-active .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-image: linear-gradient(180deg, #AB9EFF, #AB9EFF);
}

/* 时间线连接线颜色同步 - 虚线状态 */
:deep(.n-timeline .n-timeline-item:nth-child(1).n-timeline-item--dashed-line-type .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-size: 1px 8px;
  background-image: linear-gradient(180deg, #868686, #868686 50%, transparent 50%, transparent 100%);
}

:deep(.n-timeline .n-timeline-item:nth-child(2).n-timeline-item--dashed-line-type .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-size: 1px 8px;
  background-image: linear-gradient(180deg, #FFA438, #FFA438 50%, transparent 50%, transparent 100%);
}

:deep(.n-timeline .n-timeline-item:nth-child(3).n-timeline-item--dashed-line-type .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-size: 1px 8px;
  background-image: linear-gradient(180deg, #638CFD, #638CFD 50%, transparent 50%, transparent 100%);
}

:deep(.n-timeline .n-timeline-item:nth-child(4).n-timeline-item--dashed-line-type .n-timeline-item-timeline .n-timeline-item-timeline__line) {
  background-size: 1px 8px;
  background-image: linear-gradient(180deg, #AB9EFF, #AB9EFF 50%, transparent 50%, transparent 100%);
}
</style>
