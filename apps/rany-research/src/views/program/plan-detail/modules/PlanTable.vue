<template>
  <div class="flex-1 flex flex-col overflow-hidden pr-30">
    <!-- 表格头部 - 固定在顶部 -->
    <div class="sticky top-0 z-10 table-header">
      <div class="table-header-grid">
        <div class="text-center">计划周数</div>
        <div class="text-center">方案名称</div>
        <div class="text-center">方案天数</div>
        <div class="text-center">方案描述</div>
      </div>
    </div>

    <!-- 表格内容 - 可滚动 -->
    <div ref="tableContentRef" class="flex-1 overflow-y-auto">
      <div
        v-for="stage in timelineData"
        :key="stage.id"
        :id="`stage-${stage.id}`"
        class="table-group"
      >
        <!-- 阶段标题行 -->
        <div
          class="stage-title-row"
          :class="{ 'stage-title-active': activeStageId === stage.id }"
          :style="{ color: activeStageId === stage.id ? getTimelineColor(timelineData.findIndex(item => item.id === stage.id)) : '#B8B8B8' }"
        >
          {{ stage.title }}
        </div>

        <!-- 方案数据行容器 -->
        <div class="solutions-container">
          <div
            v-for="solution in stage.solutions"
            :key="solution.solutionId"
            class="solution-row"
            :class="{ 'solution-row-active': activeStageId === stage.id }"
          >
            <div class="solution-data">
              <div class="solution-field solution-stage">
                {{ solution.weekCount}}
              </div>
              <n-tooltip trigger="hover" :show-arrow="false">
                <template #trigger>
                  <div class="solution-field solution-name">
                    {{ solution.soluName }}
                  </div>
                </template>
                {{ solution.soluName }}
              </n-tooltip>
              <div class="solution-field solution-duration">
                {{ (solution.soluType === 1 ? solution.dayCount : solution.restDayCount) || 0 }}
              </div>
              <n-tooltip trigger="hover" :show-arrow="false">
                <template #trigger>
                  <div class="solution-field solution-description">
                    <span v-if="getSoluSummaryText(solution.soluSummary)">
                      {{ getSoluSummaryText(solution.soluSummary) }}
                    </span>
                  </div>
                </template>
                {{ getFullDescriptionText(solution) }}
              </n-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

// 定义时间线数据接口
interface TimelineStage {
  id: string;
  title: string;
  solutions: Array<{
    solutionId: string;
    soluName: string;
    weekCount: number;
    dayCount: number | null;
    restDayCount: number | null;
    soluType: number;
    soluSummary: string[] | string | any;
    comment: string;
  }>;
}

// 定义 Props
interface Props {
  timelineData: TimelineStage[];
  activeStageId: string;
  highlightStageId?: string;
}

// 定义 Emits
interface Emits {
  (e: 'scroll-sync', stageId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const tableContentRef = ref<HTMLElement>();

// 时间线节点颜色配置
const timelineColors = ['#868686', '#FFA438', '#638CFD', '#AB9EFF'];

// 获取时间线节点颜色
const getTimelineColor = (index: number): string => {
  return index < timelineColors.length ? timelineColors[index] : timelineColors[timelineColors.length - 1];
};

// 安全地获取 soluSummary 文本
const getSoluSummaryText = (soluSummary: any): string => {
  if (!soluSummary) return '';

  if (Array.isArray(soluSummary)) {
    return soluSummary.join(', ');
  }

  if (typeof soluSummary === 'string') {
    return soluSummary;
  }

  return String(soluSummary);
};

// 获取完整的描述文本用于 tooltip
const getFullDescriptionText = (solution: any): string => {
  const summaryText = getSoluSummaryText(solution.soluSummary);
  const commentText = solution.comment || '';

  if (summaryText && commentText) {
    return `${summaryText} ${commentText}`;
  }

  return summaryText || commentText;
};

// 防抖函数
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 滚动监听变量
let intersectionObserver: IntersectionObserver | null = null;
let isScrollingProgrammatically = false;

// 处理滚动时的时间线同步
const handleScrollSync = debounce(() => {
  if (isScrollingProgrammatically || !tableContentRef.value) return;

  const container = tableContentRef.value;
  const containerRect = container.getBoundingClientRect();
  const containerTop = containerRect.top;
  const containerHeight = containerRect.height;
  const viewportCenter = containerTop + containerHeight / 2;

  let closestStage = '';
  let closestDistance = Infinity;

  // 查找最接近视口中心的阶段
  props.timelineData.forEach(stage => {
    const element = document.getElementById(`stage-${stage.id}`);
    if (element) {
      const elementRect = element.getBoundingClientRect();
      const elementCenter = elementRect.top + elementRect.height / 2;
      const distance = Math.abs(elementCenter - viewportCenter);

      if (distance < closestDistance) {
        closestDistance = distance;
        closestStage = stage.id;
      }
    }
  });

  if (closestStage) {
    emit('scroll-sync', closestStage);
  }
}, 150);

// 设置 Intersection Observer
const setupIntersectionObserver = () => {
  if (!tableContentRef.value) return;

  const options = {
    root: tableContentRef.value,
    rootMargin: '-20% 0px -20% 0px',
    threshold: [0.1, 0.5, 0.9]
  };

  intersectionObserver = new IntersectionObserver((entries) => {
    if (isScrollingProgrammatically) return;

    let mostVisibleEntry: IntersectionObserverEntry | null = null;
    let maxIntersectionRatio = 0;

    entries.forEach(entry => {
      if (entry.isIntersecting && entry.intersectionRatio > maxIntersectionRatio) {
        maxIntersectionRatio = entry.intersectionRatio;
        mostVisibleEntry = entry;
      }
    });

    if (mostVisibleEntry) {
      const stageId = (mostVisibleEntry.target as HTMLElement).id.replace('stage-', '');
      if (stageId) {
        emit('scroll-sync', stageId);
      }
    }
  }, options);

  // 观察所有阶段元素
  props.timelineData.forEach(stage => {
    const element = document.getElementById(`stage-${stage.id}`);
    if (element && intersectionObserver) {
      intersectionObserver.observe(element);
    }
  });
};

// 滚动到指定阶段
const scrollToStage = async (stageId: string) => {
  isScrollingProgrammatically = true;

  await nextTick();
  const targetElement = document.getElementById(`stage-${stageId}`);
  if (targetElement && tableContentRef.value) {
    const containerRect = tableContentRef.value.getBoundingClientRect();
    const targetRect = targetElement.getBoundingClientRect();
    const scrollTop = tableContentRef.value.scrollTop + targetRect.top - containerRect.top;

    tableContentRef.value.scrollTo({
      top: scrollTop,
      behavior: 'smooth'
    });
  }

  // 滚动完成后重置标记
  setTimeout(() => {
    isScrollingProgrammatically = false;
  }, 1000);
};

// 组件挂载时设置监听
onMounted(() => {
  nextTick(() => {
    setupIntersectionObserver();

    // 添加滚动监听
    if (tableContentRef.value) {
      tableContentRef.value.addEventListener('scroll', handleScrollSync, { passive: true });
    }
  });
});

// 组件卸载时清理
onUnmounted(() => {
  if (intersectionObserver) {
    intersectionObserver.disconnect();
    intersectionObserver = null;
  }

  if (tableContentRef.value) {
    tableContentRef.value.removeEventListener('scroll', handleScrollSync);
  }
});

// 暴露方法给父组件
defineExpose({
  scrollToStage,
  tableContentRef
});
</script>

<style scoped>
/* 表格头部样式 */
.table-header {
  opacity: 0.9;
  background: #F2F2F2;
}

.table-header-grid {
  display: grid;
  grid-template-columns: 15% 25% 15% 45%;
  padding: 16px 0;
  font-weight: 500;
  color: #4A5568;
}

/* 表格样式 */
.table-group {
  transition: all 0.3s ease;
}

.table-group-highlight {
  background-color: rgba(99, 140, 253, 0.05);
  border-left: 4px solid #638CFD;
}

.table-group-active {
  position: relative;
}

/* 阶段标题行样式 */
.stage-title-row {
  color: #B8B8B8;
  font-family: "Source Han Sans CN";
  font-size: 16px;
  font-style: normal;
  font-weight: 350;
  line-height: normal;
  padding: 18px 24px 0;
  transition: all 0.3s ease;
}

.stage-title-active {
  font-weight: 500;
}

/* 方案数据行容器 */
.solutions-container {
  padding: 8px 0;
}

/* 方案数据行样式 */
.solution-row {
  margin-bottom: 4px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.75);
  transition: all 0.3s ease;
}

.solution-row-active {
  background: #FFF;
  box-shadow: 2px 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 方案数据网格 */
.solution-data {
  display: grid;
  grid-template-columns: 15% 25% 15% 45%;
  min-height: 40px;
  align-items: center;
  overflow: hidden;
  width: 100%;
}

/* 方案字段样式 */
.solution-field {
  color: #7A7A7A;
  text-align: center;
  font-family: "Source Han Sans CN";
  font-size: 14px;
  font-style: normal;
  font-weight: 350;
  line-height: normal;
}

.solution-row-active .solution-field {
  color: #000;
}

/* 计划阶段列 */
.solution-stage {
  text-align: center;
}

/* 方案名称列 - 添加文字溢出处理 */
.solution-name {
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 方案天数列 */
.solution-duration {
  text-align: center;
}

/* 方案描述列 - 添加文字溢出处理 */
.solution-description {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  min-width: 0;
}

.solution-description .comment-text {
  margin-left: 8px;
  font-size: 12px;
  opacity: 0.8;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  display: none;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 5px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
