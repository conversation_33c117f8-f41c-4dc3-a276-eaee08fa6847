<script setup lang="ts">
import { computed, onMounted, watchEffect } from 'vue';
import { useRoute ,useRouter} from 'vue-router';
import { storeToRefs } from 'pinia';
import { usePlanStore } from '@/store/modules/plan';
import { useItemStore } from '@/store/modules/program/item';
import { PlanDataSource } from '@/store/modules/plan/PlanDataSource';
import ProgramTable from '@/components/advanced/program-table/index.vue';
import PlanDetailHeader from './modules/plan-detail-header.vue';
import PlanDetailBody from './modules/plan-detail-body.vue';
import PlanPreview from './modules/plan-preview.vue';

const route = useRoute();
const router = useRouter();
const id = computed(() => route.params.id);

const itemStore = useItemStore();

onMounted(() => {
  itemStore.dataSource = new PlanDataSource();
});

const store = usePlanStore();
const { loading } = storeToRefs(store);
const { data, load } = store.getSpecificDetail();

watchEffect(
  () => {
    if (typeof id.value === 'string') {
      load(id.value);
    } else {
      // window.$dialog?.warning({ content: '当前计划信息不存在' });
    }
  },
  { flush: 'sync' }
);

const anchorItems = computed(() => {
  return data.value?.clinicPlan.stages.map(stage => ({
    title: `${stage.name}`,
    href: `#${stage.id}.stage`,
    children: stage.solutions.map(program => ({
      title: program.soluName,
      href: `#${stage.id}${program.solutionId}.program`,
      weekCount:program.weekCount,
      days:program.soluType=== 1? program.dayCount : program.restDayCount,
      soluSummary:program.soluSummary
    }))
  }));
});

// 计算头部组件需要的数据
const planHeaderData = computed(() => {
  if (!data.value?.clinicPlan) return undefined;

  return {
    id: data.value.clinicPlan.id,
    name: data.value.clinicPlan.name,
    status: data.value.clinicPlan.status || 0,
    diseaseName: data.value.clinicPlan.diseaseName,
    riskName: data.value.clinicPlan.riskName,
    projectName: data.value.clinicPlan.projectName,
    createTime: data.value.clinicPlan.createTime,
    updateTime: data.value.clinicPlan.updateTime
  };
});

const activeIndex = ref(0);

// 事件处理函数（暂时为空，后续实现具体功能）
const handleEdit = () => {
  console.log('编辑计划');
  // TODO: 实现编辑功能
  router.push(`/program/plan_operate/show/${id.value}`);
};

const handleToggleStatus = () => {
  console.log('切换状态');
  // TODO: 实现状态切换功能
};

const handleDelete = () => {
  console.log('删除计划');
  // TODO: 实现删除功能
};

const handleCopy = () => {
  console.log('复制计划');
  // TODO: 实现复制功能
};
</script>

<template>
  <div class="flex flex-col flex-1 overflow-hidden lt-sm:overflow-auto">
    <!-- 头部区域 -->
    <div class="mb-1 bg-white px-6 py-4 shadow-[2px_2px_10px_0px_rgba(160,209,255,0.15)]">
      <PlanDetailHeader
        v-model:active-index="activeIndex"
        :plan-data="planHeaderData"
        :loading="loading"
        @edit="handleEdit"
        @toggle-status="handleToggleStatus"
        @delete="handleDelete"
        @copy="handleCopy"

      />
    </div>

    <!-- 主体内容区域 -->
     <PlanDetailBody v-if="activeIndex === 1" :anchor-items="anchorItems" :data="data" :loading="loading" />

    <PlanPreview v-if="activeIndex === 0"  :anchor-items="anchorItems" />
  </div>
</template>

<style scoped></style>
