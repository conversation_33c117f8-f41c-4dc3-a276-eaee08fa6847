<script setup lang="ts">
import { computed, ref, watchEffect } from 'vue';
import { useDialog } from 'naive-ui';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useItemStore } from '@/store/modules/program/item';
import { useProgramStore } from '@/store/modules/program';
import ProgramTable from '@/components/advanced/program-table/index.vue';
import { ProgramDataSource } from '@/store/modules/program/programDataSource';
import LibraryOperateDrawer from '../library/modules/library-operate-drawer.vue';

const route = useRoute();
const programId = computed(() => route.params.id);
const store = useItemStore();
const programStore = useProgramStore();
const dialog = useDialog();

const { actionLoading: loading } = storeToRefs(programStore);
const { programModal } = storeToRefs(programStore);

// 监听路由参数变化,加载方案详情
watchEffect(
  () => {
    if (typeof programId.value === 'string') {
      programStore.getProgramDetail(programId.value);
      store.dataSource = new ProgramDataSource();
    }
  },
  { flush: 'sync' }
);

// 编辑抽屉相关
const drawerVisible = ref(false);
const operateType = ref<NaiveUI.TableOperateType>('edit');
const editingData = ref();

function handleEdit() {
  editingData.value = programModal.value;
  operateType.value = 'edit';
  drawerVisible.value = true;
}

async function handleSubmitted() {
  if (typeof programId.value === 'string') {
    await programStore.getProgramDetail(programId.value);
  }
}

const editMode = ref(false);
function handleEditProgram() {
  editMode.value = true;
}

function handleCancelEditProgram() {
  editMode.value = false;
}

async function handleUpdateStatus(status?: number) {
  if (programModal.value.id) {
    programStore.programModal.status = status;
    await programStore.updateProgramStatus(false);
  }
}

function handleConfirm(status?: number) {
  if (status === programStore.Status.disabled) {
    dialog.warning({
      title: '停用',
      content: '您确认要停用吗? 停用后不可使用该方案，历史数据可以查询！',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleUpdateStatus(status)
    });
  }
  if (status === programStore.Status.published) {
    dialog.warning({
      title: '发布',
      content: '是否确认发布？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => handleUpdateStatus(status)
    });
  }
}
</script>

<template>
  <div class="flex-col-stretch gap-16px">
    <NCard :loading="loading" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <!-- 标题区域 -->
      <template #header>
        <NSkeleton v-if="loading" text width="60%" />
        <template v-else>
          {{ programModal.name }}
        </template>
      </template>

      <template #header-extra>
        <NSpace>
          <NButton type="primary" ghost size="small" @click="handleEdit">
            {{ $t('common.edit') }}
          </NButton>
          <NButton v-if="!editMode" type="primary" ghost size="small" @click="handleEditProgram">编辑方案</NButton>
          <NButton v-else type="tertiary" ghost size="small" @click="handleCancelEditProgram">取消编辑</NButton>
          <NButton
            v-if="
              programModal.status === programStore.Status.drafted ||
              programModal.status === programStore.Status.disabled
            "
            type="success"
            ghost
            size="small"
            @click="handleConfirm(programStore.Status.published)"
          >
            发布
          </NButton>
          <NButton v-else type="warning" ghost size="small" @click="handleConfirm(programStore.Status.disabled)">
            停用
          </NButton>
        </NSpace>
      </template>

      <!-- 内容区域 -->
      <NSkeleton v-if="loading" text :repeat="6" />

      <div v-else>
        <!-- 方案描述信息 -->
        <NDescriptions label-placement="top" title="方案描述">
          <NDescriptionsItem>
            <template #label>疾病分型</template>
            {{ programModal.diseaseName || '暂无' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="危险度分型">
            {{ programModal.riskNames?.join(',') || '暂无' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="状态">
            <NTag type="info" :bordered="false">
              {{ programStore.statusLabel(programModal.status) }}
            </NTag>
          </NDescriptionsItem>
          <NDescriptionsItem label="创建时间">
            {{ programModal.createTime }}
          </NDescriptionsItem>
          <NDescriptionsItem label="更新时间">{{ programModal.updateTime }}</NDescriptionsItem>
        </NDescriptions>

        <NDivider />

        <!-- 方案表格 -->
        <div class="flex">
          <div class="document-scroll-container flex-1">
            <ProgramTable :edit-mode="editMode" :loading="loading" :solu-id="programModal.id" />
          </div>
        </div>
      </div>
    </NCard>

    <LibraryOperateDrawer
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="handleSubmitted"
    />
  </div>
</template>

<style scoped></style>
