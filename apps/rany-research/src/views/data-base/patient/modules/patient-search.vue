<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import type { SelectOption } from 'naive-ui';
import { fetchPostExperimentList, fetchPostHospitalList } from '@/service/api';

interface Props {
  searchParams?: Api.Patient.CommonSearchParams;
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    hospitalName: undefined,
    projectId: undefined
  })
});

interface Emits {
  (e: 'filter-change', params: Partial<Api.Patient.CommonSearchParams>): void;
}

const emit = defineEmits<Emits>();

// 医院选择器选项
const hospitalOptions = ref<{ label: string; value: string | undefined }[]>([]);
const selectedHospitalName = ref<string | null>(props.searchParams?.hospitalName || null);

// 项目选择器选项
const projectOptions = ref<SelectOption[]>([]);
const selectedProjectId = ref<string | null>(props.searchParams?.projectId || null);

// 获取医院选项 - 这里暂时使用静态数据，实际项目中应该调用API
async function getHospitalOptions() {
  // TODO: 调用API获取医院列表
  const res = await fetchPostHospitalList({
    pageNo: 1,
    pageSize: 1000,
    status: null
  });
  hospitalOptions.value =
    res.data?.records.map(item => ({
      label: item.name,
      value: item.name
    })) || [];
}

// 获取项目选项 - 这里暂时使用静态数据，实际项目中应该调用API
async function getProjectOptions() {
  const res = await fetchPostExperimentList({
    pageNo: 1,
    pageSize: 1000,
    stage: null,
    name: null,
    type: null
  });
  projectOptions.value =
    res.data?.records.map(item => ({
      label: item.name,
      value: item.id
    })) || [];
}

// 处理医院筛选变化
const handleHospitalChange = (value: string | null) => {
  selectedHospitalName.value = value;
  emit('filter-change', { hospitalName: value || undefined });
};

// 处理项目筛选变化
const handleProjectChange = (value: string | null) => {
  selectedProjectId.value = value;
  emit('filter-change', { projectId: value || undefined });
};

// 监听外部搜索参数变化
watch(
  () => props.searchParams?.hospitalName,
  newValue => {
    selectedHospitalName.value = newValue || null;
  }
);

watch(
  () => props.searchParams?.projectId,
  newValue => {
    selectedProjectId.value = newValue || null;
  }
);

onMounted(() => {
  getHospitalOptions();
  getProjectOptions();
});
</script>

<template>
  <div class="flex gap-16px">
    <div class="filter-item">
      <div class="filter-label">医院：</div>
      <div class="filter-control">
        <NSelect
          v-model:value="selectedHospitalName"
          class="filter-select"
          placeholder="请选择医院"
          :options="hospitalOptions"
          clearable
          @update:value="handleHospitalChange"
        />
      </div>
    </div>

    <div class="filter-item">
      <div class="filter-label">当前研究：</div>
      <div class="filter-control">
        <NSelect
          v-model:value="selectedProjectId"
          class="filter-select"
          placeholder="请选择研究项目"
          :options="projectOptions"
          clearable
          @update:value="handleProjectChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  white-space: nowrap;
  font-size: 14px;
  color: var(--text-color-2);
}

.filter-select {
  min-width: 120px;
}
</style>
