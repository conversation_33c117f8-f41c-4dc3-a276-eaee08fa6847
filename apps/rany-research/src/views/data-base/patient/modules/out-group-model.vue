<script setup lang="ts">
import { ref, watch } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { fetchParticipantProjectList, outGroupParticipant } from '@/service/api';

const visible = ref(false);
const loading = ref(false);

const props = defineProps<{
  patientId: string;
  projectHospitalId: string;
}>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const projectOptions = ref<Api.Common.CommonOption[]>([]);
const projectList = ref<any[]>([]);

const addingModel = ref({
  humanId: ''
});

const initProjectOptions = async () => {
  addingModel.value.humanId = '';
  const res = await fetchParticipantProjectList({
    pageNo: 1,
    pageSize: 999,
    patientId: props.patientId,
    projectHospitalId: props.projectHospitalId,
    status: 0
  });
  projectList.value = res.data?.records || [];
  projectOptions.value =
    res.data?.records?.map(item => ({
      label: item.name || '',
      value: item.id || ''
    })) || [];
};

watch(visible, () => {
  if (visible.value) {
    restoreValidation();
    initProjectOptions();
  }
});

function handleCancel() {
  visible.value = false;
}

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

async function handleConfirm() {
  try {
    loading.value = true;
    await validate();
    const res = await outGroupParticipant({
      userName: 'admin',
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      id: addingModel.value.humanId
    });
    if (res.response.data?.status === 'OK') {
      window.$message?.success('出组成功');
      visible.value = false;
      emit('submitted');
    }
  } catch (error) {
    console.error('出组失败:', error);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  show: () => (visible.value = true)
});
</script>

<template>
  <NModal v-model:show="visible" class="w-2xl" title="患者出组" :mask-closable="false" preset="card">
    <NForm
      ref="formRef"
      :model="addingModel"
      label-placement="top"
      :label-width="100"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="已入组试验" path="projectId" :required="true">
        <NSelect v-model:value="addingModel.humanId" placeholder="请选择临床试验" :options="projectOptions" />
      </NFormItem>
      <div class="mt-4 text-red-500">确认出组吗？</div>
    </NForm>

    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" :disabled="loading" @click="handleCancel">取消</NButton>
      <NButton type="primary" :loading="loading" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>
