<script setup lang="ts">
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { NDatePicker } from 'naive-ui';
import { useNaiveForm } from '@/hooks/common/form';
import { useBagStore } from '@/store/modules/bag';

const store = useBagStore();
const { loading, addingModel } = storeToRefs(store);

const visible = ref(false);
const { formRef, validate, restoreValidation } = useNaiveForm();
watch(visible, () => {
  if (visible.value) {
    restoreValidation();
  } else {
    store.resetAddingModel();
  }
});

function handleCancel() {
  visible.value = false;
}
interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();
function handleConfirm() {
  validate().then(async () => {
    const result = await store.generateBagAction();
    if (result) {
      visible.value = false;
      emit('submitted');
    }
  });
}

defineExpose({
  show: () => (visible.value = true)
});
</script>

<template>
  <NModal v-model:show="visible" class="w-2xl" title="启动治疗" :mask-closable="false" preset="card">
    <NForm
      ref="formRef"
      :model="addingModel"
      label-placement="top"
      :label-width="100"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="登记首页" path="registPage" :required="true">
        <CheckoutSelect v-model:model-value="addingModel.registPage" />
      </NFormItem>
      <NFormItem label="诊疗计划" path="registPage">
        <PlanSelect v-model:value="addingModel.clinicPlanId" placeholder="请输入名称" />
      </NFormItem>
      <NFormItem label="治疗（专科袋）开始日期" path="startTime">
        <NDatePicker v-model:model-value="addingModel.startTime" />
      </NFormItem>
    </NForm>

    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" :disabled="loading" @click="handleCancel">取消</NButton>
      <NButton type="primary" :loading="loading" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>
