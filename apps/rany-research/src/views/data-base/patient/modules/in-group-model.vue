<script setup lang="ts">
import { ref, watch } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { addParticipant, fetchParticipantProjectList } from '@/service/api';

const visible = ref(false);
const loading = ref(false);

const props = defineProps<{
  patientId: string;
  projectHospitalId: string;
}>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const projectOptions = ref<Api.Common.CommonOption[]>([]);

const addingModel = ref({
  number: '',
  projectId: ''
});

const initProjectOptions = async () => {
  addingModel.value.projectId = '';
  addingModel.value.number = '';
  const res = await fetchParticipantProjectList({
    pageNo: 1,
    pageSize: 999,
    projectHospitalId: props.projectHospitalId,
    patientId: props.patientId,
    status: 1
  });

  projectOptions.value =
    res.data?.records.map(item => ({
      label: item.name || '',
      value: item.id || ''
    })) || [];
};

watch(visible, () => {
  if (visible.value) {
    restoreValidation();
    initProjectOptions();
  }
});

function handleCancel() {
  visible.value = false;
}

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

async function handleConfirm() {
  try {
    loading.value = true;
    await validate();
    const addRes = await addParticipant({
      patientId: props.patientId,
      number: addingModel.value.number,
      projectId: addingModel.value.projectId,
      userName: 'admin',
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      status: 1,
      projectHospitalId: props.projectHospitalId
    } as Api.Participant.ParticipantAddReqVO);
    if (addRes.response.data?.status === 'OK') {
      // const res = await inGroupParticipant({
      //   id: addRes.response.data.data as string,
      //   userName: 'admin',
      //   userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
      // });
      // if (res.response.data?.status === 'OK') {
      window.$message?.success('入组成功');
      visible.value = false;
      emit('submitted');
      // }
    }
  } catch (error) {
    console.error('入组失败:', error);
  } finally {
    loading.value = false;
  }
}

defineExpose({
  show: () => (visible.value = true)
});
</script>

<template>
  <NModal v-model:show="visible" class="w-2xl" title="患者入组" :mask-closable="false" preset="card">
    <NForm
      ref="formRef"
      :model="addingModel"
      label-placement="top"
      :label-width="100"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="临床试验" path="projectId" :required="true">
        <NSelect v-model:value="addingModel.projectId" placeholder="请选择临床试验" :options="projectOptions" />
      </NFormItem>
      <NFormItem label="受试者编号" path="number" :required="true">
        <NInput v-model:value="addingModel.number" placeholder="请输入受试者编号" />
      </NFormItem>
      <div class="mt-4 text-red-500">确认入组吗？请确认该患者已经签署知情同意书！</div>
    </NForm>

    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" :disabled="loading" @click="handleCancel">取消</NButton>
      <NButton type="primary" :loading="loading" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>
