<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import dayjs from 'dayjs';
import { storeToRefs } from 'pinia';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';
import { extractBirthDateFromIDCard } from '@/utils/common';
import { usePatientStore } from '@/store/modules/patient';
import { useExperimentStore } from '@/store/modules/experiment';
import RiskCascader from '@/components/common/risk-cascader.vue';

defineOptions({
  name: 'PatientOperateDrawer'
});
interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Disease.Disease | null;
}
const props = defineProps<Props>();
interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const genderoptions = ref([
  {
    label: '男',
    value: '男'
  },
  {
    label: '女',
    value: '女'
  },
  {
    label: '未说明',
    value: '未说明'
  },
  {
    label: '未知',
    value: '未知'
  }
]);

const store = usePatientStore();
const experimentStore = useExperimentStore();
const { actionLoading, patientModal } = storeToRefs(store);
const { maritalStatusOptions } = storeToRefs(experimentStore);
const { getMaritalStatusOptions } = experimentStore;
const { updatePatient, addPatient, fetchGetPatientDetail } = store;

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增患者',
    edit: '编辑患者'
  };
  return titles[props.operateType];
});

type Model = Api.Patient.PatientAddReqVO | Api.Patient.PatientItem;

type RuleKey = Extract<
  keyof Model,
  | 'gender'
  | 'name'
  | 'hospitalId'
  | 'height'
  | 'weight'
  | 'birthplace'
  | 'familyAddress'
  | 'permanentAddress'
  | 'nativePlace'
  | 'relativeName'
  | 'diagnosisInit'
  | 'outpatientNumber'
  | 'credNumber'
>;

const rules: Record<RuleKey, App.Global.FormRule[]> = {
  gender: [defaultRequiredRule],
  name: [defaultRequiredRule],
  hospitalId: [defaultRequiredRule],
  height: [
    defaultRequiredRule,
    {
      validator: (_, value) => {
        if (value <= 40) {
          return new Error('身高应大于40cm');
        }
        if (value > 250) {
          return new Error('身高应小于250cm');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ],
  weight: [
    defaultRequiredRule,
    {
      validator: (_, value) => {
        if (value <= 1) {
          return new Error('体重应大于1kg');
        }
        if (value > 700) {
          return new Error('体重应小于700kg');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ],
  birthplace: [
    {
      validator: (_, value) => {
        if (value && value.length > 100) {
          return new Error('出生地不能超过100个字符');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ],
  familyAddress: [
    {
      validator: (_, value) => {
        if (value && value.length > 100) {
          return new Error('现住址不能超过100个字符');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ],
  permanentAddress: [
    {
      validator: (_, value) => {
        if (value && value.length > 100) {
          return new Error('户口地址不能超过100个字符');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ],
  nativePlace: [
    {
      validator: (_, value) => {
        if (value && value.length > 100) {
          return new Error('籍贯不能超过100个字符');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ],
  relativeName: [
    {
      validator: (_, value) => {
        if (value && value.length > 100) {
          return new Error('亲属姓名不能超过100个字符');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ],
  diagnosisInit: [
    {
      validator: (_, value) => {
        if (value && value.length > 100) {
          return new Error('初步诊断不能超过100个字符');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ],
  outpatientNumber: [
    {
      validator: (_, value) => {
        if (value && value.length > 100) {
          return new Error('门诊号不能超过100个字符');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ],
  credNumber: [
    {
      validator: (_, value) => {
        if (!value) return true;

        // 15位或18位身份证号码的正则表达式
        const reg = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/;

        if (!reg.test(value)) {
          return new Error('请输入有效的身份证号码');
        }
        return true;
      },
      trigger: ['input', 'blur']
    }
  ]
};

function handleInitModel() {
  store.reset();

  if (props.operateType === 'edit' && props.rowData?.id) {
    fetchGetPatientDetail(props.rowData?.id);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  if (props.operateType === 'edit') {
    // 如果nationName有值，设置为null

    if (patientModal.value.nationName) {
      patientModal.value.nationName = '';
    }
    const result = await updatePatient(patientModal.value as any);
    if (result) {
      window.$message?.success($t('common.updateSuccess'));
    }
  }

  if (props.operateType === 'add') {
    const result = await addPatient({
      ...patientModal.value,
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      userName: 'admin'
    });
    if (result) {
      window.$message?.success($t('common.addSuccess'));
    }
  }
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  handleInitModel();
  if (visible.value) {
    restoreValidation();
    getMaritalStatusOptions();
  }
});

const handleCredNumberChange = (id: string) => {
  const birthday = extractBirthDateFromIDCard(id);
  if (birthday) {
    patientModal.value.birthday = dayjs(birthday).format('YYYY-MM-DD HH:MM:DD');
  }
};
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable :loading="actionLoading">
      <NForm ref="formRef" :model="patientModal" :rules="rules" label-placement="left" :loading="actionLoading">
        <h2 class="font-700">{{ $t('page.database.patient.title') }}</h2>
        <NFormItem path="name" label="患者姓名" required>
          <NInput v-model:value="patientModal.name" placeholder="请输入患者姓名" />
        </NFormItem>
        <NFormItem path="gender" label="性别" required>
          <NSelect v-model:value="patientModal.gender" placeholder="请选择性别" :options="genderoptions"></NSelect>
        </NFormItem>
        <NFormItem path="height" label="身高" required>
          <NInputNumber v-model:value="patientModal.height as any" placeholder="请输入身高">
            <template #suffix>cm</template>
          </NInputNumber>
        </NFormItem>

        <NFormItem path="weight" label="体重" required>
          <NInputNumber v-model:value="patientModal.weight as any" placeholder="请输入体重">
            <template #suffix>kg</template>
          </NInputNumber>
        </NFormItem>

        <NFormItem path="nation" label="民族">
          <SelectNation v-model:value="patientModal.nation" />
        </NFormItem>
        <NFormItem path="birthday" label="出生日期">
          <NDatePicker v-model:value="patientModal.birthday as any" type="date" placeholder="请选择出生日期" />
        </NFormItem>
        <NFormItem path="birthplace" label="出生地">
          <NInput v-model:value="patientModal.birthplace" placeholder="请输入出生地" />
        </NFormItem>
        <NFormItem path="credNumber" label="身份证号码">
          <NInput
            v-model:value="patientModal.credNumber"
            placeholder="请输入身份证号码"
            @change="handleCredNumberChange"
          />
        </NFormItem>
        <NFormItem path="familyAddress" label="现住址">
          <NInput v-model:value="patientModal.familyAddress" placeholder="请输入现住址" />
        </NFormItem>
        <NFormItem path="permanentAddress" label="户口地址">
          <NInput v-model:value="patientModal.permanentAddress" placeholder="请输入户口地址" />
        </NFormItem>
        <NFormItem path="phoneNumber" label="联系电话">
          <NInput v-model:value="patientModal.phoneNumber" placeholder="请输入联系电话" />
        </NFormItem>
        <NFormItem path="nativePlace" label="籍贯">
          <NInput v-model:value="patientModal.nativePlace" placeholder="请输入籍贯" />
        </NFormItem>
        <NFormItem path="relativeName" label="亲属姓名">
          <NInput v-model:value="patientModal.relativeName" placeholder="请输入亲属姓名" />
        </NFormItem>
        <NFormItem path="maritalStatus" label="婚姻状况">
          <NSelect
            v-model:value="patientModal.maritalStatus"
            placeholder="请选择婚姻状况"
            :options="maritalStatusOptions"
          ></NSelect>
        </NFormItem>
        <h2 class="font-700">就诊信息</h2>
        <NFormItem path="hospitalId" label="就诊医院">
          <HospitalSelect v-model:value="patientModal.hospitalId" />
        </NFormItem>
        <NFormItem path="diagnosisInit" label="初步诊断">
          <NInput v-model:value="patientModal.diagnosisInit" placeholder="请输入初步诊断" />
        </NFormItem>
        <NFormItem path="diagnosisFinals" label="最终诊断">
          <DiseaseMultiSelect v-model:value="patientModal.diagnosisFinals" />
        </NFormItem>
        <NFormItem path="diagnosisTime" label="诊断时间">
          <NDatePicker
            v-model:value="patientModal.diagnosisTime as any"
            type="datetime"
            placeholder="请选择诊断时间"
            value-format="YYYY-MM-dd HH:mm:dd"
            :is-date-disabled="(timestamp: number) => timestamp > Date.now()"
          />
        </NFormItem>

        <NFormItem path="outpatientNumber" label="门诊号">
          <NInput v-model:value="patientModal.outpatientNumber" placeholder="请输入门诊号" />
        </NFormItem>

        <NFormItem path="riskId" label="临床危险度">
          <RiskCascader v-model:value="patientModal.riskId" />
        </NFormItem>

        <NFormItem path="patient.diseaseType" label="疾病分型">
          <DiseaseSelect v-model:value="patientModal.diseaseId" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" :loading="actionLoading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
