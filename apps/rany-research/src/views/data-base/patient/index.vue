<script setup lang="tsx">
import { NButton } from 'naive-ui';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { fetchPostPatientList } from '@/service/api';
import { useBagStore } from '@/store/modules/bag';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import PatientOperateDrawer from './modules/patient-operate-drawer.vue';
import StartTreatmentModal from './modules/start-treatment-modal.vue';
import InGroupModel from './modules/in-group-model.vue';
import OutGroupModel from './modules/out-group-model.vue';
import PatientSearch from './modules/patient-search.vue';

const appStore = useAppStore();
const router = useRouter();

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams: _resetSearchParams
} = useTable({
  apiFn: fetchPostPatientList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    name: undefined,
    hospitalName: undefined,
    projectId: undefined
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center' as const,
      width: 64
    },
    {
      key: 'name',
      title: '患者姓名',
      align: 'center' as const,
      minWidth: 100
    },
    {
      key: 'gender',
      title: '性别',
      align: 'center' as const,
      width: 80
    },
    {
      key: 'hospitalName',
      title: '当前医院',
      align: 'center' as const,
      minWidth: 120
    },
    {
      key: 'diagnosisInit',
      title: '初步诊断',
      align: 'center' as const,
      minWidth: 120
    },
    {
      key: 'diagnosisFinal',
      title: '最终诊断',
      align: 'center' as const,
      minWidth: 120
    },
    {
      key: 'riskName',
      title: '危险度',
      align: 'center' as const,
      width: 100
    },
    {
      key: 'projectName',
      title: '关联试验',
      align: 'center' as const,
      minWidth: 120
    },
    {
      key: 'createTime',
      title: '创建时间',
      align: 'center' as const,
      width: 120
    },
    {
      key: 'updateTime',
      title: '更新时间',
      align: 'center' as const,
      width: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center' as const,
      width: 280,
      render: (row: any) => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            {$t('common.edit')}
          </NButton>
          <NButton v-show={row.groupStatus === 1} type="primary" ghost size="small" onClick={() => handleQuit(row)}>
            出组
          </NButton>
          <NButton v-show={row.groupStatus !== 1} type="primary" ghost size="small" onClick={() => handleInGroup(row)}>
            入组
          </NButton>

          <NButton
            v-show={row.sbagFlag === 1}
            type="primary"
            ghost
            size="small"
            onClick={() =>
              router.push({
                name: 'data-base_patient-bag',
                params: { id: row.id }
              })
            }
          >
            专科袋
          </NButton>
          <NButton v-show={row.sbagFlag !== 1} type="primary" ghost size="small" onClick={() => startTreatment(row.id)}>
            启动治疗
          </NButton>
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onDeleted: _onDeleted
} = useTableOperate(data, getData);

const bagStore = useBagStore();
const treatmentModalRef = ref<InstanceType<typeof StartTreatmentModal>>();
const inGroupModelRef = ref<InstanceType<typeof InGroupModel>>();
const outGroupModelRef = ref<InstanceType<typeof OutGroupModel>>();

function edit(id: string | undefined) {
  handleEdit(id);
}

function startTreatment(id: string | undefined) {
  if (id) {
    bagStore.addingModel.patientId = id;
    treatmentModalRef.value?.show();
  }
}

const patientId = ref('');
const projectHospitalId = ref('');

async function handleQuit(row: Api.Patient.PatientItem) {
  patientId.value = row.id || '';
  projectHospitalId.value = row.hospitalId || '';
  outGroupModelRef.value?.show();
}

async function handleInGroup(row: Api.Patient.PatientItem) {
  patientId.value = row.id || '';
  projectHospitalId.value = row.hospitalId || '';
  inGroupModelRef.value?.show();
}

function handleGenerateSuccess() {
  getData();
}

// 处理搜索事件
function handleSearch(value: string) {
  searchParams.name = value || undefined;
  getDataByPage();
}

// 处理筛选变化事件
function handleFilterChange(filterParams: Partial<Api.Patient.CommonSearchParams>) {
  Object.assign(searchParams, filterParams);
  getDataByPage();
}
</script>

<template>
  <div class="flex-col-stretch flex-1 overflow-hidden lt-sm:overflow-auto">
    <NCard
      :bordered="false"
      size="small"
      class="data-common-table sm:flex-1-hidden"
      content-style="padding:16px 24px;display:flex;flex-direction:column;"
    >
      <TableHeaderOperation
        :loading="loading"
        search-placeholder="请输入患者姓名"
        @add="handleAdd"
        @search="handleSearch"
      >
        <template #filters>
          <PatientSearch
            :search-params="searchParams as Api.Patient.CommonSearchParams"
            @filter-change="handleFilterChange"
          />
        </template>
      </TableHeaderOperation>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <PatientOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
    <InGroupModel
      ref="inGroupModelRef"
      :patient-id="patientId"
      :project-hospital-id="projectHospitalId"
      @submitted="getDataByPage"
    />
    <OutGroupModel
      ref="outGroupModelRef"
      :project-hospital-id="projectHospitalId"
      :patient-id="patientId"
      @submitted="getDataByPage"
    />

    <StartTreatmentModal ref="treatmentModalRef" @submitted="handleGenerateSuccess" />
  </div>
</template>

<style scoped></style>
