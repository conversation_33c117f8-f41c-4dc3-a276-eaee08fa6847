<script setup lang="ts">
// import what you need
import { BlitzForm, BlitzTable } from 'blitzar';
// include Blitzar's CSS (kept at minimal and doesn't pollute global scope)
import 'blitzar/dist/style.css';

import { computed, reactive } from 'vue';

const jsonData = defineModel<any>('jsonData', { required: true, type: Object });

// const jsonData[0].formData = reactive({});

function autoFocusInput(mouseEvent: any) {
  const cellEl = mouseEvent.srcElement.parentElement;
  setTimeout(() => {
    const inputEl = cellEl.querySelector('input');
    if (inputEl) inputEl.focus();
  }, 0);
}

const editInfo: any = reactive({ editingColId: '', editingRowId: '', lastEdit: null });

function onUpdateCell({ rowId, colId, value }: any) {
  editInfo.lastEdit = { rowId, colId, value };
}

function onCellDblclick(mouseEvent: any, rowData: any, colId: any) {
  editInfo.editingColId = colId;
  editInfo.editingRowId = rowData.id;
  // auto focus logic:
  autoFocusInput(mouseEvent);
}

function stopEditing() {
  editInfo.editingRowId = '';
  editInfo.editingColId = '';
  editInfo.lastEdit = null;
}

function saveLastEdit() {
  if (!editInfo.lastEdit) return stopEditing();

  const { rowId, colId, value } = editInfo.lastEdit;

  const row = jsonData.value.schemaAll[1].rows?.find((r: any) => r.id === rowId);
  if (!row) return stopEditing();

  // 使用类型断言来避免 any 类型错误
  (row as Record<string, any>)[colId] = value;

  return stopEditing();
}

const schemaWithEditingLogic = computed(() => {
  // return the columns with the added logic to edit & save data
  return jsonData.value.schemaAll[1].schemaColumns?.map((blueprint: any) => {
    if (blueprint.component === 'div')
      return {
        ...blueprint,
        events: { dblclick: (e: any) => onCellDblclick(e, jsonData.value.schemaAll[1].rows, blueprint.id) }
      };
    return {
      ...blueprint,
      /**
       * The editing logic for every schema blueprint is dynamically setting the "mode" of a cell. It does this based on
       * the colId and rowId which are being edited.
       */
      dynamicProps: ['mode'],
      mode: (_val: any, { formData: formDataM }: any) =>
        editInfo.editingColId === blueprint.id && editInfo.editingRowId === formDataM.id ? 'edit' : 'raw',
      events: {
        keydown: (event: any) => {
          if (event.code === 'Enter') saveLastEdit();
          if (event.code === 'Escape') stopEditing();
        },
        blur: () => saveLastEdit()
      }
    };
  });
});
</script>

<template>
  <div v-for="item in jsonData.schemaAll" :key="item.type">
    <div v-if="item.type === 'form'">
      <BlitzForm
        v-model="item.formData"
        class="bform"
        :schema="item.schema as any"
        :column-count="4"
        label-position="left"
      />
    </div>
    <div v-if="item.type === 'table'">
      <BlitzTable
        class="mt-6"
        :schema-columns="schemaWithEditingLogic as any"
        :rows="item.rows as any"
        :title-field="item.titleT"
        @update-cell="({ rowId, colId, value }) => onUpdateCell({ rowId, colId, value })"
        @cell-dblclick="(mouseEvent, rowData, colId) => onCellDblclick(mouseEvent, rowData, colId)"
      />
    </div>
  </div>
</template>

<style scoped>
:deep(.bform .blitz-field__component) {
  border: 1px solid #c9c8c8;
  border-radius: 4px;
}
:deep(.bform .blitz-field__label, .bform .blitz-field__sub-label) {
  width: 80px;
}
:deep(.btable .blitz-table--grid-card) {
  border: thin solid #dfe2e5;
}
:deep(.btable .blitz-table--grid-card input) {
  min-width: 0;
}
:deep(.blitz-field__label) {
  font-size: 14px;
}
:deep(._sort-arrows) {
  display: none;
}
:deep(table) {
  border-collapse: collapse;
  border-spacing: 2px;
  border: 1px solid #dfe2e5;
  border-color: gray !important;
}
:deep(th),
:deep(td) {
  border: 1px solid #dfe2e5;
  padding: 0.6em 1em;
  text-align: center;
}
</style>
