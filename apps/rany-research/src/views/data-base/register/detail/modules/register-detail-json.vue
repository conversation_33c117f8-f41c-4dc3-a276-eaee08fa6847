<script setup lang="ts">
import { computed, ref } from 'vue';
import { NButton, NSpace } from 'naive-ui';
import JsonEditorVue from 'json-editor-vue3';
import { fetchPostRegisterPageUpdate } from '@/service/api/register';

const editMode = ref(false);
const originalJson = ref<any>(null); // 保存原始数据用于取消时还原
const jsonEditorRef = ref(); // 添加编辑器实例引用

const jsonData = defineModel<any>('jsonData', { required: true, type: Object });

const props = defineProps<{
  jsonHeight: number;
  registerId: string;
}>();

const emit = defineEmits(['update:data']);

const handleEdit = () => {
  originalJson.value = JSON.parse(JSON.stringify(jsonData.value));
  editMode.value = true;
};

const handleSave = async () => {
  try {
    editMode.value = false;
    const res = await fetchPostRegisterPageUpdate({
      id: props.registerId,
      template: jsonData.value,
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      userName: 'admin'
    });
    if (res.response.data.status === 'OK') {
      window.$message?.success?.('保存成功');
      emit('update:data', props.registerId);
      originalJson.value = JSON.parse(JSON.stringify(jsonData.value));
    } else {
      window.$message?.error?.('保存失败');
      jsonData.value = JSON.parse(JSON.stringify(originalJson.value));
    }
  } catch {
    window.$message?.error?.('保存失败,请重试');
  }
};
const currentMode = computed(() => {
  return editMode.value ? 'code' : 'view';
});
const handleCancel = () => {
  jsonData.value = JSON.parse(JSON.stringify(originalJson.value));
  editMode.value = false;
};

const handleValidationError = (_editor: any, errors: any[]) => {
  if (errors?.length) {
    console.log(errors);
    // window.$message?.error?.('JSON格式错误,请检查');
  }
};

// JSON编辑器配置
const editorOptions = {
  mainMenuBar: true, // 隐藏主菜单栏
  navigationBar: false, // 隐藏导航栏
  statusBar: false, // 隐藏状态栏
  colorPicker: true, // 启用颜色选择器
  language: 'zh-CN', // 设置中文语言
  indentation: 2 // 缩进空格数
};
</script>

<template>
  <div class="flex flex-col">
    <div class="mb-2 flex justify-end">
      <NSpace>
        <template v-if="editMode">
          <NButton size="small" type="primary" @click="handleSave">保存</NButton>
          <NButton size="small" @click="handleCancel">取消</NButton>
        </template>
        <template v-else>
          <NButton size="small" type="primary" @click="handleEdit">编辑</NButton>
        </template>
      </NSpace>
    </div>

    <JsonEditorVue
      ref="jsonEditorRef"
      :key="currentMode"
      v-model="jsonData"
      :style="{
        height: `${props.jsonHeight}px`,
        border: '1px solid #e5e7eb',
        borderRadius: '4px'
      }"
      class="json-editor"
      :current-mode="currentMode"
      :options="editorOptions"
      :mode-list="['code', 'view']"
      @validation-error="handleValidationError"
    />
  </div>
</template>

<style scoped>
.json-editor :deep(.jsoneditor) {
  border: none;
  border-radius: 4px;
}

.json-editor :deep(.jsoneditor-outer) {
  padding: 0;
  margin: 0;
  border: none;
}

.json-editor :deep(.ace-jsoneditor) {
  min-height: 200px;
}

.json-editor :deep(.ace_editor) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.json-editor :deep(.ace_content) {
  background-color: #ffffff;
}

.json-editor :deep(.ace_gutter) {
  background-color: #f9fafb;
}
</style>
