<script setup lang="ts">
import { ref } from 'vue';
import RegisterOperateModal from '@/views/data-base/register/table/modules/register-operate-modal.vue';

// 类型定义
export type OperateType = NaiveUI.TableOperateType;

// props & emits
const props = defineProps<{
  registerInfo: Api.Register.Register;
}>();

const emit = defineEmits<{
  (e: 'update:registerInfo'): void;
}>();

// 状态管理
const visible = ref(false);
const operateType = ref<OperateType>('edit');
const editingData = ref<Api.Register.Register>({});

// 打开新建弹窗
function handleCreate() {
  operateType.value = 'edit';
  visible.value = true;
  editingData.value = { name: props.registerInfo.name, id: props.registerInfo.id };
}
// 刷新列表
</script>

<template>
  <div>
    <div class="flex items-center justify-between gap-4">
      <div class="flex items-center gap-4">
        <!-- 返回 -->
        <NButton size="small" @click="$router.go(-1)">返回</NButton>
        <span>{{ props.registerInfo.name || 'xxx登记首页（未上传）' }}</span>
        <NButton text @click="handleCreate">
          <SvgIcon icon="solar:pen-2-linear" />
        </NButton>
      </div>
      <div class="flex items-center gap-2">
        <span class="text-gray-500">创建时间：</span>
        <span>{{ props.registerInfo.createTime }}</span>
        <span class="text-gray-500">更新时间：</span>
        <span>{{ props.registerInfo.updateTime }}</span>
      </div>
    </div>
    <RegisterOperateModal
      v-model:visible="visible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="() => emit('update:registerInfo')"
    />
  </div>
</template>

<style scoped></style>
