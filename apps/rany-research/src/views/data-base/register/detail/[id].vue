<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import { fetchGetRegisterPageDetail } from '@/service/api';
import JsonView from './modules/register-detail-json.vue';
import RegisterDetailFormBlitzar from './modules/register-detail-form-blitzar.vue';
import ExpSettingRegisterFile from './modules/exp-setting-register-file.vue';

const route = useRoute();
const jsonHeight = ref<number>(0);

const updateHeight = () => {
  const divElement = document.getElementById('jsonForm');
  if (!divElement) return;
  jsonHeight.value = divElement?.getBoundingClientRect().height - 80;
};
const registerInfo = ref<Api.Register.Register>({});
const getRegisterInfo = async () => {
  // 通过接口获取详情数据
  const { data } = await fetchGetRegisterPageDetail(route.params.id as string);

  registerInfo.value = data!;
  if (!data?.template) return;
  registerInfo.value.template = JSON.parse(String(data?.template));
};

onMounted(() => {
  setTimeout(() => {
    updateHeight();
  }, 1000);
  getRegisterInfo();
  window.addEventListener('resize', updateHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateHeight);
});
</script>

<template>
  <div>
    <NCard :bordered="false" size="small" class="mb-2 overflow-hidden card-wrapper">
      <ExpSettingRegisterFile :register-info="registerInfo" @update:register-info="getRegisterInfo()" />
    </NCard>
    <div class="flex">
      <NCard v-if="registerInfo.template" id="jsonForm" :bordered="false" size="small" class="mr-2 flex-[3]">
        <RegisterDetailFormBlitzar v-if="registerInfo.template" :json-data="registerInfo.template" />
        <NResult v-else class="h-" status="418" title="暂无数据" description="请先上传文件"></NResult>
      </NCard>
      <NCard v-if="registerInfo.template" :bordered="false" size="small" class="flex-[1] overflow-hidden card-wrapper">
        <JsonView
          v-model:json-data="registerInfo.template"
          :json-height="jsonHeight"
          :register-id="registerInfo.id!"
          @update:data="getRegisterInfo"
        />
      </NCard>
      <NCard v-else :bordered="false" size="small" class="flex items-center justify-center">
        <NEmpty description="请先上传json文件" />
      </NCard>
    </div>
  </div>
</template>

<style scoped>
:deep(.n-card__content) {
  flex: 0;
}
</style>
