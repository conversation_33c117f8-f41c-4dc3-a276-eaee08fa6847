<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import type { SelectOption } from 'naive-ui';
import { registerTypeOptions } from '@/constants/business';
import { translateOptions } from '@/utils/common';

interface Props {
  searchParams?: Api.Register.RegisterSearchParams;
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({
    pageNo: 1,
    pageSize: 10,
    status: null,
    name: ''
  })
});

interface Emits {
  (e: 'filter-change', params: Partial<Api.Register.RegisterSearchParams>): void;
}

const emit = defineEmits<Emits>();

// 状态选择器选项
const statusOptions = ref<SelectOption[]>([]);
const selectedStatus = ref<number | null>(props.searchParams?.status || null);

// 初始化状态选项
function initStatusOptions() {
  statusOptions.value = translateOptions(registerTypeOptions);
}

// 处理状态筛选变化
const handleStatusChange = (value: number | null) => {
  selectedStatus.value = value;
  emit('filter-change', { status: value });
};

// 监听外部搜索参数变化
watch(
  () => props.searchParams?.status,
  newValue => {
    selectedStatus.value = newValue || null;
  }
);

onMounted(() => {
  initStatusOptions();
});
</script>

<template>
  <div class="flex gap-16px">
    <div class="filter-item">
      <div class="filter-label">状态：</div>
      <div class="filter-control">
        <NSelect
          v-model:value="selectedStatus"
          class="filter-select"
          placeholder="请选择状态"
          :options="statusOptions"
          clearable
          @update:value="handleStatusChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  white-space: nowrap;
  font-size: 14px;
  color: var(--text-color-2);
}

.filter-select {
  min-width: 120px;
}
</style>
