<script setup lang="tsx">
import { NButton, NForm, NFormItem, NInput, NSpace } from 'naive-ui';
import { computed, reactive, watch } from 'vue';
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
import { fetchGetRegisterPageDetail, fetchPostRegisterPageAdd, fetchPostRegisterPageUpdate } from '@/service/api';

export type OperateType = NaiveUI.TableOperateType;

interface Props {
  /** 操作类型 */
  operateType: OperateType;
  /** 编辑时的行数据 */
  rowData?: Api.Register.Register | null;
}

interface Emits {
  (e: 'submitted'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();

const title = computed(() => {
  return props.operateType === 'add' ? '新增登记首页' : '编辑登记首页';
});

const formModel = reactive<Api.Register.Register>({
  name: '',
  filePath: '',
  status: 1
});

const rules = {
  name: {
    required: true,
    message: '请输入登记首页名称',
    trigger: ['blur', 'input']
  }
};

async function handleSubmit() {
  try {
    await validate();

    const params = {
      ...formModel,
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      userName: 'admin'
    };

    if (props.operateType === 'edit') {
      await fetchPostRegisterPageUpdate({
        ...params,
        id: props.rowData?.id
      });
      window.$message?.success($t('common.updateSuccess'));
    } else {
      await fetchPostRegisterPageAdd(params);
      window.$message?.success($t('common.addSuccess'));
    }

    closeDrawer();
    emit('submitted');
  } catch (error) {
    console.error('提交失败:', error);
    window.$message?.error('提交失败,请重试!');
  }
}

async function initFormData() {
  // 重置表单
  Object.assign(formModel, {
    name: '',
    filePath: '',
    status: 1
  });

  if (props.operateType === 'edit' && props.rowData?.id) {
    try {
      const { data } = await fetchGetRegisterPageDetail(props.rowData.id);
      formModel.name = data?.name ?? '';
      formModel.id = data?.id ?? '';
    } catch (error) {
      console.error('获取详情失败:', error);
      window.$message?.error('获取详情失败');
      closeDrawer();
    }
  }
}

function closeDrawer() {
  visible.value = false;
}

watch(
  visible,
  newVal => {
    if (newVal) {
      initFormData();
      restoreValidation();
    }
  },
  { immediate: true }
);
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-800px">
    <NScrollbar class="h-300px pr-20px">
      <NForm ref="formRef" :model="formModel" :rules="rules" label-placement="left" label-width="80">
        <div class="grid grid-cols-1 gap-4">
          <NFormItem label="名称" path="name" required>
            <NInput v-model:value="formModel.name" placeholder="请输入登记首页名称" />
          </NFormItem>
          <NFormItem label="首页json" path="filePath">
            <JsonUpload v-model:value="formModel.filePath" :operate-type="props.operateType" />
          </NFormItem>
        </div>
      </NForm>
    </NScrollbar>
    <template #footer>
      <NSpace justify="end" :size="16">
        <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
        <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
