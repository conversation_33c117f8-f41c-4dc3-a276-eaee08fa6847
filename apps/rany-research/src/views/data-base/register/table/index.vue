<script setup lang="tsx">
import { ref } from 'vue';
import type { Ref } from 'vue';
import { useRouter } from 'vue-router';
import { NButton, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { useTabsStore } from 'component-library';
import { fetchPostRegisterList, fetchPostRegisterPageDelete } from '@/service/api';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { registerTypeRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import RegisterSearch from './modules/register-search.vue';
import RegisterOperateModal from './modules/register-operate-modal.vue';

export type OperateType = NaiveUI.TableOperateType;

const { bool: visible, setTrue: openModal } = useBoolean();

const appStore = useAppStore();
const router = useRouter();
const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination, searchParams } = useTable({
  apiFn: fetchPostRegisterList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    status: null,
    name: ''
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: $t('page.database.register.table.name'),
      align: 'center',
      width: 100,
      render: row => {
        return (
          <NButton
            text
            type="primary"
            onClick={() => {
              useTabsStore().addTab({
                title: row.name,
                path: `/data-base/register/detail/${row.id}`,
                query: {
                  id: row.id
                },
                type: 'rany-research',
                closable: true,
                label: row.name,
                id: row.id
              });
              router.push(`/data-base/register/detail/${row.id}`);
            }}
          >
            {row.name}
          </NButton>
        );
      }
    },
    {
      key: 'projectName',
      title: $t('page.database.register.table.project'),
      align: 'center',
      width: 180
    },
    {
      key: 'createTime',
      title: $t('page.database.register.table.createTime'),
      align: 'center',
      width: 180
    },
    {
      key: 'updateTime',
      title: $t('page.database.register.table.updateTime'),
      align: 'center',
      width: 180
    },
    {
      key: 'status',
      title: $t('page.database.register.table.status'),
      align: 'center',
      render: row => {
        if (row.status === null || row.status === undefined) {
          return null;
        }

        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        // 检查status是否为有效值
        if (!(row.status in registerTypeRecord)) {
          return null;
        }

        const label = $t(registerTypeRecord[row.status as Api.Common.EnableStatus]);

        // 检查tagMap中是否存在对应的type
        const type = tagMap[row.status as Api.Common.EnableStatus];
        if (!type) {
          return null;
        }

        return <NTag type={type}>{label}</NTag>;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 230,
      render: row => (
        <div class="flex-center justify-end gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row)}>
            {$t('common.edit')}
          </NButton>

          <NButton
            type="error"
            ghost
            size="small"
            onClick={() => {
              window.$dialog?.error({
                title: '提示',
                content: '确定要删除该登记吗?',
                positiveText: '确定',
                negativeText: '取消',
                onPositiveClick: () => handleDelete(row.id!)
              });
            }}
          >
            {$t('common.delete')}
          </NButton>
        </div>
      )
    }
  ]
});

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  onBatchDeleted();
}

const operateType = ref<OperateType>('add');

function handleAdd() {
  operateType.value = 'add';
  openModal();
}
/** the edit menu data or the parent menu data when adding a child menu */
const editingData: Ref<Api.Register.Register | null> = ref(null);

function edit(item: Api.Register.Register) {
  operateType.value = 'edit';
  editingData.value = { ...item };
  openModal();
}

async function handleDelete(id: string) {
  await fetchPostRegisterPageDelete(id);
  getDataByPage();
}

// 处理搜索事件
function handleSearch(value: string) {
  searchParams.name = value || '';
  getDataByPage();
}

// 处理筛选变化事件
function handleFilterChange(filterParams: Partial<Api.Register.RegisterSearchParams>) {
  Object.assign(searchParams, filterParams);
  getDataByPage();
}
</script>

<template>
  <div class="flex-col-stretch flex-1 overflow-hidden lt-sm:overflow-auto">
    <NCard
      :bordered="false"
      size="small"
      class="data-common-table sm:flex-1-hidden"
      content-style="padding:16px 24px;display:flex;flex-direction:column;"
    >
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :disabled-delete="checkedRowKeys.length === 0"
        :loading="loading"
        search-placeholder="请输入登记名称"
        @add="handleAdd"
        @delete="handleBatchDelete"
        @refresh="getData"
        @search="handleSearch"
      >
        <template #filters>
          <RegisterSearch :search-params="searchParams" @filter-change="handleFilterChange" />
        </template>
      </TableHeaderOperation>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <RegisterOperateModal
        v-model:visible="visible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
