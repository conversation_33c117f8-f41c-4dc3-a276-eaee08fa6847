<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import type { SelectOption } from 'naive-ui';
import { fetchGetDiseaseList } from '@/service/api/disease';

interface Props {
  searchParams?: Api.Disease.DiseaseSearchParams;
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({
    pageNo: 1,
    pageSize: 10,
    name: null,
    parentId: null
  })
});

interface Emits {
  (e: 'filter-change', params: Partial<Api.Disease.DiseaseSearchParams>): void;
}

const emit = defineEmits<Emits>();

// 父级选择器选项
const parentOptions = ref<SelectOption[]>([]);
const selectedParentId = ref<string | null>(props.searchParams?.parentId || null);

// 获取父级选项
async function getParentListOptions() {
  if (parentOptions.value.length > 0) return;

  const { error, data } = await fetchGetDiseaseList({
    pageSize: 1000,
    pageNo: 1
  });

  if (!error) {
    const options = data.records.map((item: any) => ({
      label: item.shortName,
      value: item.id
    }));

    parentOptions.value = [
      {
        label: '顶级',
        value: '-1'
      },
      ...options
    ];
  }
}

// 处理父级筛选变化
const handleParentChange = (value: string | null) => {
  selectedParentId.value = value;
  emit('filter-change', { parentId: value });
};

// 监听外部搜索参数变化
watch(
  () => props.searchParams?.parentId,
  newValue => {
    selectedParentId.value = newValue || null;
  }
);

onMounted(() => {
  getParentListOptions();
});
</script>

<template>
  <div class="filter-item">
    <div class="filter-label">父级简称：</div>
    <div class="filter-control">
      <NSelect
        v-model:value="selectedParentId"
        class="filter-select"
        placeholder="请选择父级"
        :options="parentOptions"
        clearable
        @update:value="handleParentChange"
      />
    </div>
  </div>
</template>

<style scoped></style>
