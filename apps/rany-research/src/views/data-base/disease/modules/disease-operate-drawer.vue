<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchGetDiseaseList, fetchPostDiseaseAdd, fetchPutDiseaseEdit } from '@/service/api/disease';
import { $t } from '@/locales';

defineOptions({
  name: 'UserOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Disease.Disease | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: $t('page.database.disease.add'),
    edit: $t('page.database.disease.edit')
  };
  return titles[props.operateType];
});

type Model = Pick<
  Api.Disease.Disease,
  'shortName' | 'name' | 'parentId' | 'enName' | 'abbrName' | 'alias' | 'idcCode' | 'status'
>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    shortName: '',
    name: '',
    parentId: null,
    enName: '',
    abbrName: '',
    alias: '',
    idcCode: null,
    status: 1
  };
}

type RuleKey = Extract<keyof Model, 'shortName' | 'name' | 'parentId'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  shortName: defaultRequiredRule,
  name: defaultRequiredRule,
  parentId: defaultRequiredRule
};

/** the enabled role options */
const roleOptions = ref<CommonType.Option<string>[]>([]);

async function getParentListOptions() {
  const { error, data } = await fetchGetDiseaseList({
    pageSize: 1000,
    pageNo: 1
  });

  if (!error) {
    const options = data.records.map((item: any) => ({
      label: item.shortName,
      value: item.id
    }));

    // the mock data does not have the roleCode, so fill it
    // if the real request, remove the following code
    // const userRoleOptions = model.userRoles.map(item => ({
    //   label: item,
    //   value: item
    // }));
    // end

    roleOptions.value = [
      {
        label: '顶级',
        value: '-1'
      },
      ...options
    ];
  }
}

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

const idcCodeOptions = ref<CommonType.Option<string>[]>([
  // 模拟icd编码数据
  {
    label: 'A01',
    value: 'A01'
  },
  {
    label: 'A02',
    value: 'A02'
  },
  {
    label: 'A03',
    value: 'A03'
  }
]);

const loadingRef = ref(false);
const filteredOptions = ref<CommonType.Option<string>[]>([]);

const handleSearch = (query: string) => {
  if (!query.length) {
    filteredOptions.value = [];
    return;
  }
  loadingRef.value = true;
  filteredOptions.value = idcCodeOptions.value.filter(item => item.label.includes(query));
  loadingRef.value = false;
};

async function handleSubmit() {
  await validate();
  const submitData = { ...model };

  let res: any;
  if (props.operateType === 'edit') {
    // 编辑逻辑
    res = await fetchPutDiseaseEdit({
      ...submitData,
      id: props.rowData?.id,
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      userName: 'admin'
    });
  } else {
    // 添加逻辑
    res = await fetchPostDiseaseAdd({
      ...model,
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      userName: 'admin'
    });
  }

  if (res.error) {
    window.$message?.error(res.data.message);
  } else {
    window.$message?.success(props.operateType === 'edit' ? '编辑成功' : '添加成功');
  }

  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
    getParentListOptions();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem :label="$t('page.database.disease.shortName')" path="shortName">
          <NInput v-model:value="model.shortName" maxlength="50" placeholder="请输入不超过50字的简称" />
        </NFormItem>
        <NFormItem :label="$t('page.database.disease.name')" path="name">
          <NInput v-model:value="model.name" maxlength="50" placeholder="请输入不超过50字的名称" />
        </NFormItem>
        <NFormItem :label="$t('page.database.disease.parentId')" path="parentId">
          <NSelect
            v-model:value="model.parentId"
            :options="roleOptions"
            :placeholder="$t('page.database.disease.parentId')"
          />
        </NFormItem>
        <NFormItem :label="$t('page.database.disease.enName')" path="enName">
          <NInput v-model:value="model.enName" :placeholder="$t('page.database.disease.enName')" />
        </NFormItem>
        <NFormItem :label="$t('page.database.disease.abbrName')" path="abbrName">
          <NInput v-model:value="model.abbrName" :placeholder="$t('page.database.disease.abbrName')" />
        </NFormItem>
        <NFormItem :label="$t('page.database.disease.alias')" path="alias">
          <NInput v-model:value="model.alias" :placeholder="$t('page.database.disease.alias')" />
        </NFormItem>
        <NFormItem label="关联ICD疾病编码" path="idcCode">
          <NSelect
            v-model:value="model.idcCode"
            :placeholder="$t('page.database.disease.idcCode')"
            :options="filteredOptions"
            :loading="loadingRef"
            filterable
            clearable
            remote
            @search="handleSearch"
          >
            <template #suffix>
              <SvgIcon icon="solar:rounded-magnifer-linear" />
            </template>
          </NSelect>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
