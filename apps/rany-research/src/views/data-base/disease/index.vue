<script setup lang="tsx">
import { ref } from 'vue';
import { <PERSON><PERSON>utton, NTag, useDialog } from 'naive-ui';
import { fetchDeleteDisease, fetchGetDiseaseList } from '@/service/api/disease';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import DiseaseOperateDrawer from './modules/disease-operate-drawer.vue';
import DiseaseSearch from './modules/disease-search.vue';

const appStore = useAppStore();

const dialog = useDialog();

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams: _resetSearchParams
} = useTable({
  apiFn: fetchGetDiseaseList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    name: null,
    parentId: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'shortName',
      title: $t('page.database.disease.shortName'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'name',
      title: $t('page.database.disease.name'),
      align: 'center',
      minWidth: 100
    },
    {
      key: 'enName',
      title: $t('page.database.disease.enName'),
      align: 'center',
      width: 100
    },
    {
      key: 'parentId',
      title: $t('page.database.disease.parentId'),
      align: 'center',
      minWidth: 100,
      render: row => {
        if (row.parentId === '-1') {
          return <NTag>{'顶级'}</NTag>;
        }
        const name = getDiseaseNameById(row.parentId!);
        return <NTag>{name}</NTag>;
      }
    },
    {
      key: 'abbrName',
      title: $t('page.database.disease.abbrName'),
      align: 'center',
      width: 120
    },
    {
      key: 'idcCode',
      title: $t('page.database.disease.idcCode'),
      align: 'center',
      minWidth: 200
    },
    {
      key: 'alias',
      title: $t('page.database.disease.alias'),
      align: 'center',
      width: 100
    },
    {
      key: 'createTime',
      title: $t('page.database.disease.createTime'),
      align: 'center',
      width: 100
    },
    {
      key: 'updateTime',
      title: $t('page.database.disease.updateTime'),
      align: 'center',
      width: 100
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            {$t('common.edit')}
          </NButton>
          <NButton type="error" ghost size="small" onClick={() => handleDelete(row.id)}>
            {$t('common.delete')}
          </NButton>
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted: _onBatchDeleted,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData);

const parentShortNameOptions = ref<{ label: string; value: string }[]>([]);

async function getParentShortNameOptions() {
  const res = await fetchGetDiseaseList({ pageNo: 1, pageSize: 1000 });
  parentShortNameOptions.value =
    res.data?.records?.map(item => ({
      label: item.shortName ?? '',
      value: item.id ?? ''
    })) ?? [];
}
getParentShortNameOptions();

function getDiseaseNameById(id: string | undefined) {
  const disease = parentShortNameOptions.value.find(item => item.value === id);
  return disease?.label || '';
}

async function handleDelete(id: string | undefined) {
  dialog.create({
    title: '删除',
    content: `您确定要删除吗？
    删除后历史记录不可查询！`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      if (id) {
        const res = await fetchDeleteDisease(id);
        if (res.data?.status === 'OK') {
          window.$message?.success('删除成功');
          onDeleted();
        }
      }
    }
  });
}

function edit(id: string | undefined) {
  handleEdit(id);
}

// 处理搜索事件
function handleSearch(value: string) {
  searchParams.name = value || null;
  getDataByPage();
}

// 处理筛选变化事件
function handleFilterChange(filterParams: Partial<Api.Disease.DiseaseSearchParams>) {
  Object.assign(searchParams, filterParams);
  getDataByPage();
}
</script>

<template>
  <div class="flex-col-stretch flex-1 overflow-hidden lt-sm:overflow-auto">
    <NCard
      :bordered="false"
      size="small"
      class="data-common-table sm:flex-1-hidden"
      content-style="padding:16px 24px;display:flex;flex-direction:column;"
    >
      <TableHeaderOperation
        :loading="loading"
        :search-placeholder="$t('page.database.form.placeholder.name')"
        @add="handleAdd"
        @search="handleSearch"
      >
        <template #filters>
          <DiseaseSearch
            :search-params="searchParams as Api.Disease.DiseaseSearchParams"
            @filter-change="handleFilterChange"
          />
        </template>
      </TableHeaderOperation>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <DiseaseOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
