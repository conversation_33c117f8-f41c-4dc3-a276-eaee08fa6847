<script setup lang="tsx">
import type { CascaderOption } from 'naive-ui';
import { NButton, NDataTable, NForm, NFormItem, NInput, NInputNumber, NSelect, NSpace } from 'naive-ui';
import { computed, reactive, ref, watch } from 'vue';
import { $t } from '@/locales';
import { useSuiteStore } from '@/store/modules/database/suite';
import { useDiseaseStore } from '@/store/modules/database/disease';
import { usePlatformStore } from '@/store/modules/platform';
import { useNaiveForm } from '@/hooks/common/form';
import { fetchGetSuiteItemDel } from '@/service/api';
import SvgIcon from '@/components/custom/svg-icon.vue';
import LabourCascader from '@/components/advanced/program-table/item-value/labour-cascader.vue';
import ExaminationCascader from '@/components/advanced/program-table/item-value/examination-cascader.vue';
import OperationCascader from '@/components/advanced/program-table/item-value/operation-cascader.vue';

const suiteStore = useSuiteStore();
const { loading: suiteLoading, options: suiteOptions, load: loadSuite } = suiteStore.getSuitOptions();
const diseaseStore = useDiseaseStore();

type TableColumn<T> = NaiveUI.TableColumn<T>;
export type OperateType = NaiveUI.TableOperateType;

interface Props {
  operateType: OperateType;
  rowData?: Api.Suite.Suite | null;
}

interface DetailItem {
  unit: any;
  singleDose: any;
  id?: string;
  orderNumber: number;
  name: string | null;
  type: number | null;
  comment?: string;
  isEditing: boolean;
  isNew?: boolean;
}

interface Emits {
  (e: 'submitted'): void;
}

const platformStore = usePlatformStore();
const {
  loading: medicineLoading,
  options: medicineOptions,
  load: loadMedicine
} = platformStore.getMedicineDictOptions();

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: $t('page.database.suite.add'),
    edit: $t('page.database.suite.edit')
  };
  return titles[props.operateType];
});

// 表单相关数据
const typeOptions: any = ref([]);
const diseaseOptions = ref<Api.Common.CommonOption[]>([]);
const detailData = ref<DetailItem[]>([]);
const checkedRowKeys = ref<number[]>([]);
const suiteId = ref<string>('');

const formModel = reactive({
  basicInfo: createDefaultModel()
});

const unitOptions = [
  { label: 'g', value: 'g' },
  { label: 'mg', value: 'mg' },
  { label: 'μg', value: 'μg' },
  { label: 'ml', value: 'ml' },
  { label: 'L', value: 'L' },
  { label: 'IU', value: 'IU' },
  { label: 'U', value: 'U' },
  { label: 'mg/㎡', value: 'mg/㎡' },
  { label: 'g/㎡', value: 'g/㎡' },
  { label: 'U/㎡', value: 'U/㎡' },
  { label: 'mg/㎡/day', value: 'mg/㎡/day' },
  { label: '次', value: '次' }
];

// 表格列配置
const detailColumns = [
  {
    type: 'selection'
  },
  {
    title: '',
    width: 40,
    render: (row: DetailItem) => {
      if (row.isEditing) {
        return (
          <NButton text class="h-full w-full flex items-center justify-center" onClick={() => handleDelete(row)}>
            <SvgIcon icon="solar:minus-circle-linear" class="text-icon text-red-500" />
          </NButton>
        );
      }
      return null;
    }
  },
  {
    title: '组',
    key: 'orderNumber',
    width: 80
  },
  {
    title() {
      return (
        <div>
          <span class="text-red-500">*</span>
          <span>项目类别</span>
        </div>
      );
    },
    key: 'type',
    render: (row: DetailItem) => {
      if (row.isEditing) {
        return (
          <NSelect
            v-model:value={row.type}
            options={typeOptions.value}
            onFocus={getItemType}
            placeholder="请选择项目类别"
            on-change={() => {
              row.name = null;
            }}
          />
        );
      }
      return typeOptions.value.find((opt: any) => opt.value === row.type)?.label;
    },
    width: 240
  },
  {
    title() {
      return (
        <div>
          <span class="text-red-500">*</span>
          <span>项目名称</span>
        </div>
      );
    },
    key: 'name',
    width: 360,
    render: (row: DetailItem) => {
      if (row.isEditing) {
        return (
          <div>
            <NSelect
              v-show={row.type === 3}
              v-model:value={row.name}
              filterable
              loading={medicineLoading.value}
              options={medicineOptions.value}
              placeholder="请选择西成药"
              class="w-full"
              on-update:value={(_: any, option: CascaderOption) => {
                row.name = option.label?.toString() ?? '';
              }}
              onFocus={loadMedicine}
            >
              {({ option }: any) => <span>{option.label}</span>}
            </NSelect>
            <NSelect
              v-show={row.type === 5}
              v-model:value={row.name}
              filterable
              loading={suiteLoading.value}
              options={suiteOptions.value}
              placeholder="请选择组套项目"
              class="w-full"
              on-update:value={(_: any, option: CascaderOption) => {
                row.name = option.label?.toString() ?? '';
              }}
              on-focus={loadSuite}
            >
              {({ option }: any) => <span>{option.label}</span>}
            </NSelect>

            <LabourCascader
              v-show={row.type === 1}
              v-model:value={row.name}
              editMode={false}
              on-update:value={(_: any, _option: CascaderOption, pathValues: Array<CascaderOption | null>) => {
                row.name = pathValues.map(p => p?.label).join('/');
              }}
            />
            <ExaminationCascader
              v-show={row.type === 2}
              v-model:value={row.name}
              editMode={false}
              on-update:value={(_: any, _option: CascaderOption, pathValues: Array<CascaderOption | null>) => {
                row.name = pathValues.map(p => p?.label).join('/');
              }}
            />
            <OperationCascader
              v-show={row.type === 4}
              v-model:value={row.name}
              editMode={false}
              on-update:value={(_: any, _option: CascaderOption, pathValues: Array<CascaderOption | null>) => {
                row.name = pathValues.map(p => p?.label).join('/');
              }}
            />
          </div>
        );
      }
      return row.name;
    }
  },
  {
    title: '用量',
    key: 'singleDose',
    width: 120,
    render: (row: DetailItem) => {
      if (row.isEditing) {
        return <NInputNumber v-model:value={row.singleDose} placeholder="请输入用量" min={1} />;
      }
      return row.singleDose;
    }
  },
  {
    title: '单位',
    key: 'unit',
    width: 150,
    render: (row: DetailItem) => {
      if (row.isEditing) {
        if (row.type === 3) {
          return <NSelect v-model:value={row.unit} placeholder="请选择单位" options={unitOptions} />;
        }
        if (!row.unit) {
          row.unit = '次';
        }
        return <span>{row.unit}</span>;
      }
      return row.unit;
    }
  },
  {
    title: '条件',
    width: 180,
    key: 'comment',
    render: (row: DetailItem) => {
      if (row.isEditing) {
        return <NInput v-model:value={row.comment} type="textarea" rows={1} />;
      }
      return row.comment;
    }
  },
  {
    title: '操作',
    key: 'actions',
    render: (row: DetailItem) => {
      if (!row.isEditing) {
        return (
          <NButton type="primary" size="small" onClick={() => handleEdit(row)}>
            编辑
          </NButton>
        );
      }
      return (
        <NSpace>
          <NButton size="small" onClick={() => handleCancel(row)}>
            取消
          </NButton>
          <NButton type="primary" size="small" onClick={() => handleSave(row)}>
            保存
          </NButton>
        </NSpace>
      );
    }
  }
];

// 表单校验规则
const rules = {
  basicInfo: {
    name: {
      required: true,
      message: '请输入名称',
      trigger: ['blur', 'input']
    },
    type: {
      required: true,
      message: '请选择项目类别',
      trigger: ['blur', 'change']
    }
  }
};

// 方法
function createDefaultModel(): Api.Suite.Suite {
  return {
    name: '',
    shortName: '',
    alias: '',
    diseaseId: null,
    description: '',
    enName: ''
  };
}

function handleEdit(row: DetailItem) {
  row.isEditing = true;
}

function handleCancel(row: DetailItem) {
  if (row.isNew) {
    const index = detailData.value.length - 1;
    detailData.value.splice(index, 1);
  } else {
    row.isEditing = false;
  }
}

async function handleSave(row: DetailItem) {
  if (!suiteId.value) {
    window.$message?.error('请先保存基础信息');
    return;
  }

  if (!row.type) {
    window.$message?.error('请选择项目类别');
    return;
  }

  if (!row.name) {
    window.$message?.error('请选择项目名称');
    return;
  }

  const params: Api.Suite.SuiteItem = {
    moduleId: suiteId.value,
    moduleType: 2,
    name: row.name,
    singleDose: row.singleDose,
    unit: row.unit,
    comment: row.comment,
    type: Number(row.type),
    orderNumber: row.orderNumber
  };

  try {
    if (row.isNew) {
      await suiteStore.addSuiteItem(params);
      row.isNew = false;
    } else {
      params.id = row.id;
      await suiteStore.updateSuiteItem(params);
    }
    row.isEditing = false;
    window.$message?.success('保存成功');
  } catch (error: any) {
    console.log(error);
    window.$message?.error('保存失败');
  }
}

async function handleDelete(row: DetailItem) {
  if (row.isNew) {
    const index = detailData.value.length - 1;
    detailData.value.splice(index, 1);
    return;
  }

  try {
    const index = detailData.value.findIndex(item => item.id === row.id);
    if (index > -1) {
      detailData.value.splice(index, 1);
      detailData.value.forEach((item, idx) => {
        item.orderNumber = idx + 1;
      });

      const res = await fetchGetSuiteItemDel(row.id!);
      if (res.response.data.status === 'OK') {
        window.$message?.success('删除成功');
      } else {
        throw new Error(res.response.data.message);
      }
    }
  } catch (error: any) {
    window.$message?.error(error.message || '删除失败');
  }
}

function getSubmitParams() {
  const submitParams: Api.Suite.Suite & Api.Common.BaseRequestParams = {
    name: formModel.basicInfo.name,
    shortName: formModel.basicInfo.shortName,
    alias: formModel.basicInfo.alias,
    diseaseId: formModel.basicInfo.diseaseId,
    description: formModel.basicInfo.description,
    enName: formModel.basicInfo.enName,
    userName: 'admin',
    userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
  };

  if (props.operateType === 'edit' && formModel.basicInfo.id) {
    submitParams.id = formModel.basicInfo.id;
  }

  return submitParams;
}

async function handleSubmit() {
  try {
    await validate();
    const params = getSubmitParams();

    if (props.operateType === 'edit') {
      await suiteStore.updateSuite({ ...params, userId: 'ed2bcf1d7a584a69bf99bd0df530475a', userName: 'admin' });
    } else {
      const res: any = await suiteStore.addSuite({
        ...params,
        userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
        userName: 'admin'
      });
      suiteId.value = res.data?.data;
    }

    window.$message?.success($t('common.updateSuccess'));
    emit('submitted');
  } catch (error) {
    console.log(error);
    window.$message?.error('提交失败');
  }
}

async function getDiseaseOptions() {
  await diseaseStore.getDiseaseList();
  diseaseOptions.value = diseaseStore.diseaseList;
}

async function getItemType() {
  if (typeOptions.value.length > 0) return;
  await suiteStore.getItemType();
  typeOptions.value = [...(suiteStore.itemTypeList as any)];
}

async function getSuiteItemList(moduleId: string) {
  await suiteStore.getSuiteItemList(moduleId);
  detailData.value = suiteStore.suiteItemList.map(item => ({
    unit: item.unit,
    singleDose: Number(item.singleDose),
    id: item.id,
    orderNumber: item.orderNumber,
    name: item.name,
    type: item.type,
    comment: item.comment,
    isEditing: false,
    isNew: false
  }));
}

function handleInitModel() {
  Object.assign(formModel, createDefaultModel());

  if (!props.rowData) return;

  if (props.operateType === 'edit') {
    Promise.all([getDiseaseOptions(), getItemType(), getSuiteItemList(props.rowData.id!)]);
    suiteId.value = props.rowData.id!;
    Object.assign(formModel, { basicInfo: props.rowData });
  }
}

function closeDrawer() {
  visible.value = false;
  Object.assign(formModel.basicInfo, createDefaultModel());
  suiteId.value = '';
  detailData.value = [];
}

function handleAddDetail() {
  const newId = detailData.value.length + 1;
  detailData.value.push({
    orderNumber: newId,
    name: null,
    type: null,
    comment: '',
    unit: '次',
    singleDose: 1,
    isEditing: true,
    isNew: true
  });
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="h-full w-full" @close="closeDrawer">
    <NScrollbar>
      <NCard title="基础信息" class="mb-4">
        <NForm
          ref="formRef"
          :model="formModel.basicInfo"
          :rules="rules.basicInfo"
          label-placement="left"
          label-width="80"
        >
          <div class="grid grid-cols-4 gap-4">
            <NFormItem label="名称" path="name" required>
              <NInput v-model:value="formModel.basicInfo.name" placeholder="请输入名称" />
            </NFormItem>

            <NFormItem label="疾病分型" path="diseaseId">
              <DiseaseSelect v-model:value="formModel.basicInfo.diseaseId" />
            </NFormItem>
            <NFormItem label="英文名称" path="enNamee">
              <NInput v-model:value="formModel.basicInfo.enName" placeholder="请输入英文名称" />
            </NFormItem>
            <NFormItem label="缩写" path="shortName">
              <NInput v-model:value="formModel.basicInfo.shortName" placeholder="请输入缩写" />
            </NFormItem>
            <NFormItem label="别名" path="alias">
              <NInput v-model:value="formModel.basicInfo.alias" placeholder="请输入别名" />
            </NFormItem>
            <NFormItem label="描述" path="description" class="col-span-2">
              <NInput
                v-model:value="formModel.basicInfo.description"
                type="textarea"
                placeholder="请输入描述"
                :rows="1"
              />
            </NFormItem>
          </div>
          <NSpace justify="end" :size="16">
            <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
            <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
          </NSpace>
        </NForm>
      </NCard>
      <NCard title="组合详情">
        <template #header-extra>
          <NButton type="primary" @click="handleAddDetail">添加项目</NButton>
        </template>
        <NSpace vertical>
          <NDataTable
            v-model:checked-row-keys="checkedRowKeys"
            :columns="detailColumns as TableColumn<DetailItem>[]"
            :data="detailData"
            :row-key="(row: any) => row.id"
            class="data-common-table"
          />
        </NSpace>
      </NCard>
    </NScrollbar>
  </NModal>
</template>

<style scoped></style>
