<script setup lang="tsx">
import { ref } from 'vue';
import type { Ref } from 'vue';
import { NButton, useDialog } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchGetSuiteDel, fetchPostSuiteList } from '@/service/api/suite';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import SuiteSearch from './modules/suite-search.vue';

import SuiteOperateModal from './modules/suite-operate-modal.vue';

export type OperateType = NaiveUI.TableOperateType;

const { bool: visible, setTrue: openModal } = useBoolean();

const appStore = useAppStore();

const dialog = useDialog();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchPostSuiteList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    name: null,
    dieaseId: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center'
    },
    {
      key: 'name',
      title: $t('page.database.suite.name'),
      align: 'center'
    },
    {
      key: 'shortName',
      title: $t('page.database.suite.shortName'),
      align: 'center'
    },
    {
      key: 'alias',
      title: $t('page.database.suite.alias'),
      align: 'center'
    },
    {
      key: 'diseaseId',
      title: $t('page.database.suite.diseaseId'),
      align: 'center',
      render: row => <div>{row.diseaseName}</div>
    },
    {
      key: 'enName',
      title: $t('page.database.suite.enName'),
      align: 'center'
    },
    {
      key: 'subItems',
      title: $t('page.database.suite.detail'),
      align: 'center',
      // subItems是个数组，拆分以逗号分隔
      render: row => <div>{row.subItems?.join(',')}</div>
    },
    {
      key: 'description',
      title: $t('page.database.suite.desc'),
      align: 'center'
    },
    {
      key: 'createTime',
      title: $t('page.database.suite.createTime'),
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 120,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row)}>
            {$t('common.edit')}
          </NButton>
          <NButton type="error" ghost size="small" onClick={() => handleDelete(row.id!)}>
            {$t('common.delete')}
          </NButton>
        </div>
      )
    }
  ]
});

const { checkedRowKeys, onBatchDeleted, onDeleted } = useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  onBatchDeleted();
}

const operateType = ref<OperateType>('add');

function handleAdd() {
  operateType.value = 'add';
  openModal();
}
/** the edit menu data or the parent menu data when adding a child menu */
const editingData: Ref<Api.Suite.Suite | null> = ref(null);

function edit(item: Api.Suite.Suite) {
  operateType.value = 'edit';
  editingData.value = { ...item };
  openModal();
}
async function handleDelete(id: string) {
  // const res = await fetchGetSuiteBeforeDel({ id });
  dialog.create({
    title: '删除',
    content: `您确定要删除吗？
      删除后历史记录不可查询！`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const res = await fetchGetSuiteDel(id);
      if (res.response.data.status === 'OK') {
        onDeleted();
      }
    }
  });
}
</script>

<template>
  <div class="min-h-500px flex gap-16px overflow-hidden lt-sm:overflow-auto">
    <div class="flex-col-stretch gap-16px overflow-hidden">
      <SuiteSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
      <NCard
        :title="$t('page.database.suite.title')"
        :bordered="false"
        size="small"
        class="data-common-table sm:flex-1-hidden card-wrapper"
      >
        <template #header-extra>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading"
            @add="handleAdd"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </template>
        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="962"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="mobilePagination"
          class="sm:h-full"
        />
        <SuiteOperateModal
          v-model:visible="visible"
          :operate-type="operateType"
          :row-data="editingData"
          @submitted="getDataByPage"
        />
      </NCard>
    </div>
  </div>
</template>

<style scoped></style>
