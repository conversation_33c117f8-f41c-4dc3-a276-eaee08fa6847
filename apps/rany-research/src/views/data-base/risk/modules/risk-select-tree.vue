<script setup lang="ts">
import { ref, watch } from 'vue';
import { fetchGetRiskTree } from '@/service/api/risk';

interface Props {
  selectedId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  selectedId: ''
});

interface Emits {
  (e: 'update:selectedId', value: string): void;
}

const emit = defineEmits<Emits>();

const data = ref();
const selectedValue = ref<string | null>(props.selectedId || null);

async function getRiskTreeData() {
  const { error, data: newData } = await fetchGetRiskTree();
  if (!error) {
    console.log('newData', newData);
    data.value = newData;
  }
}

getRiskTreeData();

// 处理选择变化
const handleSelectionChange = (value: string | null) => {
  selectedValue.value = value;
  emit('update:selectedId', value || '');
};

// 监听外部选择变化
watch(
  () => props.selectedId,
  newValue => {
    selectedValue.value = newValue || null;
  }
);
</script>

<template>
  <div class="filter-item">
    <div class="filter-label">分型分组：</div>
    <div class="filter-control">
      <NTreeSelect
        v-model:value="selectedValue"
        class="filter-tree-select"
        size="small"
        :options="data"
        label-field="name"
        key-field="id"
        children-field="childList"
        placeholder="请选择分型分组"
        clearable
        @update:value="handleSelectionChange"
      />
    </div>
  </div>
</template>

<style scoped></style>
