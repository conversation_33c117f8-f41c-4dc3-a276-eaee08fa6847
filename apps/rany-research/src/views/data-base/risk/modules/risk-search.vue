<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';

interface Props {
  searchParams?: any;
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({
    pageNo: 1,
    pageSize: 10,
    shortName: null,
    name: null,
    riskId: ''
  })
});

interface Emits {
  (e: 'filter-change', params: any): void;
}

const emit = defineEmits<Emits>();

// 名称筛选
const selectedName = ref<string | null>(props.searchParams?.name || null);

// 处理名称筛选变化
const handleNameChange = (value: string | null) => {
  selectedName.value = value;
  emit('filter-change', { name: value });
};

// 监听外部搜索参数变化
watch(
  () => props.searchParams?.name,
  newValue => {
    selectedName.value = newValue || null;
  }
);

onMounted(() => {
  // 初始化逻辑
});
</script>

<template>
  <div class="flex gap-16px">
    <div class="filter-item">
      <div class="filter-label">名称：</div>
      <div class="filter-control">
        <NInput
          v-model:value="selectedName"
          class="filter-input"
          placeholder="请输入名称"
          clearable
          size="small"
          @update:value="handleNameChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  font-size: 14px;
  color: var(--text-color-2);
}

.filter-input {
  min-width: 120px;
}
</style>
