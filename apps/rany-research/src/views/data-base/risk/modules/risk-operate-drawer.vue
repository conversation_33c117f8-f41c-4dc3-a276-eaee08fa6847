<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchGetDiseaseList, fetchPostRiskAdd, fetchPostRiskEdit } from '@/service/api';
import { $t } from '@/locales';
import ExperimentMultiSelect from '@/components/common/experiment-multi-select.vue';

defineOptions({
  name: 'RiskOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Risk.Risk | null;
  /** the project id */
  projectId?: string;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: $t('page.database.risk.add'),
    edit: $t('page.database.risk.edit')
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Risk.Risk, 'diseaseId' | 'riskId' | 'groupCondition' | 'projectIds'>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    diseaseId: '',
    riskId: '',
    groupCondition: '',
    projectIds: props.projectId ? [props.projectId] : ([] as string[])
  };
}

type RuleKey = Extract<keyof Model, 'diseaseId' | 'riskId' | 'groupCondition'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  diseaseId: defaultRequiredRule,
  riskId: defaultRequiredRule,
  groupCondition: defaultRequiredRule
};

/** the disease options */
const diseaseOptions = ref<CommonType.Option<string>[]>([]);

async function getDiseaseListOptions() {
  const { error, data } = await fetchGetDiseaseList({
    pageNo: 1,
    pageSize: 1000
  });

  if (!error) {
    const options = data.records.map(item => ({
      label: item.shortName || '',
      value: item.id || ''
    }));

    diseaseOptions.value = options;
  }
}

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
    // 将危险度id放到数组内
    model.projectIds = (props.rowData.projectId || '').split(',').filter(Boolean);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  const submitData: any = {
    ...model,
    userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
    userName: 'admin'
  };
  if (props.operateType === 'add') {
    const { error } = await fetchPostRiskAdd(submitData);
    if (!error) {
      window.$message?.success($t('common.addSuccess'));
      closeDrawer();
      emit('submitted');
    }
  } else if (props.operateType === 'edit') {
    submitData.id = props.rowData?.id;
    const { error } = await fetchPostRiskEdit(submitData);
    if (!error) {
      window.$message?.success($t('common.updateSuccess'));
      closeDrawer();
      emit('submitted');
    }
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
    getDiseaseListOptions();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem :label="$t('page.database.risk.shortName')" path="diseaseId">
          <NSelect
            v-model:value="model.diseaseId"
            :options="diseaseOptions"
            :placeholder="$t('page.database.risk.shortName')"
          />
        </NFormItem>
        <NFormItem :label="$t('page.database.risk.risk')" path="riskId">
          <RiskCascader v-model:value="model.riskId" :multiple="false" />
        </NFormItem>
        <NFormItem :label="$t('page.database.risk.projectName')" path="projectIds">
          <ExperimentMultiSelect v-model:value="model.projectIds!" :disabled="!!props.projectId" />
        </NFormItem>
        <NFormItem :label="$t('page.database.risk.groupCondition')" path="groupCondition">
          <NInput
            v-model:value="model.groupCondition"
            type="textarea"
            :placeholder="$t('page.database.risk.groupCondition')"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
