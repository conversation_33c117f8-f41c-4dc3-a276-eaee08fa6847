<script setup lang="tsx">
import { NButton, useDialog } from 'naive-ui';
import { reactive } from 'vue';
import { fetchGetRiskDel, fetchGetRiskList } from '@/service/api/risk';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import RiskOperateDrawer from './modules/risk-operate-drawer.vue';
import RiskSearch from './modules/risk-search.vue';
import RiskSelectTree from './modules/risk-select-tree.vue';

const appStore = useAppStore();

const dialog = useDialog();

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination, searchParams } = useTable({
  apiFn: fetchGetRiskList,
  showTotal: false,
  apiParams: reactive({
    pageNo: 1,
    pageSize: 10,
    shortName: null,
    name: null,
    riskId: ''
  }),
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 48
    },
    {
      key: 'shortName',
      title: $t('page.database.risk.shortName'),
      align: 'center',
      width: 80
    },
    {
      key: 'risk',
      title: $t('page.database.risk.risk'),
      align: 'center',
      width: 80,
      render: row => {
        return <span>{row.riskName}</span>;
      }
    },
    {
      key: 'projectName',
      title: $t('page.database.risk.projectName'),
      align: 'center',
      width: 120
    },
    {
      key: 'groupCondition',
      title: $t('page.database.risk.groupCondition'),
      align: 'center'
    },
    // {
    //   key: 'createTime',
    //   title: $t('page.database.disease.createTime'),
    //   align: 'center',
    //   width: 100
    // },
    {
      key: 'updateTime',
      title: $t('page.database.disease.updateTime'),
      align: 'center'
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            {$t('common.edit')}
          </NButton>
          <NButton type="error" ghost size="small" onClick={() => handleDelete(row.id!)}>
            {$t('common.delete')}
          </NButton>
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData);

async function handleDelete(id: string) {
  dialog.create({
    title: '删除',
    content: `您确定要删除吗？
      删除后历史记录不可查询！`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const res: any = await fetchGetRiskDel(id);
      if (res.status === 'OK') {
        onDeleted();
      }
    }
  });
}

function edit(id: string | undefined) {
  handleEdit(id);
}

function handleRiskTreeChange(id: string) {
  searchParams.riskId = id;
  getDataByPage();
}

// 处理搜索事件
function handleSearch(value: string) {
  searchParams.shortName = value || null;
  getDataByPage();
}

// 处理筛选变化事件
function handleFilterChange(filterParams: any) {
  Object.assign(searchParams, filterParams);
  getDataByPage();
}

// 使用reduce方法过滤数据
// const filteredData = computed(() => {
//   return data.value.reduce((acc: any[], curr: any) => {
//     if (curr.delFlag !== 1) {
//       acc.push(curr);
//     }
//     return acc;
//   }, []);
// });
</script>

<template>
  <div class="flex-col-stretch flex-1 overflow-hidden">
    <NCard
      :bordered="false"
      size="small"
      class="data-common-table sm:flex-1-hidden"
      content-style="padding:16px 24px;display:flex;flex-direction:column;"
    >
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :disabled-delete="checkedRowKeys.length === 0"
        :loading="loading"
        search-placeholder="请输入简称"
        @add="handleAdd"
        @refresh="getData"
        @search="handleSearch"
      >
        <template #filters>
          <RiskSelectTree :selected-id="searchParams.riskId" @update:selected-id="handleRiskTreeChange" />
          <!-- <RiskSearch :search-params="searchParams" @filter-change="handleFilterChange" /> -->
        </template>
      </TableHeaderOperation>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <RiskOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
