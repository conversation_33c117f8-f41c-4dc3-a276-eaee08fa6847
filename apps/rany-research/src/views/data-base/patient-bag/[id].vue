<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useBagStore } from '@/store/modules/bag';
import ExpParticipantDetail from '@/views/experiment/detail/modules/participant/exp-participant-detail.vue';

const route = useRoute();
const id = computed(() => route.params.id as string);

const store = useBagStore();
const { loading } = storeToRefs(store);
</script>

<template>
  <div class="flex-col-stretch gap-16px">
    <NCard :loading="loading" :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <NDescriptions label-placement="top" title="患者详情" />
      <ExpParticipantDetail :id="id" type="patient" />
    </NCard>
  </div>
</template>

<style scoped>
.card-wrapper {
  min-height: 200px;
}
</style>
