<script setup lang="ts">
import type { Ref } from 'vue';
import { onMounted, ref } from 'vue';
import { NButton, NCard, NDescriptions, NDescriptionsItem, NImage, NSpace, NTabPane, NTabs } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { useRoute } from 'vue-router';
import { fetchPostHospitalList } from '@/service/api/hospital';
import HospitalOperateModal from '../table/modules/hospital-operate-modal.vue';
import DeptInfoTable from './modules/hos-dept-info-table.vue';
import ExperimentTable from './modules/hos-experiment-table.vue';
import HospitalFileTable from './modules/hos-file-table.vue';

interface Props {
  id: string;
}

const route = useRoute();

const hosId: Ref<string> = ref(String(route.params.id)); // 确保将参数转换为字符串

const props = defineProps<Props>();

const hospitalInfo = ref<Api.Hospital.Hospital | undefined>(undefined); // 初始化为 undefined

const activeTab = ref('department');

const { bool: visible, setTrue: openModal } = useBoolean();

async function getHospitalInfo() {
  try {
    const res = await fetchPostHospitalList({
      pageNo: 1,
      pageSize: 10
    });
    if (res.data) {
      hospitalInfo.value = res.data.records.find(item => item.id === props.id);
    }
  } catch {
    window.$message?.error('获取医院信息失败');
  }
}

function handleEdit() {
  // hospitalInfo.value = { ...hospitalInfo.value };
  openModal();
}

onMounted(() => {
  getHospitalInfo();
});
</script>

<template>
  <div class="h-full flex-col gap-16px">
    <NCard :bordered="false" size="large">
      <div class="flex gap-24px">
        <div class="w-120px">
          <NImage
            v-if="hospitalInfo?.logo"
            :src="`/filestorage${hospitalInfo.logo}`"
            width="120"
            height="120"
            object-fit="contain"
          />
        </div>
        <div class="flex-1">
          <NSpace vertical :size="24">
            <div class="flex-col gap-8px">
              <div class="flex items-center justify-between">
                <h1 class="text-24px font-bold">{{ hospitalInfo?.name }}</h1>
                <NButton size="small" type="primary" @click="handleEdit()">编辑</NButton>
              </div>
              <NDescriptions>
                <NDescriptionsItem label="地址">
                  {{ hospitalInfo?.address }}
                </NDescriptionsItem>
                <NDescriptionsItem label="联系电话">
                  {{ hospitalInfo?.iphoneNumber }}
                </NDescriptionsItem>
                <NDescriptionsItem label="联系邮箱">
                  {{ hospitalInfo?.email }}
                </NDescriptionsItem>
              </NDescriptions>
            </div>
          </NSpace>
        </div>
      </div>
    </NCard>

    <NCard :bordered="false" class="flex-1">
      <NTabs v-model:value="activeTab" type="line" animated class="h-full">
        <NTabPane name="department" tab="科室信息">
          <DeptInfoTable :hospital-id="hosId" />
        </NTabPane>
        <NTabPane name="users" tab="用户信息">用户信息内容</NTabPane>
        <NTabPane name="experiments" tab="关联试验">
          <ExperimentTable :hospital-id="hosId" />
        </NTabPane>
        <NTabPane name="files" tab="文件">
          <HospitalFileTable :relevance-id="hosId" />
        </NTabPane>
      </NTabs>
    </NCard>

    <HospitalOperateModal
      v-model:visible="visible"
      v-model:row-data="hospitalInfo!"
      operate-type="edit"
      @submitted="getHospitalInfo"
    />
  </div>
</template>

<style scoped></style>
