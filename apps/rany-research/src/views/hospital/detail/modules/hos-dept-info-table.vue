<script setup lang="tsx">
import { onMounted, ref } from 'vue';
import type { Ref } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchPostDepartmentDelete, fetchPostDepartmentEdit, fetchPostDepartmentList } from '@/service/api';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { enableStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
// import DeptSearch from './dept-search.vue';
import { useHosStore } from '@/store/modules/hospital';
import DepartmentOperateModal from '../../table/modules/department-operate-modal.vue';

export type OperateType = NaiveUI.TableOperateType;

defineOptions({
  name: 'DeptTable'
});

interface Props {
  hospitalId?: string | undefined;
}

const hosStore = useHosStore();

const props = withDefaults(defineProps<Props>(), {
  hospitalId: ''
});

const { bool: visible, setTrue: openModal } = useBoolean();
const submitLoading = ref(false);

const appStore = useAppStore();
function getDepartmentNameById(departmentId: string) {
  const data = hosStore.departmentList;
  const department = data?.find(dept => dept.id === departmentId);
  return department ? department.name : '';
}

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination } = useTable({
  apiFn: fetchPostDepartmentList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    hospitalId: props.hospitalId
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: $t('page.dept.name'),
      align: 'center'
    },
    {
      key: 'parentId',
      title: '上级组织',
      align: 'center',
      sorter: true,
      render: row => {
        const name = getDepartmentNameById(row.parentId);
        return <NTag>{name || '顶级'}</NTag>;
      }
    },
    {
      key: 'division',
      title: '科别',
      align: 'center',
      sorter: true
    },

    {
      key: 'createTime',
      title: $t('page.dept.createTime'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: $t('page.dept.status'),
      align: 'center',
      sorter: true,
      render: row => {
        if (row.status === null) {
          return null;
        }

        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = $t(enableStatusRecord[row.status as Api.Common.EnableStatus]);

        return <NTag type={tagMap[row.status as Api.Common.EnableStatus]}>{label}</NTag>;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 230,
      render: row => (
        <div class="flex-center justify-end gap-8px">
          {row.status === 1 ? (
            <NPopconfirm onPositiveClick={() => handleDisable(row)}>
              {{
                default: () => '确定要禁用该医院吗?',
                trigger: () => (
                  <NButton type="primary" ghost size="small">
                    禁用
                  </NButton>
                )
              }}
            </NPopconfirm>
          ) : (
            <NPopconfirm onPositiveClick={() => handleEnable(row)}>
              {{
                default: () => '确定要启用该医院吗?',
                trigger: () => (
                  <NButton type="primary" ghost size="small">
                    启用
                  </NButton>
                )
              }}
            </NPopconfirm>
          )}
          <NButton type="primary" ghost size="small" onClick={() => edit(row)}>
            {$t('common.edit')}
          </NButton>

          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

/** the edit menu data or the parent menu data when adding a child menu */
const editingData: Ref<Api.Dept.Department> = ref({} as Api.Dept.Department);

const rules = {
  name: {
    required: true,
    message: '请输入科室名称',
    trigger: ['blur', 'input']
  },
  division: {
    required: true,
    message: '请选择科别',
    trigger: ['blur', 'change']
  },
  parentId: {
    required: true,
    message: '请选择上级组织',
    trigger: ['blur', 'change']
  }
};

async function handleDelete(id: string | undefined) {
  try {
    submitLoading.value = true;
    await fetchPostDepartmentDelete(id);
    window.$message?.success('删除成功');
    getData();
  } catch (error: any) {
    window.$message?.error(error?.message || '删除失败');
  } finally {
    submitLoading.value = false;
  }
}

async function handleDisable(row: Api.Dept.Department) {
  try {
    submitLoading.value = true;
    await fetchPostDepartmentEdit({ ...row, status: 0, userId: 'ed2bcf1d7a584a69bf99bd0df530475a', userName: 'admin' });
    window.$message?.success('禁用成功');
    getData();
  } catch (error: any) {
    window.$message?.error(error?.message || '禁用失败');
  } finally {
    submitLoading.value = false;
  }
}

async function handleEnable(row: Api.Dept.Department) {
  try {
    submitLoading.value = true;
    await fetchPostDepartmentEdit({ ...row, status: 1, userId: 'ed2bcf1d7a584a69bf99bd0df530475a', userName: 'admin' });
    window.$message?.success('启用成功');
    getData();
  } catch (error: any) {
    window.$message?.error(error?.message || '启用失败');
  } finally {
    submitLoading.value = false;
  }
}

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  onBatchDeleted();
}

const operateType = ref<OperateType>('add');

function handleAdd() {
  operateType.value = 'add';
  editingData.value = {
    name: '',
    division: '',
    parentId: '',
    status: 1,
    hospitalId: props.hospitalId,
    userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
    userName: 'admin'
  } as Api.Dept.Department;
  openModal();
}

function edit(item: Api.Dept.Department) {
  operateType.value = 'edit';
  editingData.value = { ...item };
  openModal();
}

onMounted(() => {
  hosStore.getDepartmentList(props.hospitalId);
});
</script>

<template>
  <div class="min-h-500px flex gap-16px overflow-hidden lt-sm:overflow-auto">
    <div class="flex-col-stretch gap-16px overflow-hidden">
      <!-- <DeptSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" /> -->
      <NCard
        :title="$t('page.dept.title')"
        :bordered="false"
        size="small"
        class="data-common-table sm:flex-1-hidden card-wrapper"
      >
        <template #header-extra>
          <TableHeaderOperation
            v-model:columns="columnChecks"
            :disabled-delete="checkedRowKeys.length === 0"
            :loading="loading || submitLoading"
            @add="handleAdd"
            @delete="handleBatchDelete"
            @refresh="getData"
          />
        </template>
        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="962"
          :loading="loading || submitLoading"
          remote
          :row-key="row => row.id"
          :pagination="mobilePagination"
          class="sm:h-full"
        />
        <DepartmentOperateModal
          v-model:visible="visible"
          :row-data="editingData"
          :operate-type="operateType"
          :hospital-id="props.hospitalId"
          :rules="rules"
          @submitted="getDataByPage"
        />
      </NCard>
    </div>
  </div>
</template>

<style scoped></style>
