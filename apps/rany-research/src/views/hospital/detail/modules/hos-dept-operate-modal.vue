<script setup lang="tsx">
import { NButton, NForm, NFormItem, NImage, NInput, NSelect, NSpace, NUpload } from 'naive-ui';
import type { Ref } from 'vue';
import { computed, reactive, ref, watch } from 'vue';
import type { UploadFileInfo } from 'naive-ui';
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
import { fetchPostHospitalAdd, fetchPutHospitalUpdate } from '@/service/api/hospital';
import { useHosStore } from '@/store/modules/hospital';

export type OperateType = NaiveUI.TableOperateType;

interface Props {
  operateType: OperateType;
  rowData?: Api.Hospital.Hospital | null;
}

const uploadFileUrl = import.meta.env.VITE_UPLOAD_URL;

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', { default: false });

const { formRef, validate, restoreValidation } = useNaiveForm();

const regionStore = useHosStore();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: '新增医院',
    edit: '编辑医院'
  };
  return titles[props.operateType];
});

const formModel = reactive<Api.Hospital.Hospital>({
  name: '',
  province: '',
  address: '',
  iphoneNumber: '',
  email: '',
  logo: '',
  status: 1,
  coordinate: '',
  longitude: '',
  latitude: ''
});

const previewFileList = ref<UploadFileInfo[]>();

const areaModel: Ref<{
  province: string | number;
  city: string | number;
  district: string | number;
}> = ref({
  province: '',
  city: '',
  district: ''
});
// 省份数据
const provinceOptions = computed<NaiveUI.SelectOption[]>(() =>
  regionStore.provinceList.map(({ name, id }) => ({
    label: name,
    value: id
  }))
);

// 城市数据
const cityOptions = computed<NaiveUI.SelectOption[]>(() => {
  if (!areaModel.value.province || regionStore.cityList.length === 0) return [];
  return regionStore.cityList.map(({ name, id }) => ({
    label: name,
    value: id
  }));
});

// 区县数据
const districtOptions = computed<NaiveUI.SelectOption[]>(() => {
  if (!areaModel.value.province || !areaModel.value.city || regionStore.districtList.length === 0) return [];
  return regionStore.districtList.map(({ name, id }) => ({
    label: name,
    value: id
  }));
});

// 监听省份变化
watch(
  () => areaModel.value.province,
  async (val, oldVal) => {
    if (oldVal && val !== oldVal) {
      areaModel.value.city = '';
      areaModel.value.district = '';
    }
    if (val) {
      await regionStore.getRegionList(2, val.toString());
    }
  }
);

// 监听城市变化
watch(
  () => areaModel.value.city,
  async (val, oldVal) => {
    if (oldVal && val !== oldVal) {
      areaModel.value.district = '';
    }
    if (val) {
      await regionStore.getRegionList(3, val.toString());
    }
  }
);

const rules = {
  name: {
    required: true,
    message: '请输入医院名称',
    trigger: ['blur', 'input']
  },
  province: {
    message: '请选择省份',
    trigger: ['blur', 'change']
  },
  city: {
    message: '请选择城市',
    trigger: ['blur', 'change']
  },
  district: {
    message: '请选择区县',
    trigger: ['blur', 'change']
  },
  address: {
    message: '请输入详细地址',
    trigger: ['blur', 'input']
  },
  iphoneNumber: {
    required: false,
    message: '请输入联系电话',
    trigger: ['blur', 'input'],
    validator(_rule: any, value: string) {
      if (!value) return true;
      return /^1[3-9]\d{9}$/.test(value) || new Error('请输入正确的手机号码');
    }
  },
  email: {
    required: false,
    message: '请输入联系邮箱',
    trigger: ['blur', 'input'],
    validator(_rule: any, value: string) {
      if (!value) return true;
      return /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value) || new Error('请输入正确的邮箱格式');
    }
  }
};

async function getSubmitParams() {
  formModel.province = [areaModel.value.province, areaModel.value.city, areaModel.value.district]
    .filter(Boolean)
    .join(',');
  formModel.coordinate = `${formModel.longitude}-${formModel.latitude}`;

  const params = { ...formModel };
  if (props.operateType === 'edit' && props.rowData?.id) {
    params.id = props.rowData.id;
    await fetchPutHospitalUpdate({ ...params, userId: 'ed2bcf1d7a584a69bf99bd0df530475a', userName: 'admin' });
  } else {
    await fetchPostHospitalAdd(params);
  }
}

async function handleSubmit() {
  await validate();
  await getSubmitParams();
  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

async function handleInitModel() {
  Object.assign(formModel, {
    name: '',
    province: '',
    address: '',
    iphoneNumber: '',
    email: '',
    logo: '',
    status: 1,
    coordinate: '',
    longitude: '',
    latitude: ''
  });

  Object.assign(areaModel.value, {
    province: '',
    city: '',
    district: ''
  });

  await regionStore.getRegionList(1);

  if (!props.rowData || props.operateType !== 'edit') return;

  Object.assign(formModel, props.rowData);

  if (formModel.province) {
    const [province = '', city = '', district = ''] = formModel.province.split(',');

    if (province) {
      await regionStore.getRegionList(2, province);
    }
    if (city) {
      await regionStore.getRegionList(3, city);
    }
    areaModel.value.district = district;
    areaModel.value.city = city;
    areaModel.value.province = province;
  }

  if (formModel.coordinate) {
    const [longitude = '', latitude = ''] = formModel.coordinate.split('-');
    formModel.longitude = longitude;
    formModel.latitude = latitude;
  }
}

function closeDrawer() {
  visible.value = false;
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

// 添加上传成功的逻辑
const handleFinish = ({ event }: { event?: any }) => {
  const response = JSON.parse(event?.target?.response || '{}');
  formModel.logo = response.data[0];
};
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-800px">
    <NScrollbar class="h-480px pr-20px">
      <NForm ref="formRef" :model="formModel" :rules="rules" label-placement="left" label-width="80">
        <div class="grid grid-cols-1 gap-4">
          <NFormItem label="医院名称" path="name" required>
            <NInput v-model:value="formModel.name" placeholder="请输入医院名称" />
          </NFormItem>

          <div class="grid grid-cols-3 gap-4">
            <NFormItem label="所在省" path="province">
              <NSelect v-model:value="areaModel.province" :options="provinceOptions" placeholder="请选择省份" />
            </NFormItem>
            <NFormItem label="所在市" path="city">
              <NSelect v-model:value="areaModel.city" :options="cityOptions" placeholder="请选择城市" />
            </NFormItem>
            <NFormItem label="所在区" path="district">
              <NSelect v-model:value="areaModel.district" :options="districtOptions" placeholder="请选择区县" />
            </NFormItem>
          </div>

          <NFormItem label="详细地址" path="address">
            <NInput v-model:value="formModel.address" placeholder="请输入详细地址" />
          </NFormItem>

          <div class="grid grid-cols-2 gap-4">
            <NFormItem label="经度" path="longitude">
              <NInput v-model:value="formModel.longitude" placeholder="请输入经度" />
            </NFormItem>
            <NFormItem label="纬度" path="latitude">
              <NInput v-model:value="formModel.latitude" placeholder="请输入纬度" />
            </NFormItem>
          </div>

          <NFormItem label="联系电话" path="iphoneNumber">
            <NInput v-model:value="formModel.iphoneNumber" placeholder="请输入联系电话" />
          </NFormItem>

          <NFormItem label="联系邮箱" path="email">
            <NInput v-model:value="formModel.email" placeholder="请输入联系邮箱" />
          </NFormItem>

          <NFormItem label="医院Logo" path="logo">
            <NUpload
              :action="uploadFileUrl"
              :max="1"
              :default-file-list="previewFileList"
              list-type="image-card"
              @finish="handleFinish"
            ></NUpload>
            <NImage v-if="formModel.logo" :src="formModel.logo" />
          </NFormItem>
        </div>
      </NForm>
    </NScrollbar>
    <template #footer>
      <NSpace justify="end" :size="16">
        <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
        <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
