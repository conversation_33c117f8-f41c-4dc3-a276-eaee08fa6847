<script setup lang="tsx">
import { NButton, NTag } from 'naive-ui';
import { fetchPostExperimentExitHospital, fetchPostExperimentListByHospitalId } from '@/service/api';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useRouterPush } from '@/hooks/common/router';
import { experimentStatusRecord } from '@/constants/business';
import { useTable } from '@/hooks/common/table';

export type OperateType = NaiveUI.TableOperateType;

defineOptions({
  name: 'ExperimentTable'
});

interface Props {
  hospitalId?: string | undefined;
}

const { routerPushByKey } = useRouterPush();

const props = withDefaults(defineProps<Props>(), {
  hospitalId: ''
});

const appStore = useAppStore();

const { columns, data, loading, mobilePagination, getData } = useTable({
  apiFn: fetchPostExperimentListByHospitalId,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    hospitalId: props.hospitalId
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: $t('page.experiment.table.name'),
      align: 'center',
      width: 100,
      render: row => (
        <span
          class="cursor-pointer hover:text-primary"
          onClick={() => routerPushByKey('experiment_detail', { params: { id: row.id! } })}
        >
          {row.name}
        </span>
      )
    },
    {
      key: 'shortName',
      title: $t('page.experiment.table.shortName'),
      align: 'center',
      width: 100
    },
    {
      key: 'estimatedHuman',
      title: $t('page.experiment.table.estimatedHuman'),
      align: 'center',
      width: 180
    },
    {
      key: 'estimatedHosp',
      title: $t('page.experiment.table.estimatedHosp'),
      align: 'center',
      width: 100
    },
    {
      key: 'projectDiseList',
      title: $t('page.experiment.table.disease'),
      align: 'center',
      width: 120,
      render: row => {
        return row.diseaseName;
      }
    },
    {
      key: 'stage',
      title: $t('page.experiment.table.stage'),
      align: 'center',
      width: 100,
      render: row => {
        return row.stageName;
      }
    },
    {
      key: 'type',
      title: $t('page.experiment.table.type'),
      align: 'center',
      width: 100,
      render: row => {
        return row.typeName;
      }
    },
    {
      key: 'startTime',
      title: $t('page.experiment.table.startTime'),
      align: 'center',
      width: 220
    },
    {
      key: 'actualStartTime',
      title: $t('page.experiment.table.actualStartTime'),
      align: 'center',
      width: 220
    },
    {
      key: 'status',
      title: $t('page.experiment.table.status'),
      align: 'center',
      width: 60,
      render: row => {
        if (row.status === null) {
          return null;
        }

        const tagMap: Record<Api.Experiment.ExperimentStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning',
          2: 'error'
        };

        const label = $t(experimentStatusRecord[row.status as Api.Common.EnableStatus]);

        return <NTag type={tagMap[row.status as Api.Common.EnableStatus]}>{label}</NTag>;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 100,
      render: row => (
        <div class="flex-center justify-end gap-8px">
          {
            <NButton
              type="primary"
              ghost
              size="small"
              onClick={() => {
                window.$dialog?.warning({
                  title: '提示',
                  content: '确定要退出该试验吗?',
                  positiveText: '确定',
                  negativeText: '取消',
                  onPositiveClick: () => handleOut(row.id!)
                });
              }}
            >
              退出
            </NButton>
          }
        </div>
      )
    }
  ]
});
async function handleOut(id: string) {
  try {
    await fetchPostExperimentExitHospital({
      id,
      hospitalId: props.hospitalId!,
      userName: 'admin',
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
    });
    window.$message?.success('退出成功');
    getData();
  } catch {
    window.$message?.error('退出失败');
  }
}
</script>

<template>
  <div class="min-h-500px flex gap-16px overflow-hidden lt-sm:overflow-auto">
    <div class="flex-col-stretch gap-16px overflow-hidden">
      <NCard :bordered="false" size="small" class="data-common-table sm:flex-1-hidden card-wrapper">
        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="962"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="mobilePagination"
          class="sm:h-full"
        />
      </NCard>
    </div>
  </div>
</template>

<style scoped></style>
