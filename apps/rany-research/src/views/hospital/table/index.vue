<script setup lang="tsx">
import { ref } from 'vue';
import type { Ref } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { useTabsStore } from 'component-library';
import { fetchPostHospitalList } from '@/service/api';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { enableStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { fetchPutHospitalUpdate } from '@/service/api/hospital';
import { useRouterPush } from '@/hooks/common/router';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import HospitalSearch from './modules/hospital-search.vue';
import HospitalOperateModal from './modules/hospital-operate-modal.vue';
import DepartmentOperateModal from './modules/department-operate-modal.vue';

export type OperateType = NaiveUI.TableOperateType;

const { bool: visible, setTrue: openModal } = useBoolean();

const { routerPushByKey } = useRouterPush();

const appStore = useAppStore();

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination, searchParams } = useTable({
  apiFn: fetchPostHospitalList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    status: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: $t('page.hospital.table.name'),
      align: 'center',
      render: row => (
        <span
          class="cursor-pointer text-primary"
          onClick={() => {
            useTabsStore().addTab({
              title: row.name,
              path: `/hospital/detail/${row.id}`,
              query: {
                id: row.id
              },
              type: 'rany-research',
              closable: true,
              label: row.name,
              id: row.id
            });
            routerPushByKey('hospital_detail', { params: { id: row.id! } });
          }}
        >
          {row.name}
        </span>
      )
    },
    {
      key: 'address',
      title: $t('page.hospital.table.address'),
      align: 'center'
    },
    {
      key: 'projectNumber',
      title: $t('page.hospital.table.projectNumber'),
      align: 'center'
    },
    {
      key: 'userNumber',
      title: $t('page.hospital.table.userNumber'),
      align: 'center',
      width: 60
    },
    {
      key: 'createTime',
      title: $t('page.hospital.table.createTime'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'updateTime',
      title: $t('page.hospital.table.updateTime'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: $t('page.hospital.table.status'),
      align: 'center',
      render: row => {
        if (row.status === null) {
          return null;
        }

        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning'
        };

        const label = $t(enableStatusRecord[row.status as Api.Common.EnableStatus]);

        return <NTag type={tagMap[row.status as Api.Common.EnableStatus]}>{label}</NTag>;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 230,
      render: row => (
        <div class="flex-center justify-end gap-8px">
          {row.status === 1 ? (
            <NPopconfirm onPositiveClick={() => handleDisable(row)}>
              {{
                default: () => '确定要禁用该医院吗?',
                trigger: () => (
                  <NButton type="primary" ghost size="small">
                    禁用
                  </NButton>
                )
              }}
            </NPopconfirm>
          ) : (
            <NPopconfirm onPositiveClick={() => handleEnable(row)}>
              {{
                default: () => '确定要启用该医院吗?',
                trigger: () => (
                  <NButton type="primary" ghost size="small">
                    启用
                  </NButton>
                )
              }}
            </NPopconfirm>
          )}
          <NButton type="primary" ghost size="small" onClick={() => edit(row)}>
            {$t('common.edit')}
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => addDepartment(row)}>
            添加科室
          </NButton>
          {/* <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm> */}
        </div>
      )
    }
  ]
});

async function handleDisable(row: Api.Hospital.Hospital) {
  try {
    await fetchPutHospitalUpdate({
      ...row,
      status: 0,
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      userName: 'admin'
    });
    window.$message?.success('禁用成功');
    getData();
  } catch {
    window.$message?.error('禁用失败');
  }
}

async function handleEnable(row: Api.Hospital.Hospital) {
  try {
    await fetchPutHospitalUpdate({ ...row, status: 1, userId: 'ed2bcf1d7a584a69bf99bd0df530475a', userName: 'admin' });
    window.$message?.success('启用成功');
    getData();
  } catch {
    window.$message?.error('启用失败');
  }
}
const rules = {
  name: {
    required: true,
    message: '请输入科室名称',
    trigger: ['blur', 'input']
  },
  division: {
    required: true,
    message: '请选择科别',
    trigger: ['blur', 'change']
  },
  parentId: {
    required: true,
    message: '请选择上级组织',
    trigger: ['blur', 'change']
  }
};
const { checkedRowKeys, onBatchDeleted } = useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  onBatchDeleted();
}

const operateType = ref<OperateType>('add');

function handleAdd() {
  operateType.value = 'add';
  openModal();
}
/** the edit menu data or the parent menu data when adding a child menu */
const editingData: Ref<Api.Hospital.Hospital | null> = ref(null);

function edit(item: Api.Hospital.Hospital) {
  operateType.value = 'edit';
  editingData.value = { ...item };
  openModal();
}
const deptVisible = ref(false);
const deptEditingData: Ref<Api.Dept.Department | null> = ref(null);
function addDepartment(hosData: any) {
  // 深拷贝
  const hosDataCopy = JSON.parse(JSON.stringify(hosData));
  deptVisible.value = true;
  hosDataCopy.name = '';
  hosDataCopy.userId = 'ed2bcf1d7a584a69bf99bd0df530475a';
  hosDataCopy.userName = 'admin';
  deptEditingData.value = { ...hosDataCopy };
}

// 处理搜索事件
function handleSearch(value: string) {
  searchParams.name = value || null;
  getDataByPage();
}

// 处理筛选变化事件
function handleFilterChange(filterParams: any) {
  Object.assign(searchParams, filterParams);
  getDataByPage();
}
</script>

<template>
  <div class="flex-col-stretch flex-1 overflow-hidden lt-sm:overflow-auto">
    <NCard
      :bordered="false"
      size="small"
      class="data-common-table sm:flex-1-hidden"
      content-style="padding:16px 24px;display:flex;flex-direction:column;"
    >
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :disabled-delete="checkedRowKeys.length === 0"
        :loading="loading"
        search-placeholder="请输入医院名称"
        @add="handleAdd"
        @delete="handleBatchDelete"
        @refresh="getData"
        @search="handleSearch"
      >
        <template #filters>
          <HospitalSearch :search-params="searchParams" @filter-change="handleFilterChange" />
        </template>
      </TableHeaderOperation>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <HospitalOperateModal
        v-model:visible="visible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
      <DepartmentOperateModal
        v-model:visible="deptVisible"
        operate-type="add"
        :hospital-id="deptEditingData?.id"
        :row-data="deptEditingData"
        :rules="rules"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
