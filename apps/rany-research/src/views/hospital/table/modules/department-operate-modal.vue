<script setup lang="tsx">
import { NButton, NForm, NFormItem, NInput, NSelect, NSpace } from 'naive-ui';
import { computed, reactive, ref, watch } from 'vue';
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
import { useDeptStore } from '@/store/modules/database/dept';
import { fetchPostDepartmentEdit } from '@/service/api';

export type OperateType = NaiveUI.TableOperateType;

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit menu data */
  rowData?: Api.Dept.Department | null;
  hospitalId?: string;
  rules?: Record<string, any>;
}

const deptStore = useDeptStore();
const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: '添加科室',
    edit: '编辑科室'
  };
  return titles[props.operateType];
});

const formModel = reactive(createDefaultModel());

function createDefaultModel(): Api.Dept.Department & Api.Common.BaseRequestParams {
  return {
    name: '',
    division: '',
    parentId: '',
    status: 1,
    hospitalId: props.hospitalId,
    userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
    userName: 'admin',
    id: ''
  };
}

const divisionOptions = [
  { value: '临床科室', label: '临床科室' },
  { value: '管理部', label: '管理部' },
  { value: '医技科室', label: '医技科室' },
  { value: '研究中心', label: '研究中心' },
  { value: '职能科室', label: '职能科室' }
];
const parentOptions = computed(() => {
  if (!deptStore.departmentList) return [];
  return [
    ...deptStore.departmentList
      .filter(dept => dept.status === 1)
      .map(dept => ({
        label: dept.name,
        value: dept.id,
        division: dept.division
      }))
  ];
});

const loading = ref(false);
const submitLoading = ref(false);

// 是否可以编辑科别
const canEditDivision = computed(() => {
  const parentDept = deptStore.departmentList?.find(dept => dept.id === formModel.parentId);
  return parentDept?.name === '顶级' || !formModel.parentId;
});

// 监听上级组织变化，自动设置科别
watch(
  () => formModel.parentId,
  newVal => {
    if (newVal) {
      const parentDept = deptStore.departmentList?.find(dept => dept.id === newVal);
      if (parentDept && parentDept.name !== '顶级') {
        formModel.division = parentDept.division;
      }
    } else {
      formModel.division = '';
    }
    // 当上级组织变化时，触发一次表单验证
    if (formRef.value) {
      formRef.value.validate(errors => {
        if (!errors) {
          // 验证通过
        }
      });
    }
  }
);

async function handleFocus() {
  try {
    loading.value = true;
    await deptStore.getDepartmentList(props.hospitalId);
  } catch (error: any) {
    window.$message?.error(error?.message || '获取科室列表失败');
  } finally {
    loading.value = false;
  }
}

async function handleSubmit() {
  try {
    submitLoading.value = true;
    await validate();
    const submitData = { ...formModel };

    if (props.operateType === 'edit') {
      const res: any = await fetchPostDepartmentEdit({ ...submitData, id: props.rowData?.id });
      if (res.error) {
        window.$message?.error(res.data.message || '更新失败');
        return;
      }
      window.$message?.success($t('common.updateSuccess'));
    } else {
      const { error, data } = await deptStore.addDepartment(formModel);
      if (error) {
        window.$message?.error(data.data.message || '添加失败');
        return;
      }
      window.$message?.success($t('common.updateSuccess'));
    }
    closeDrawer();
    emit('submitted');
  } catch (error: any) {
    window.$message?.error(error?.message || '操作失败');
  } finally {
    submitLoading.value = false;
  }
}

function handleInitModel() {
  Object.assign(formModel, createDefaultModel());
  if (props.rowData) {
    Object.assign(formModel, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
  Object.assign(formModel, createDefaultModel());
  restoreValidation();
}

watch(
  () => props.operateType,
  () => {
    if (visible.value) {
      handleInitModel();
      restoreValidation();
    }
  }
);

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
    handleFocus();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-800px">
    <NScrollbar class="pr-20px">
      <NForm ref="formRef" :model="formModel" :rules="props.rules" label-placement="left" label-width="80">
        <div class="grid grid-cols-1 gap-4">
          <NFormItem label="科室名称" path="name">
            <NInput v-model:value="formModel.name" placeholder="请输入科室名称" />
          </NFormItem>

          <NFormItem label="上级组织" path="parentId">
            <NSelect
              v-model:value="formModel.parentId"
              :options="parentOptions"
              placeholder="请选择上级组织"
              :loading="loading"
              clearable
              @focus="handleFocus"
            />
          </NFormItem>

          <NFormItem label="科别" path="division">
            <NSelect
              v-model:value="formModel.division"
              :options="divisionOptions"
              placeholder="请选择科别"
              :disabled="!canEditDivision"
              clearable
            />
          </NFormItem>
        </div>
      </NForm>
    </NScrollbar>
    <template #footer>
      <NSpace justify="end" :size="16">
        <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
        <NButton type="primary" :loading="submitLoading" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
