<script setup lang="ts">
import { computed, nextTick, ref, toRefs, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { usePlanStore } from '@/store/modules/plan';
import { type FlowEdge, type FlowNode, useFlowChartStore } from '@/store/modules/flow-chart';
import FlowChartHeader from './modules/FlowChartHeader.vue';
import VueFlowContainer from './modules/VueFlowContainer.vue';
import '@vue-flow/core/dist/style.css';
import '@vue-flow/core/dist/theme-default.css';
import '@vue-flow/controls/dist/style.css';
import '@vue-flow/minimap/dist/style.css';

const route = useRoute();
const message = useMessage();
const planStore = usePlanStore();
const flowChartStore = useFlowChartStore();

const { planInfo } = toRefs(planStore);

// 加载状态
const loading = ref(false);

// 计划详情数据
const planDetail = ref<any>(null);

// 计算属性：从store获取节点和边数据
const flowNodes = computed((): FlowNode[] => {
  const storeNodes = flowChartStore.flowNodes;
  // 获取节点数组
  const nodes = storeNodes || [];

  // 确保是数组
  if (!Array.isArray(nodes)) {
    return [];
  }

  return nodes;
});

const flowEdges = computed((): FlowEdge[] => {
  const storeEdges = flowChartStore.flowEdges;
  return storeEdges || [];
});

// 备注相关状态
const commentBox = ref({
  visible: false,
  x: 0,
  y: 0
});

const comments = ref<Array<{ id: string; content: string; x: number; y: number }>>([]);

// 自动排版和缩放状态
const shouldAutoLayout = ref(false);
const currentScale = ref('auto');

// 处理评论按钮点击
const handleComment = (position: { x: number; y: number }) => {
  // 直接使用页面坐标，让VueFlowContainer内部处理转换
  commentBox.value = {
    visible: true,
    x: position.x,
    y: position.y
  };
};

// 处理布局按钮点击
const handleLayout = () => {
  shouldAutoLayout.value = true;
  // 重置状态以便下次可以再次触发
  nextTick(() => {
    shouldAutoLayout.value = false;
  });
};

// 处理缩放变化
const handleScale = (value: string) => {
  currentScale.value = value;
  message.info(`缩放已设置为: ${value}`);
};

// 保存备注
const handleSaveComment = (content: string) => {
  const newComment = {
    id: Date.now().toString(),
    content,
    x: commentBox.value.x,
    y: commentBox.value.y
  };
  comments.value.push(newComment);
  commentBox.value.visible = false;
  message.success('备注已保存');
};

// 关闭评论框
const handleCloseComment = () => {
  commentBox.value.visible = false;
};

// 处理评论位置变化
const handleCommentPositionChange = (position: { x: number; y: number }) => {
  commentBox.value.x = position.x;
  commentBox.value.y = position.y;
};

// 处理已保存评论的位置更新
const handleUpdateCommentPosition = (commentId: string, position: { x: number; y: number }) => {
  const comment = comments.value.find(c => c.id === commentId);
  if (comment) {
    comment.x = position.x;
    comment.y = position.y;
    // 触发响应式更新
    comments.value = [...comments.value];
  }
};

// 处理删除评论
const handleDeleteComment = (commentId: string) => {
  const index = comments.value.findIndex(c => c.id === commentId);
  if (index > -1) {
    comments.value.splice(index, 1);
    message.success('评论已删除');
  }
};

const VueFlowContainerKey = ref(0);

// 数据转换函数 - 将API数据转换为流程图数据
const transformDataToFlowChart = async (responseData: any) => {
  // 检查响应格式
  if (!responseData || !responseData.data || !responseData.data.clinicPlan) {
    message.warning('数据格式不正确');
    return;
  }

  const { clinicPlan } = responseData.data;
  const { stages } = clinicPlan;

  if (!stages || stages.length === 0) {
    message.warning('没有找到阶段数据');
    flowChartStore.resetNodesAndEdges();
    VueFlowContainerKey.value += 1;
    return;
  }

  // 清空当前流程图数据
  try {
    // 获取当前节点和边
    const currentNodes = flowNodes.value;
    const currentEdges = flowEdges.value;

    // 一次性清空所有节点和边
    if (currentNodes.length > 0 || currentEdges.length > 0) {
      // 清空现有节点
      [...currentNodes].forEach(node => flowChartStore.removeNode(node.id));
      // 清空现有边
      [...currentEdges].forEach(edge => flowChartStore.removeEdge(edge.id));

      // 确保清空操作完成
      await nextTick();
      await new Promise<void>(resolve => {
        setTimeout(() => resolve(), 100);
      });
    }

    // 转换stages数据为流程图节点和边
    let xPosition = 80;
    // 使用固定的中心位置，让fitView自动调整
    const yPosition = 0; // 使用0作为中心，让fitView处理居中

    // 准备所有节点数据
    const nodesToAdd: any[] = [];
    const edgesToAdd: any[] = [];

    // 先构建所有节点和边的数据结构
    stages.forEach((stage: any, index: number) => {
      // 为每个阶段创建分组节点
      // 不管有没有solutions都要显示阶段节点
      const solutionNodes =
        stage.solutions && stage.solutions.length > 0
          ? stage.solutions.map((solution: any, solutionIndex: number) => ({
              id: `${stage.id}-solution-${solutionIndex}`,
              data: {
                label: solution.soluName,
                days: solution.soluType === 2 ? `${solution.restDayCount}天` : `${solution.dayCount}天`
              }
            }))
          : []; // 如果没有solutions，使用空数组

      const groupNode = {
        id: stage.id,
        type: 'group', // 直接使用group类型，与nodeTypes注册的key一致
        position: { x: xPosition, y: yPosition },
        data: {
          type: 'default',
          title: stage.name,
          topLabel: stage.name, // 根据阶段设置不同的标签
          nodes: solutionNodes
        }
      };

      nodesToAdd.push(groupNode);

      // 如果不是第一个节点，创建连接到前一个节点的边
      if (index > 0) {
        const edge = {
          id: `edge-${stages[index - 1].id}-${stage.id}`,
          source: stages[index - 1].id,
          target: stage.id,
          type: 'smoothstep',
          animated: true
        };

        edgesToAdd.push(edge);
      }

      xPosition += 200; // 下一个节点的位置
    });

    // 一次性添加所有节点
    if (nodesToAdd.length > 0) {
      // 依次添加每个节点，并等待响应式更新
      // eslint-disable-next-line no-await-in-loop
      for (const node of nodesToAdd) {
        flowChartStore.addNode(node as any);
        // 每添加一个节点等待一下，确保响应式更新
        // eslint-disable-next-line no-await-in-loop
        await nextTick();
      }

      // 确保所有节点添加完成
      await nextTick();
      await new Promise<void>(resolve => {
        setTimeout(() => resolve(), 200);
      });
    }

    // 等待节点渲染完成后再添加边
    if (edgesToAdd.length > 0) {
      // 添加所有边
      // eslint-disable-next-line no-await-in-loop
      for (const edge of edgesToAdd) {
        flowChartStore.addEdge(edge);
        // eslint-disable-next-line no-await-in-loop
        await nextTick();
      }

      // 确保所有边添加完成
      await nextTick();
    }

    // 最终确认数据已更新
    await nextTick();

    // 同步流程图数据到目录树
    flowChartStore.syncNodesToTreeData();

    await new Promise<void>(resolve => {
      setTimeout(() => resolve(), 300);
    });
  } catch {
    message.error('流程图数据转换失败');
  }
};

// 加载计划数据
const loadPlanData = async (id: string) => {
  await planStore.getPlanInfoPreview(id);
  if (planInfo.value) {
    const responseData = planInfo.value;

    //   // 检查数据结构
    if (responseData.clinicPlan) {
      // 保存计划详情
      planDetail.value = responseData;

      // 构造转换函数需要的数据格式
      const transformData = {
        data: {
          clinicPlan: responseData.clinicPlan
        }
      };

      // 转换数据为流程图节点
      await transformDataToFlowChart(transformData);
    }
  } else {
    message.warning('未获取到计划数据');
  }
};

// 页面挂载时加载数据

watch(
  () => route.params.id,
  async (newVal, _oldVal) => {
    loading.value = true;
    if (newVal) {
      await loadPlanData(newVal as string);
    }
    loading.value = false;
  },
  { immediate: true, deep: true }
);

// 页面标题 - 直接从planStore获取
const pageTitle = computed(() => {
  return planStore.planBaseInfo.name || planDetail.value?.clinicPlan?.name || '-';
});
</script>

<template>
  <div class="relative flex flex-col flex-1">
    <!-- 头部组件 -->
    <FlowChartHeader :title="pageTitle" @comment="handleComment" @layout="handleLayout" @scale="handleScale" />

    <!-- 流程图区域 -->
    <VueFlowContainer
      :key="VueFlowContainerKey"
      :loading="loading"
      :nodes="flowNodes"
      :edges="flowEdges"
      :should-auto-layout="shouldAutoLayout"
      :scale-value="currentScale"
      :comment-box="commentBox"
      :comments="comments"
      @comment-save="handleSaveComment"
      @comment-close="handleCloseComment"
      @comment-position-change="handleCommentPositionChange"
      @update-comment-position="handleUpdateCommentPosition"
      @delete-comment="handleDeleteComment"
    />
  </div>
</template>
