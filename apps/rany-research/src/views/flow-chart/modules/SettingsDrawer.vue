<script setup lang="ts">
import { ref } from 'vue';
import { NButton, NDrawer } from 'naive-ui';
import SettingsContent from './SettingsContent.vue';

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const emit = defineEmits<Emits>();

const showButton = ref(true);
const drawerVisible = ref(false);

// 处理设置按钮点击
const handleSettingsClick = () => {
  showButton.value = false;
  drawerVisible.value = true;
  emit('update:visible', true);
};

// 处理抽屉关闭
const handleDrawerClose = () => {
  drawerVisible.value = false;
  showButton.value = true;
  emit('update:visible', false);
};
</script>

<template>
  <div>
    <!-- 设置按钮 -->
    <NButton
      v-if="showButton"
      size="small"
      class="settings-button absolute right-4 top-4 z-40 shadow-lg"
      @click="handleSettingsClick"
    >
      <template #icon>
        <icon-line-md-cog />
      </template>
      设置
    </NButton>

    <!-- 右侧抽屉 - 无遮罩 -->
    <NDrawer
      v-model:show="drawerVisible"
      :width="240"
      placement="right"
      :mask-closable="false"
      :show-mask="false"
      :close-on-esc="false"
      to=".vue-flow-wrapper"
      class="settings-drawer"
      @update:show="handleDrawerClose"
    >
      <SettingsContent @close="handleDrawerClose" />
    </NDrawer>
  </div>
</template>

<style lang="scss">
/* 确保抽屉在VueFlow内部正确显示 */
:deep(.n-drawer) {
  position: absolute !important;
}

.settings-button.n-button {
  background-color: white !important;
}

.settings-drawer :deep(.n-drawer-container) {
  position: absolute !important;
}

.settings-drawer.n-drawer.n-drawer--right-placement {
  right: 12px !important;
  top: 60px !important; /* 确保不遮挡头部，头部高度约为70-80px */
  background-color: transparent !important;
  margin-bottom: 12px !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  height: calc(100% - 70px) !important; /* 减去头部高度和边距 */
}

.settings-drawer .n-drawer-content-wrapper {
  background-color: white !important;
}

.settings-drawer .n-drawer-body-content-wrapper {
  padding: 0 16px !important;
}
</style>
