<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute,useRouter } from 'vue-router';
import { NButton, NIcon, NSelect, NTooltip } from 'naive-ui';
import { usePlanStore } from '@/store/modules/plan';
import { useTabsStore } from 'component-library';
import OperateDrawer from '@/views/program/plan/modules/operate-drawer.vue';


const tabsStore = useTabsStore();

const route = useRoute();
const router = useRouter();
const planStore = usePlanStore();

const props = defineProps<{
  title?: string;
}>();

interface Emits {
  (e: 'comment', position: { x: number; y: number }): void;
  (e: 'layout'): void;
  (e: 'scale', value: string): void;
}

const emit = defineEmits<Emits>();

// 获取计划ID
const planId = computed(() => {
  if (route.params.id) {
    return route.params.id as string;
  }
  return route.query.planId as string;
});

// 保存状态
const saveLoading = ref(false);
const publishLoading = ref(false);

// 模态弹窗状态
const modalVisible = ref(false);
const operateType = ref<'add' | 'edit'>('edit');

const ScaleOptions = [
  { label: '自适应', value: 'auto' },
  { label: '50%', value: '50%' },
  { label: '100%', value: '100%' },
  { label: '150%', value: '150%' }
];
const scale = ref('auto');

// 处理评论按钮点击
const handleCommentClick = (event: MouseEvent) => {
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  emit('comment', {
    x: rect.left,
    y: rect.bottom + 5
  });
};

// 处理布局按钮点击
const handleLayoutClick = () => {
  emit('layout');
};

// 处理缩放变化
const handleScaleChange = (value: string) => {
  emit('scale', value);
};

//处理预览
const handlePreview = async() =>{
  await tabsStore.addTab({
    id: route.params.id,
    title: props.title,
    label: props.title,
    path: `/program/plan-detail/${route.params.id}`,
    closable: true,
    key: route.params.id,
    type: 'rany-research'
  });
  router.push(`/program/plan-detail/${route.params.id}`)
}

// 处理保存
const handleSave = async () => {
  try {
    saveLoading.value = true;

    // 更新planStore中的formModel，使用planBaseInfo的数据
    planStore.formModel = {
      id: planId.value,
      name: planStore.planBaseInfo.name,
      diseaseId: planStore.planBaseInfo.diseaseId,
      riskId: planStore.planBaseInfo.riskId,
      restriction: planStore.planBaseInfo.restriction || 1,
      projectId: planStore.planBaseInfo.projectId || ''
    };

    // 调用更新接口
    await planStore.updatePlan();
    window.$message?.success('保存成功');
    handleCancel();
  } catch {
    window.$message?.error('保存失败');
  } finally {
    saveLoading.value = false;
  }
};

// 处理发布
const handlePublish = async () => {
  try {
    publishLoading.value = true;

    // 先保存当前数据
    planStore.formModel = {
      id: planId.value,
      name: planStore.planBaseInfo.name,
      diseaseId: planStore.planBaseInfo.diseaseId,
      riskId: planStore.planBaseInfo.riskId,
      restriction: planStore.planBaseInfo.restriction || 1,
      projectId: planStore.planBaseInfo.projectId || '',
      status: 1 // 设置为发布状态
    };

    // 调用更新接口
    await planStore.updatePlan();

    window.$message?.success('发布成功');

    // 关闭tab
    handleCancel()
  } catch {
    window.$message?.error('发布失败');
  } finally {
    publishLoading.value = false;
  }
};

const handleCancel = async () => {
  const tabId = JSON.parse(localStorage.getItem('tabs') || '{}').activeTab;
  await tabsStore.closeTab(tabId);
  const tabs = JSON.parse(localStorage.getItem('tabs') || '{}');
  if (tabs.tabs.length > 0) {
    router.push(tabs.tabs[tabs.tabs.length - 1].path);
  }
};

// 处理打开模态弹窗
const handleOpenDrawer = () => {
  operateType.value = 'edit';
  modalVisible.value = true;
};
</script>

<template>
  <div class="w-full bg-white shadow-[2px_2px_12px_0px_rgba(160,209,255,0.15)]">
    <!-- 顶部工具栏 -->
    <div class="flex items-center justify-between px-4 py-2">
      <!-- 左侧返回按钮和标题 -->
      <div class="flex items-center gap-3">
        <!--
 <NButton text size="small" class="text-gray-600 dark:text-gray-400">
          <NIcon size="16">
            <icon-local-left />
          </NIcon>
        </NButton>
-->
        <div class="ml-2 flex flex-col">
          <div class="flex items-center gap-2">
            <h1 class="text-[12px] font-medium text-gray-900 dark:text-white">
              {{ props.title || planStore.planBaseInfo.name || '-' }}
            </h1>
             <NIcon
          size="16"
          class="cursor-pointer text-gray-600 transition-colors hover:text-blue-600"
          @click="handleOpenDrawer"
        >
          <icon-local-write />
        </NIcon>
          </div>

          <span class="text-[10px] text-gray-500 dark:text-gray-400">
            已自动保存 ·
            {{
              new Date().toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
              })
            }}
          </span>
        </div>
      </div>

      <!-- 中间按钮 -->
      <div class="flex items-center gap-1">
        <!-- 评论按钮 -->
        <NTooltip placement="bottom">
          <template #trigger>
            <div
              class="h-[24px] w-[27px] flex cursor-pointer items-center justify-center rounded-[6px] hover:bg-gray-100"
              @click="handleCommentClick"
            >
              <icon-local-comment />
            </div>
          </template>
          <div>
            <p>评论</p>
          </div>
        </NTooltip>

        <!-- 布局按钮 -->
        <NTooltip placement="bottom">
          <template #trigger>
            <div
              class="h-[24px] w-[27px] flex cursor-pointer items-center justify-center rounded-[6px] hover:bg-gray-100"
              @click="handleLayoutClick"
            >
              <icon-local-layout />
            </div>
          </template>
          <p>自动排版</p>
        </NTooltip>

        <!-- 缩放控制 -->
        <NTooltip placement="bottom">
          <template #trigger>
            <div
              class="h-[24px] w-[27px] flex cursor-pointer items-center justify-center rounded-[6px] hover:bg-gray-100"
            >
              <icon-local-scale />
            </div>
          </template>
          <p>缩放</p>
        </NTooltip>
        <NSelect
          v-model:value="scale"
          size="small"
          :options="ScaleOptions"
          class="w-22 text-gray-400"
          @update:value="handleScaleChange"
        />
      </div>
      <!-- 右侧操作按钮 -->
      <div class="flex items-center gap-2">
        <!-- 预览按钮 -->
        <NButton  @click="handlePreview"  secondary size="small" class="border-blue-200 text-blue-600 hover:bg-blue-50">预览</NButton>

        <!-- 详情按钮 -->
        <NButton secondary size="small" class="text-gray-700 dark:text-gray-300">详情</NButton>

        <!-- 保存按钮 -->
        <NButton
          secondary
          size="small"
          class="text-gray-700 dark:text-gray-300"
          :loading="saveLoading"
          @click="handleSave"
        >
          保存
        </NButton>

        <!-- 发布按钮 -->
        <NButton
          type="primary"
          size="small"
          class="bg-blue-600 hover:bg-blue-700"
          :loading="publishLoading"
          @click="handlePublish"
        >
          发布
        </NButton>
      </div>
    </div>

    <!-- 编辑计划模态弹窗 -->
    <OperateDrawer
      v-model:visible="modalVisible"
      :operate-type="operateType"
      :row-data="planStore.planBaseInfo"
    />
  </div>
</template>

<style scoped>
/* 自定义样式 */
:deep(.n-button) {
  border-radius: 6px;
}

:deep(.n-button--text-type) {
  --n-padding: 6px 8px;
}

:deep(.n-button--small-type) {
  --n-height: 28px;
  --n-font-size: 13px;
}

:deep(.n-upload-trigger) {
  background-color: transparent !important;
  border: none !important;
  padding: 0 !important;
}

/* 工具栏按钮悬停效果 */
.n-button:hover {
  transform: none;
}
</style>
