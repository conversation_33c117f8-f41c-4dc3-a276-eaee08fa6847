<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { NButton, NDrawerContent, NInput, NTabPane, NTabs, useMessage } from 'naive-ui';
import { type FlowEdge, type FlowNode, type GroupNodeData, useFlowChartStore } from '@/store/modules/flow-chart';
import { fetchPostClinicStageAdd } from '@/service/api/clinicPlan';
import DirectoryTree from './DirectoryTree.vue';
import ProgramDragList from './ProgramDragList.vue';

interface Emits {
  (e: 'close'): void;
}

const emit = defineEmits<Emits>();

const route = useRoute();
const message = useMessage();
const activeTab = ref('outline');
const searchValue = ref('');

// 使用流程图store
const flowChartStore = useFlowChartStore();

// 获取计划ID
const planId = computed(() => {
  // 优先从路由参数获取（新路由结构）
  if (route.params.id) {
    return route.params.id as string;
  }
  // 兼容旧的查询参数方式
  return route.query.planId as string;
});

// Tab选项
const tabs = [
  { key: 'outline', label: '大纲' },
  { key: 'schemes', label: '方案库' },
  // { key: 'plans', label: '计划' }
];

// 处理搜索
const handleSearch = (value: string) => {
  searchValue.value = value;
  // 这里可以添加搜索逻辑
};

// 处理添加标签页
const handleAddTab = async () => {
  if (!planId.value) {
    message.error('无法获取计划ID');
    return;
  }

  try {
    // 获取当前流程图中的节点总数，用于生成新阶段名称和顺序号
    const currentNodeCount = flowChartStore.flowNodes.length;
    const newStageNumber = currentNodeCount + 1;
    const newStageName = `新阶段${newStageNumber}`;

    // 准备接口参数
    const stageData = {
      clinicPlanId: planId.value,
      orderNumber: newStageNumber,
      relation: newStageName,
      type: 0, // 0表示阶段类型
      id: ''
    };

    // 调用接口添加阶段
    const { data: stageId, error } = await fetchPostClinicStageAdd(stageData);

    if (error || !stageId) {
      message.error('添加阶段失败');
      return;
    }

    // 创建新的分组节点数据
    const newGroupNodeData: GroupNodeData = {
      title: newStageName,
      nodes: [], // 方案为空
      topLabel: `新阶段${newStageNumber}`
    };

    // 创建新的流程图节点，使用接口返回的ID
    const newFlowNode: FlowNode = {
      id: stageId.toString(), // 使用接口返回的ID
      type: 'group', // 使用group类型
      position: { x: 0, y: 0 }, // 初始位置，后续会自动调整
      data: newGroupNodeData
    };

    // 添加节点到store
    flowChartStore.addNode(newFlowNode);

    // 如果已有节点，自动连接到最后一个节点
    if (currentNodeCount > 0) {
      const lastNode = flowChartStore.flowNodes[currentNodeCount - 1];
      if (lastNode) {
        const newEdge: FlowEdge = {
          id: `edge-${lastNode.id}-${stageId}`,
          source: lastNode.id,
          target: stageId.toString(),
          type: 'custom',
          animated: true,
          style: { strokeWidth: 2, stroke: '#72BBFF' }
        };
        flowChartStore.addEdge(newEdge);
      }
    }

    message.success(`已添加新阶段: ${newStageName}`);
  } catch (err) {
    console.error('添加阶段失败:', err);
    message.error('添加阶段失败，请重试');
  }
};
</script>

<template>
  <NDrawerContent class="directory-content">
    <!-- 头部 -->
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <icon-local-menu />
          <span class="text-[14px] font-medium">目录</span>
        </div>
        <NButton size="small" text @click="emit('close')">✕</NButton>
      </div>
    </template>

    <!-- 内容区域 -->
    <div class="h-full flex flex-col">
      <!-- Tab切换 -->
      <div class="mb-3 flex items-center justify-between">
        <NTabs v-model:value="activeTab" type="line" size="small" class="flex-1">
          <NTabPane v-for="tab in tabs" :key="tab.key" :name="tab.key" :tab="tab.label" />
        </NTabs>
        <Transition name="fade-scale">
          <NButton
            v-show="activeTab === 'outline'"
            size="small"
            circle
            class="ml-2 transition-all duration-200 hover:scale-110 hover:bg-primary/10"
            @click="handleAddTab"
          >
            <template #icon>
              <SvgIcon icon="tabler:plus" class="h-4 w-4" />
            </template>
          </NButton>
        </Transition>
      </div>

      <!-- 搜索框 -->
      <div class="mb-4">
        <NInput v-model:value="searchValue" placeholder="搜索..." size="small" clearable @input="handleSearch">
          <template #prefix>
            <icon-line-md:search />
          </template>
        </NInput>
      </div>

      <!-- 树形结构内容 -->
      <div class="flex-1 overflow-auto">
        <!-- 大纲tab显示树形结构 -->
        <DirectoryTree v-if="activeTab === 'outline'" :tab="activeTab" :search-value="searchValue" />

        <!-- 方案库tab显示拖拽列表 -->
        <ProgramDragList v-else-if="activeTab === 'schemes'" />

        <!-- 计划tab暂时显示树形结构 -->
        <!-- <DirectoryTree v-else :tab="activeTab" :search-value="searchValue" /> -->
      </div>
    </div>
  </NDrawerContent>
</template>

<style lang="scss">
:deep(.n-tabs-nav) {
  padding: 0;
}

.directory-content {
  .n-drawer-header {
    padding: 12px 16px !important;
    border-bottom: none !important;
  }
  .n-tabs-tab-pad {
    --n-tab-gap: 12px;
    --n-tab-text-color-active: #0085ff;
  }
}

:deep(.n-tabs-tab) {
  padding: 8px 12px;
}
</style>
