<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { NButton, NEllipsis, NSpin } from 'naive-ui';
import { useRoute } from 'vue-router';
import { fetchGetSuiteItemList } from '@/service/api/suite';
import ItemModal from '@/components/advanced/program-table/item-value/item-modal.vue';

const route = useRoute();
const itemModalRef = ref<InstanceType<typeof ItemModal>>();

// 获取计划ID
const planId = computed(() => {
  // 优先从路由参数获取（新路由结构）
  if (route.params.id) {
    return route.params.id as string;
  }
  // 兼容旧的查询参数方式
  return route.query.planId as string;
});

// 项目数据状态
const projectItems = ref<Api.Suite.SuiteItem[]>([]);
const loading = ref(false);

// 获取项目列表数据
const getProjectItems = async () => {
  if (!planId.value) return;

  loading.value = true;
  try {
    const { error, data } = await fetchGetSuiteItemList(planId.value);
    if (!error && data) {
      // 过滤掉已删除的项目（使用any类型处理delFlag属性）
      projectItems.value = data.filter((item: any) => item.delFlag !== 1);
      console.log(projectItems.value);
    }
  } catch (err) {
    console.error('获取项目列表失败:', err);
  } finally {
    loading.value = false;
  }
};

// 添加项目
const handleAddProject = () => {
  itemModalRef.value?.show();
};

// 处理项目添加成功后刷新列表
const handleItemAdded = () => {
  getProjectItems();
};

// 组件挂载时获取数据
onMounted(() => {
  getProjectItems();
});
</script>

<template>
  <div>
    <!-- 加载状态 -->
    <NSpin :show="loading">
      <!-- 项目列表 -->
      <div class="space-y-1">
        <!-- 项目头部 -->
        <div class="flex rounded-lg bg-gray-100 px-2 py-1 text-[12px] font-500 text-gray-600">
          <div class="w-1/3 whitespace-nowrap">项目名称</div>
          <div class="ml-2 flex-1 text-left">项目详情</div>
        </div>

        <!-- 项目列表项 -->
        <div
          v-for="(item, index) in projectItems"
          :key="item.id || index"
          class="flex rounded-lg bg-gray-50 px-2 py-1 text-[12px]"
        >
          <div class="w-1/3 text-gray-700">
            <NEllipsis
              :tooltip="{
                placement: 'right',
                showArrow: false,
                contentStyle: 'max-width: 120px; font-size: 12px; padding: 0; '
              }"
            >
              {{ item.name }}
            </NEllipsis>
          </div>
          <div class="ml-2 flex-1 text-left text-gray-700">
            <!-- 根据项目类型显示不同的详情信息 -->
            <NEllipsis
              :tooltip="{
                placement: 'right',
                showArrow: false,
                contentStyle: 'max-width: 300px; font-size: 12px; padding: 2px 4px; line-height: 1;'
              }"
            >
              <!-- 这里的内容暂定 -->
              <span></span>
            </NEllipsis>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && projectItems.length === 0" class="flex justify-center py-4 text-[12px] text-gray-500">
          暂无项目数据
        </div>

        <!-- 添加项目按钮 -->
        <div class="flex justify-center">
          <NButton type="primary" class="h-[27px] w-full rounded-lg" @click="handleAddProject">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            添加项目
          </NButton>
        </div>
      </div>
    </NSpin>

    <!-- 添加项目弹窗 -->
    <ItemModal ref="itemModalRef" :module-id="planId" @submitted="handleItemAdded" />
  </div>
</template>

<style lang="scss" scoped></style>
