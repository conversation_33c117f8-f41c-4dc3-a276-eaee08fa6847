import { nextTick } from 'vue'
import type { Node, Edge } from '@vue-flow/core'

export interface LayoutOptions {
  direction?: 'horizontal' | 'vertical'
  nodeSpacing?: number
  containerWidth?: number
  containerHeight?: number
}

export interface NodeMetrics {
  width: number
  height: number
  mainNodeHeight: number
  topBoxHeight: number
}

/**
 * 简单的水平布局 Composable
 * 专门用于连接点对齐的布局算法
 */
export function useLayout() {

  /**
   * 估算节点宽度
   */
  const estimateNodeWidth = (node: Node): number => {
    const baseWidth = 120
    const label = node.data?.topLabel || node.data?.groupTitle || node.data?.label || ''
    const estimatedWidth = Math.max(baseWidth, label.length * 8 + 40)
    return Math.min(estimatedWidth, 200)
  }

  /**
   * 估算节点高度
   */
  const estimateNodeHeight = (node: Node): number => {
    const topBoxHeight = 24
    const mainNodeMinHeight = 72
    const childNodeHeight = 32
    const childNodeSpacing = 8
    const padding = 16

    let totalHeight = topBoxHeight + mainNodeMinHeight

    // 计算子节点高度
    if (node.data?.nodes && Array.isArray(node.data.nodes)) {
      const childCount = node.data.nodes.length
      if (childCount > 0) {
        const childrenHeight = childCount * childNodeHeight + (childCount - 1) * childNodeSpacing
        totalHeight += childrenHeight + padding
      }
    }

    return totalHeight
  }

  /**
   * 估算主节点高度（连接点所在的部分）
   */
  const estimateMainNodeHeight = (node: Node): number => {
    const baseHeight = 72 // 基础主节点高度

    // 根据内容调整高度
    const label = node.data?.groupTitle || node.data?.label || ''
    const lines = Math.ceil(label.length / 10) // 假设每行10个字符
    const additionalHeight = Math.max(0, (lines - 1) * 20) // 每行额外20px

    return baseHeight + additionalHeight
  }

  /**
   * 获取节点的实际尺寸信息
   */
  const getNodeMetrics = async (nodes: Node[]): Promise<NodeMetrics[]> => {
    // 等待DOM更新
    await nextTick()

    const metrics: NodeMetrics[] = []

    for (const node of nodes) {
      let width = estimateNodeWidth(node)
      let height = estimateNodeHeight(node)
      let mainNodeHeight = estimateMainNodeHeight(node)
      const topBoxHeight = 24

      // 尝试获取实际DOM尺寸
      const nodeElement = document.querySelector(`[data-id="${node.id}"]`)
      if (nodeElement) {
        const rect = nodeElement.getBoundingClientRect()
        if (rect.width > 0 && rect.height > 0) {
          width = rect.width
          height = rect.height

          // 尝试获取主节点部分的高度
          const mainElement = nodeElement.querySelector('.parent-node, .group-node-container')
          if (mainElement) {
            const mainRect = mainElement.getBoundingClientRect()
            if (mainRect.height > 0) {
              mainNodeHeight = mainRect.height
            }
          }
        }
      }

      metrics.push({
        width,
        height,
        mainNodeHeight,
        topBoxHeight
      })
    }

    return metrics
  }

  /**
   * 水平布局算法 - 连接点对齐
   */
  const layoutNodes = (
    nodes: Node[],
    metrics: NodeMetrics[],
    options: LayoutOptions = {}
  ): Node[] => {
    const {
      nodeSpacing = 80,
      containerWidth = 1200,
      containerHeight = 600
    } = options

    if (nodes.length === 0) return nodes

    // 计算总宽度
    const totalWidth = metrics.reduce((sum, metric, index) => {
      return sum + metric.width + (index > 0 ? nodeSpacing : 0)
    }, 0)

    // 计算起始X坐标（水平居中）
    const startX = Math.max(50, (containerWidth - totalWidth) / 2)

    // 定义统一的连接点水平线（容器垂直中心）
    const connectionLineY = containerHeight / 2

    // 布局每个节点
    let currentX = startX
    const layoutedNodes = nodes.map((node, index) => {
      const metric = metrics[index]

      // 计算节点Y坐标，使连接点对齐到统一水平线
      // 连接点位置 = nodeY + topBoxHeight + mainNodeHeight / 2
      // 要使连接点对齐：nodeY + topBoxHeight + mainNodeHeight / 2 = connectionLineY
      // 所以：nodeY = connectionLineY - topBoxHeight - mainNodeHeight / 2
      const nodeY = connectionLineY - metric.topBoxHeight - metric.mainNodeHeight / 2

      const position = {
        x: currentX,
        y: nodeY
      }

      currentX += metric.width + nodeSpacing

      return {
        ...node,
        position
      }
    })

    return layoutedNodes
  }

  /**
   * 自动布局主函数
   */
  const autoLayout = async (
    nodes: Node[],
    edges: Edge[],
    options: LayoutOptions = {}
  ): Promise<{ nodes: Node[], edges: Edge[] }> => {
    if (nodes.length === 0) {
      return { nodes, edges }
    }

    // 获取节点尺寸信息
    const metrics = await getNodeMetrics(nodes)

    // 应用布局
    const layoutedNodes = layoutNodes(nodes, metrics, options)

    return {
      nodes: layoutedNodes,
      edges // 边不需要修改，Vue Flow会自动重新计算路径
    }
  }

  return {
    autoLayout,
    getNodeMetrics,
    layoutNodes,
    estimateNodeWidth,
    estimateNodeHeight,
    estimateMainNodeHeight
  }
}
