<script setup lang="ts">
import { computed, h, nextTick, ref, watch } from 'vue';
import { Handle, Position, VueFlow, useVueFlow } from '@vue-flow/core';
import { Background } from '@vue-flow/background';
import { Controls } from '@vue-flow/controls';
import { NDropdown, useMessage } from 'naive-ui';
import {
  type FlowEdge,
  type FlowNode,
  type GroupNodeData,
  isGroupNodeData,
  useFlowChartStore
} from '@/store/modules/flow-chart';
import CommentBox from '@/components/flow-chart/CommentBox.vue';
import { fetchPostClinicStageAddProgramOrRest, fetchPostClinicStageUpdate, fetchGetClinicStageDelete, fetchGetClinicStageCopy } from '@/service/api/clinicPlan';
import CommentOverlay from './CommentOverlay.vue';
import DirectoryDrawer from './DirectoryDrawer.vue';
import SettingsDrawer from './SettingsDrawer.vue';
import CustomEdge from './CustomEdge.vue';
import { useLayout } from './useLayout';
import PlanModalAddRest from '@/views/program/plan/modules/plan-modal-add-rest.vue';
import { useUserStore } from '@/store/modules/user';

// 定义props
interface Props {
  nodes: FlowNode[];
  edges: FlowEdge[];
  shouldAutoLayout?: boolean;
  scaleValue?: string;
  commentBox?: {
    visible: boolean;
    x: number;
    y: number;
  };
  comments?: Array<{ id: string; content: string; x: number; y: number }>;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  nodes: () => [],
  edges: () => [],
  shouldAutoLayout: false, // 默认关闭自动布局，使用vue-flow默认行为
  scaleValue: 'auto',
  commentBox: () => ({ visible: false, x: 0, y: 0 }),
  comments: () => []
});

// 定义emits
interface Emits {
  (e: 'comment-save', content: string): void;
  (e: 'comment-close'): void;
  (e: 'comment-position-change', position: { x: number; y: number }): void;
  (e: 'update-comment-position', commentId: string, position: { x: number; y: number }): void;
  (e: 'delete-comment', commentId: string): void;
}

const emit = defineEmits<Emits>();

// 简化的内部状态
const isInitialized = ref(false);
const internalNodes = ref<any[]>([]);
const internalEdges = ref<any[]>([]);

// 选中节点相关的连接线高亮
const selectedNodeId = ref<string | null>(null);

// 添加编辑状态管理
const editingNodeId = ref<string | null>(null);
const editingValue = ref<string>('');
const originalValue = ref<string>('');
// 是否已经聚焦过的标记
const hasFocused = ref(false);

const flowChartStore = useFlowChartStore();
const { onNodeClick, fitView, setNodes, setEdges, onConnect, addEdges, zoomTo, onPaneClick } = useVueFlow();
const message = useMessage();

// 休疗弹窗引用
const restModalRef = ref<InstanceType<typeof PlanModalAddRest>>();
const currentRestNodeId = ref<string>('');

// 防抖函数，用于延迟执行同步操作
let syncTreeDataTimer: NodeJS.Timeout | null = null;
const debouncedSyncTreeData = () => {
  if (syncTreeDataTimer) {
    clearTimeout(syncTreeDataTimer);
  }
  syncTreeDataTimer = setTimeout(() => {
    flowChartStore.syncNodesToTreeData();
  }, 200);
};

// 视图状态保护机制
const preserveViewState = async (callback: () => Promise<void> | void) => {
  // 保存当前的视图状态
  const viewport = document.querySelector('.vue-flow__viewport') as HTMLElement;
  const currentTransform = viewport?.style.transform || '';
  const currentZoom = viewport?.style.zoom || '';
  const activeElement = document.activeElement as HTMLElement;

  try {
    // 执行回调函数
    await callback();

    // 等待DOM更新
    await nextTick();

    // 恢复视图状态
    if (viewport && currentTransform) {
      viewport.style.transform = currentTransform;
      if (currentZoom) {
        viewport.style.zoom = currentZoom;
      }
    }

    // 恢复焦点
    if (activeElement && typeof activeElement.focus === 'function') {
      setTimeout(() => {
        try {
          activeElement.focus();
        } catch (e) {
          // 忽略焦点恢复失败
        }
      }, 50);
    }
  } catch (error) {
    console.error('视图状态保护执行失败:', error);
  }
};

// 节点下拉菜单选项
const getNodeDropdownOptions = () => [
  {
    label:'休疗',
    key:'rest',
    props:{

    }
  },
  {
    label: '复制',
    key: 'copy',
    props:{
    }
  },
  {
    label: '删除',
    key: 'delete',
    props: {
    }
  }
];

// 处理节点下拉菜单选择
const handleNodeDropdownSelect = async (key: string, nodeId: string) => {
  if (key === 'delete') {
    await handleDeleteNode(nodeId);
  } else if (key === 'rest') {
    // 处理添加休疗
    currentRestNodeId.value = nodeId;
    restModalRef.value?.show(nodeId, internalNodes.value.length+1
    );
  } else if (key === 'copy') {
    // 处理复制阶段
    await handleCopyStage(nodeId);
  }
};

// 处理复制阶段
const handleCopyStage = async (nodeId: string) => {
  try {
    const userStore = useUserStore();
    // 调用复制接口
    const { data: newStageId, error } = await fetchGetClinicStageCopy(nodeId,userStore.id,userStore.userName);

    if (error || !newStageId) {
      message.error('复制阶段失败');
      return;
    }

    // 找到原始节点
    const originalNode = internalNodes.value.find(node => node.id === nodeId);
    if (!originalNode || !isGroupNodeData(originalNode.data)) {
      message.error('找不到要复制的阶段');
      return;
    }

    // 创建复制的节点数据
    const originalData = originalNode.data as GroupNodeData;
    const copiedNodeData: GroupNodeData = {
      title: `${originalData.title}(副本)`,
      topLabel: `${originalData.topLabel || originalData.title}(副本)`,
      nodes: originalData.nodes ? [...originalData.nodes] : [] // 深拷贝子节点数组
    };

    // 创建新的流程图节点
    const newFlowNode: FlowNode = {
      id: newStageId.toString(),
      type: 'group',
      position: { x: 0, y: 0 }, // 初始位置，后续会自动调整
      data: copiedNodeData
    };

    // 添加到内部节点数组的最后
    internalNodes.value.push(newFlowNode);

    // 如果有多个节点，自动连接到最后一个节点
    if (internalNodes.value.length > 1) {
      const lastNodeIndex = internalNodes.value.length - 2; // 倒数第二个节点
      const lastNode = internalNodes.value[lastNodeIndex];

      if (lastNode) {
        const newEdge: FlowEdge = {
          id: `edge-${lastNode.id}-${newStageId}`,
          source: lastNode.id,
          target: newStageId.toString(),
          type: 'custom'
        };

        // 添加边到内部边数组
        internalEdges.value.push(newEdge);
      }
    }

    // 同步到store
    flowChartStore.addOutlineItem(newStageId.toString(), copiedNodeData);

    // 触发重新布局
    await handleAutoLayout();

    message.success('阶段复制成功');

  } catch (error) {
    console.error('复制阶段失败:', error);
    message.error('复制阶段失败');
  }
};

// 处理休疗确认后的逻辑
const handleRestConfirm = (restData: { days: number; stageId: string ,stageSoluId:string }) => {
  if (currentRestNodeId.value) {
    // 找到对应的节点
    const targetNode = internalNodes.value.find(node => node.id === currentRestNodeId.value);
    if (targetNode && isGroupNodeData(targetNode.data)) {
      // 添加一个休疗子节点到该阶段
      const restNode = {
        id: restData.stageSoluId,
        label: '休疗',
        restDayCount: restData.days, // 休疗天数
        soluType: 2, // 休疗类型
        type: 'rest',
        data: {
          label: '休疗',
          days: restData.days,
        }
      };

      // 更新节点数据
      if (!targetNode.data.nodes) {
        targetNode.data.nodes = [];
      }
      targetNode.data.nodes.push(restNode);

      // 同步到大纲数据
      flowChartStore.addOutlineItem(targetNode.id, targetNode.data);

      // 触发重新布局
      handleAutoLayout();
    }

    // 清空当前节点ID
    currentRestNodeId.value = '';
  }
};

// 删除节点的处理函数
const handleDeleteNode = async (nodeId: string) => {
  try {
    // 调用删除接口
    const { error } = await fetchGetClinicStageDelete(nodeId);

    if (!error) {
      // 删除成功，在本地删除节点数据
      await preserveViewState(async () => {
        // 从本地节点数组中删除
        const nodeIndex = internalNodes.value.findIndex(node => node.id === nodeId);
        if (nodeIndex > -1) {
          internalNodes.value.splice(nodeIndex, 1);
          setNodes(internalNodes.value);
        }

        // 删除相关的边
        const relatedEdges = internalEdges.value.filter(edge =>
          edge.source === nodeId || edge.target === nodeId
        );
        relatedEdges.forEach(edge => {
          const edgeIndex = internalEdges.value.findIndex(e => e.id === edge.id);
          if (edgeIndex > -1) {
            internalEdges.value.splice(edgeIndex, 1);
          }
        });
        setEdges(internalEdges.value);

        // 从store中删除节点
        flowChartStore.removeNode(nodeId);

        // 本地删除大纲数据项，不再重新请求接口
        flowChartStore.removeOutlineItem(nodeId);
      });

      message.success('删除成功');
    } else {
      message.error('删除失败');
    }
  } catch (error) {
    console.error('删除节点失败:', error);
    message.error('删除失败');
  }
};

// 获取连接线样式的函数
const getEdgeStyle = (sourceId: string, targetId: string) => {
  const isHighlighted = selectedNodeId.value &&
    (selectedNodeId.value === sourceId || selectedNodeId.value === targetId);

  return {
    strokeWidth: 2,
    stroke: isHighlighted ? '#0085FF' : '#72BBFF',
    strokeDasharray: 'none' // 确保是实线
  };
};

// 由于使用自定义边组件，不再需要markerEnd配置



// 取消编辑
const cancelEdit = () => {
  editingNodeId.value = null;
  editingValue.value = '';
  originalValue.value = '';
  hasFocused.value = false; // 重置聚焦标记
};

// 更新节点的topLabel
const updateNodeTopLabel = (nodeId: string, newValue: string) => {
  const updatedNodes = internalNodes.value.map(node => {
    if (node.id === nodeId) {
      return {
        ...node,
        data: {
          ...node.data,
          topLabel: newValue
        }
      };
    }
    return node;
  });

  internalNodes.value = updatedNodes;
  setNodes(updatedNodes);
};

// 处理topLabel双击编辑
const handleTopLabelDoubleClick = (nodeId: string, currentValue: string) => {
  editingNodeId.value = nodeId;
  editingValue.value = currentValue || '';
  originalValue.value = currentValue || '';
};

// 处理编辑完成
const handleEditComplete = async (nodeId: string) => {
  const trimmedValue = editingValue.value.trim();

  // 如果值没有改变，直接退出编辑模式
  if (trimmedValue === originalValue.value) {
    cancelEdit();
    return;
  }

  try {
    // 获取节点在数组中的索引
    const nodeIndex = internalNodes.value.findIndex(node => node.id === nodeId);
    const orderNumber = nodeIndex >= 0 ? nodeIndex + 1 : 1;

    // 调用API更新
    const { error } = await fetchPostClinicStageUpdate({
      id: nodeId,
      relation: trimmedValue,
      orderNumber
    });

    // 检查响应状态
    if (!error) {
      // 更新节点数据
      updateNodeTopLabel(nodeId, trimmedValue);

      // 同步更新store中的节点数据和大纲树数据
      flowChartStore.updateNodeData(nodeId, { topLabel: trimmedValue });

      cancelEdit();
    } else {
      // 状态不是OK，回退到原始值
      editingValue.value = originalValue.value;
      updateNodeTopLabel(nodeId, originalValue.value);
      cancelEdit();
      console.warn('更新失败，已回退到原始值');
    }
  } catch (error) {
    // 请求失败，回退到原始值
    editingValue.value = originalValue.value;
    updateNodeTopLabel(nodeId, originalValue.value);
    cancelEdit();
    console.error('更新失败:', error);
  }
};

// 处理输入框键盘事件
const handleInputKeydown = (event: KeyboardEvent, nodeId: string) => {
  if (event.key === 'Enter') {
    event.preventDefault();
    handleEditComplete(nodeId);
  } else if (event.key === 'Escape') {
    event.preventDefault();
    editingValue.value = originalValue.value;
    cancelEdit();
  }
};

// 根据节点索引获取上方盒子样式
const getTopBoxStyle = (nodeIndex: number) => {
  let backgroundColor = '#505050'; // 默认颜色

  if (nodeIndex === 1) {
    backgroundColor = '#FFA438';
  } else if (nodeIndex === 2) {
    backgroundColor = '#638CFD';
  } else if (nodeIndex >= 3) {
    backgroundColor = '#AB9EFF';
  }

  return {
    position: 'absolute',
    top: '-40px',
    left: '50%',
    transform: 'translateX(-50%)',
    backgroundColor,
    color: '#FFFFFF',
    fontSize: '12px',
    fontWeight: '500',
    padding: '6px 12px',
    borderRadius: '6px',
    whiteSpace: 'nowrap',
    zIndex: 1000,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
  };
};

// 创建可编辑的topLabel
const createEditableTopLabel = (nodeId: string, topLabel: string, nodeIndex: number) => {
  const isEditing = editingNodeId.value === nodeId;

  if (isEditing) {
    // 只在刚开始编辑时聚焦和全选，避免每次渲染都重新选择
    if (!hasFocused.value) {
      nextTick(() => {
        const inputEl = document.querySelector(`input[data-node-id="${nodeId}"]`) as HTMLInputElement;
        if (inputEl) {
          inputEl.focus();
          inputEl.select();
          hasFocused.value = true;
        }
      });
    }

    return h('input', {
      type: 'text',
      value: editingValue.value,
      'data-node-id': nodeId,
      style: {
        ...getTopBoxStyle(nodeIndex),
        position: 'absolute',
        top: '-40px',
        left: '50%',
        transform: 'translateX(-50%)',
        border: '2px solid #72BBFF',
        outline: 'none',
        textAlign: 'center',
        minWidth: '80px'
      },
      onInput: (e: Event) => {
        editingValue.value = (e.target as HTMLInputElement).value;
      },
      onKeydown: (e: KeyboardEvent) => handleInputKeydown(e, nodeId),
      onBlur: () => handleEditComplete(nodeId)
    });
  }

  return h(
    'div',
    {
      class: 'top-box',
      style: {
        ...getTopBoxStyle(nodeIndex),
        cursor: 'pointer'
      },
      onDblclick: (e: Event) => {
        e.stopPropagation();
        handleTopLabelDoubleClick(nodeId, topLabel);
      }
    },
    topLabel || '-'
  );
};

// 移除未使用的连接映射

// 检查特定连接点是否有连接
const isHandleConnected = (nodeId: string, handleType: 'source' | 'target'): boolean => {
  return props.edges.some(edge => {
    if (handleType === 'source') {
      return edge.source === nodeId;
    }
    return edge.target === nodeId;
  });
};

// 优化的Handle渲染函数 - 减少内联样式
const createHandle = (config: { type: 'source' | 'target'; position: Position; nodeId: string; size?: number }) => {
  const { type, position, nodeId, size = 8 } = config;
  const isConnected = isHandleConnected(nodeId, type);

  // 只设置必要的动态样式
  const style: any = {
    backgroundColor: isConnected ? '#72BBFF' : '#B8B8B8'
  };

  // 设置尺寸（如果不是默认值）
  if (size !== 8) {
    style.width = `${size}px`;
    style.height = `${size}px`;
  }

  // 设置位置偏移 - 连接点跨越边框，一半在内一半在外
  if (position === Position.Top) {
    style.top = '0'; // 向上偏移一半
  }
  if (position === Position.Bottom) {
    style.bottom = '0'; // 向下偏移一半
  }
  if (position === Position.Left) {
    style.left = '-6px'; // 向左偏移一半
    style.top = '50%';
    style.transform = 'translateY(-50%)';
  }
  if (position === Position.Right) {
    style.right = '-6px'; // 向右偏移一半
    style.top = '50%';
    style.transform = 'translateY(-50%)';
  }

  return h(Handle, { type, position, style });
};

// 创建节点下拉菜单组件
const createNodeDropdown = (nodeId: string) => {
  return h(
    NDropdown,
    {
      placement: 'bottom-start',
      trigger: 'click',
      size: 'small',
      options: getNodeDropdownOptions(),
      onSelect: (key: string) => handleNodeDropdownSelect(key, nodeId)
    },
    {
      default: () => h(
        'div',
        {
          class: 'absolute top-1 right-1 w-3.5 h-3 flex items-center justify-center cursor-pointer hover:bg-gray-100 rounded-0.5 transition-all duration-200 z-20 opacity-70 hover:opacity-100',
          style: {
            fontSize: '12px',
            color: '#666',
            fontWeight: 'bold',
            lineHeight: '1'
          },
          title: '更多操作'
        },
        '⋯'
      )
    }
  );
};

// 更新边样式的函数
const updateEdgeStyles = () => {
  const updatedEdges = internalEdges.value.map(edge => ({
    ...edge,
    type: 'custom',
    data: { originalType: 'smoothstep' }, // 传递原始类型给CustomEdge
    style: getEdgeStyle(edge.source, edge.target)
  }));

  internalEdges.value = updatedEdges;
  setEdges(updatedEdges);
};

// 节点点击事件
onNodeClick(({ node }: any) => {
  selectedNodeId.value = node.id;
  flowChartStore.setSelectedNodeId(node.id);

  // 重新处理边数据以更新样式
  updateEdgeStyles();
});

// 点击空白区域取消选中
onPaneClick(() => {
  selectedNodeId.value = null;
  flowChartStore.setSelectedNodeId(null);

  // 重新处理边数据以更新样式
  updateEdgeStyles();
});

// 连接事件处理
onConnect((connection: any) => {
  const newEdge = {
    id: `edge-${connection.source}-${connection.target}`,
    source: connection.source,
    target: connection.target,
    type: 'custom',
    animated: true,
    data: { originalType: 'smoothstep' },
    style: getEdgeStyle(connection.source, connection.target)
  };
  addEdges([newEdge]);

  // 更新内部边状态以触发连接点颜色更新
  internalEdges.value = [...internalEdges.value, newEdge];
});

// 处理缩放功能
const handleScaleChange = (scaleValue: string) => {
  switch (scaleValue) {
    case 'auto':
      fitView({
        padding: 0.3,
        maxZoom: 1.5,
        includeHiddenNodes: false
      });
      break;
    case '50%':
      zoomTo(0.5);
      break;
    case '100%':
      zoomTo(1.0);
      break;
    case '150%':
      zoomTo(1.5);
      break;
    default: {
      const percentage = Number.parseInt(scaleValue.replace('%', ''), 10);
      if (!Number.isNaN(percentage)) {
        zoomTo(percentage / 100);
      }
      break;
    }
  }
};

// 父节点样式配置
const parentNodeStyle = {
  position: 'relative',
  minWidth: '120px', // 改为最小宽度
  minHeight: '72px', // 改为最小高度，允许根据内容扩展
  borderRadius: '12px',
  opacity: '0.75',
  background: 'linear-gradient(0deg, #FFF -30.9%, #E7E7E7 100%)',
  boxShadow:
    '142px 158px 60px 0px rgba(186, 186, 186, 0.00), 91px 101px 54px 0px rgba(186, 186, 186, 0.03), 51px 57px 46px 0px rgba(186, 186, 186, 0.10), 23px 25px 34px 0px rgba(186, 186, 186, 0.17), 6px 6px 19px 0px rgba(186, 186, 186, 0.20)',
  flexShrink: '0',
  display: 'flex',
  flexDirection: 'column', // 垂直排列子节点
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: '12px',
  fontWeight: '500',
  color: '#333',
  border: '2px solid transparent', // 预留边框空间，避免选中时布局跳动
  padding: '21px 16px', // 设置垂直21px，水平16px的内边距
  whiteSpace: 'nowrap' // 防止文字换行
};

// 获取节点索引的辅助函数
const getNodeIndex = (nodeId: string): number => {
  // 从内部节点数组中找到节点的索引
  const nodeIndex = internalNodes.value.findIndex(node => node.id === nodeId);
  return nodeIndex >= 0 ? nodeIndex : 0;
};

// 根据节点索引获取父节点样式
const getParentNodeStyle = (nodeIndex: number) => {
  let background = 'linear-gradient(0deg, #FFF -30.9%, #E7E7E7 100%)'; // 默认背景

  if (nodeIndex === 1) {
    background = 'linear-gradient(0deg, #FFF -30.9%, #FFE3C1 100%)';
  } else if (nodeIndex === 2) {
    background = 'linear-gradient(0deg, #FFF -23.37%, #B2C6FF 100%)';
  } else if (nodeIndex >= 3) {
    background = 'linear-gradient(0deg, #FFF -18.27%, #DED9FD 100%)';
  }

  return {
    ...parentNodeStyle,
    background
  };
};

// 添加拖拽接收动画
const addDropAnimation = (nodeId: string) => {
  const nodeElement = document.querySelector(`[data-id="${nodeId}"]`);
  if (nodeElement) {
    nodeElement.classList.add('drop-animation');
    setTimeout(() => {
      nodeElement.classList.remove('drop-animation');
    }, 600);
  }
};

// 新增节点动画
const addNewNodeAnimation = (_nodeId: string) => {
  // 查找新添加的子节点元素
  const childNodes = document.querySelectorAll('.child-node');
  const lastChildNode = childNodes[childNodes.length - 1] as HTMLElement;

  if (lastChildNode) {
    lastChildNode.style.transform = 'scale(0) translateY(-20px)';
    lastChildNode.style.opacity = '0';
    lastChildNode.style.transition = 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)';

    setTimeout(() => {
      lastChildNode.style.transform = 'scale(1) translateY(0)';
      lastChildNode.style.opacity = '1';
    }, 50);

    // 清理动画样式
    setTimeout(() => {
      lastChildNode.style.transition = '';
      lastChildNode.style.transform = '';
      lastChildNode.style.opacity = '';
    }, 500);
  }
};

// 添加方案节点到阶段
const addProgramNodeToStage = async (stageId: string, program: any, _orderNumber: number) => {
  const targetNodeIndex = internalNodes.value.findIndex(node => node.id === stageId);
  if (targetNodeIndex === -1) return;

  const targetNode = internalNodes.value[targetNodeIndex];

  // 确保是分组节点
  if (!targetNode.data || !('nodes' in targetNode.data)) return;

  // 生成新的方案子节点ID
  const newSolutionId = `${stageId}-solution-${Date.now()}`;

  // 创建新的方案子节点
  const newSolutionNode = {
    id: newSolutionId,
    data: {
      label: program.name || '未命名方案',
      days: program.dayCount ? `${program.dayCount}天` : '',
      soluName: program.name || '未命名方案',
      comment: program.comment || '',
      dayCount: program.dayCount || 0,
      weekCount: program.weekCount || 0,
      solutionId: program.id,
      stageSoluId: newSolutionId,
      soluType: 1,
      soluSummary: program.comment || program.name || ''
    }
  };

  // 更新节点数据，添加新的子节点
  const updatedNodeData = {
    ...targetNode.data,
    nodes: [...(targetNode.data.nodes || []), newSolutionNode]
  };

  const updatedNodes = [...internalNodes.value];
  updatedNodes[targetNodeIndex] = {
    ...targetNode,
    data: updatedNodeData
  };

  // 应用动画效果，但避免触发大规模重新渲染
  internalNodes.value = updatedNodes;
  setNodes(updatedNodes);

  // 添加新增子节点的动画
  setTimeout(() => {
    addNewNodeAnimation(newSolutionId);
  }, 100);

  // 延迟更新store，避免立即触发响应式更新
  setTimeout(() => {
    flowChartStore.updateNodeData(stageId, updatedNodeData);
  }, 50);
};

// 处理拖拽相关功能
const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  event.dataTransfer!.dropEffect = 'copy';
};

const handleDrop = async (event: DragEvent, nodeId: string) => {
  event.preventDefault();

  try {
    const data = event.dataTransfer?.getData('application/json');
    if (!data) return;

    const dragData = JSON.parse(data);
    if (dragData.type === 'program' && dragData.program?.id) {
      const program = dragData.program;
      const programId = program.id;

      // 获取目标节点，计算orderNumber
      const targetNode = internalNodes.value.find(node => node.id === nodeId);
      if (!targetNode) {
        console.error('目标节点不存在');
        return;
      }

      // 添加拖拽接收动画
      addDropAnimation(nodeId);

      let orderNumber = 1;
      if (targetNode.data && 'nodes' in targetNode.data && Array.isArray(targetNode.data.nodes)) {
        orderNumber = targetNode.data.nodes.length + 1;
      }

      // 调用API添加方案到阶段
      const { error } = await fetchPostClinicStageAddProgramOrRest({
        stageId: nodeId,
        type: 1, // 1表示方案类型
        relation: programId,
        orderNumber
      });

      if (!error) {
        // 使用视图状态保护机制执行更新操作
        await preserveViewState(async () => {
          // API成功后，立即更新本地节点数据，添加新的方案子节点
          await addProgramNodeToStage(nodeId, program, orderNumber);

          // 本地添加大纲数据项，不再重新请求接口
          const targetNode = internalNodes.value.find(node => node.id === nodeId);
          if (targetNode) {
            flowChartStore.addOutlineItem(nodeId, targetNode.data);
          }
        });

        window.$message?.success('方案添加成功');
      } else {
        window.$message?.error('方案添加失败');
      }
    }
  } catch (error) {
    console.error('处理拖拽失败:', error);
    window.$message?.error('添加方案失败');
  }
};

// 自定义父节点渲染
const renderTreatmentNode = (nodeProps: any) => {
  const { data, selected, id } = nodeProps;

  // 从节点ID中提取索引信息，或者使用位置来确定索引
  const nodeIndex = getNodeIndex(id);

  return h(
    'div',
    {
      class: `parent-node-wrapper`,
      style: {
        position: 'relative'
      }
    },
    [
      // 上方盒子
      createEditableTopLabel(id, data.topLabel || '-', nodeIndex),
      // 主节点
      h(
        'div',
        {
          class: `parent-node ${selected ? 'selected' : ''}`,
          style: {
            ...getParentNodeStyle(nodeIndex),
            pointerEvents: 'auto' // 确保可以接收点击事件
          },
          onDragover: handleDragOver,
          onDrop: (e: DragEvent) => handleDrop(e, id)
        },
        [
          createHandle({ type: 'target', position: Position.Left, nodeId: id }),
          createHandle({ type: 'source', position: Position.Right, nodeId: id }),
          createNodeDropdown(id)
        ]
      )
    ]
  );
};

// 自定义分组节点渲染（父节点包含子节点）
const renderGroupNode = (nodeProps: any) => {
  const { data, selected, id } = nodeProps;

  // 确保data是GroupNodeData类型
  if (!isGroupNodeData(data)) {
    return h('div', { class: 'error-node' }, '数据错误');
  }

  const groupData = data as GroupNodeData;

  // 确保nodes是数组（可以为空数组）
  if (!Array.isArray(groupData.nodes)) {
    return h('div', { class: 'error-node' }, '数据错误');
  }

  // 获取节点索引用于样式
  const nodeIndex = getNodeIndex(id);

  // 子节点样式配置
  const childNodeBaseStyle = {
    position: 'relative',
    display: 'flex',
    minHeight: '50px',
    height: 'auto',
    padding: '8px 16px',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '2px',
    flexShrink: '0',
    borderRadius: '6px',
    border: '0.5px solid #B2C6FF',
    background: '#FFF',
    fontSize: '11px',
    fontWeight: '400',
    color: '#333',
    cursor: 'pointer',
    textAlign: 'center',
    minWidth: '100px', // 增加最小宽度
    width: 'fit-content' // 根据内容自适应宽度
  };

  // 创建子节点
  const childNodes = groupData.nodes.map((childNode: any, index: number) => {
    return h(
      'div',
      {
        key: childNode.id,
        class: 'child-node',
        style: {
          ...childNodeBaseStyle,
          // 最后一个子节点不需要margin-bottom
          marginBottom: index === groupData.nodes.length - 1 ? '0' : '20px'
        }
      },
      [
        h('div', { class: 'child-node-content' }, [
          h('div', { class: 'child-node-title' }, childNode.data?.label || childNode.label || '子节点'),
          // 根据 soluType 判断显示内容
          (() => {
              // 方案节点，显示原有的days字段
              const days = childNode.data?.days || childNode.days;
              return days ? h('div', { class: 'child-node-days' }, days) : null;

          })()
        ].filter(Boolean))
      ]
    );
  });

  // 移除硬编码尺寸计算，让CSS自动撑开

  return h(
    'div',
    {
      class: `group-node-wrapper`,
      style: {
        position: 'relative'
      }
    },
    [
      // 上方盒子
      createEditableTopLabel(id, data.topLabel || '-', nodeIndex),
      // 主分组节点
      h(
        'div',
        {
          class: `group-node-container ${selected ? 'selected' : ''}`,
          style: {
            ...getParentNodeStyle(nodeIndex),
            pointerEvents: 'auto' // 确保可以接收点击事件
          },
          onDragover: handleDragOver,
          onDrop: (e: DragEvent) => handleDrop(e, id)
        },
        [
          createHandle({ type: 'target', position: Position.Left, nodeId: id }),
          createHandle({ type: 'source', position: Position.Right, nodeId: id }),
          createNodeDropdown(id),
          ...childNodes
        ]
      )
    ]
  );
};

// 简化的数据处理函数
const processFlowData = () => {
  if (props.nodes.length === 0) {
    return [];
  }

  return props.nodes.map((node, index) => {
    // 标准化节点类型
    const nodeType = node.type === 'groupNode' ? 'group' : node.type;

    // 使用简单的水平布局，让vue-flow处理其余的布局逻辑
    const position = node.position.x !== 0 || node.position.y !== 0
      ? node.position
      : { x: 0, y: 0 }; // 简单的水平排列

    return {
      id: node.id,
      type: nodeType,
      position,
      data: JSON.parse(JSON.stringify(node.data))
    };
  });
};

// 处理边数据 - 根据节点顺序自动生成连接线
const processEdgeData = () => {
  const edges: any[] = [];

  // 如果有传入的边数据，先处理这些
  if (props.edges.length > 0) {
    edges.push(...props.edges.map(edge => ({
      id: edge.id,
      source: edge.source,
      target: edge.target,
      type: 'custom',
      animated: edge.animated || true,
      data: { originalType: 'smoothstep' },
      style: getEdgeStyle(edge.source, edge.target)
    })));
  }

  // 如果没有边数据或边数据不足，根据节点顺序自动生成连接线
  if (props.nodes.length > 1) {
    const nodeIds = props.nodes.map(node => node.id);

    // 为相邻节点创建连接线
    for (let i = 0; i < nodeIds.length - 1; i++) {
      const sourceId = nodeIds[i];
      const targetId = nodeIds[i + 1];
      const edgeId = `auto-edge-${sourceId}-${targetId}`;

      // 检查是否已经存在这条边
      const existingEdge = edges.find(edge =>
        (edge.source === sourceId && edge.target === targetId) ||
        edge.id === edgeId
      );

      if (!existingEdge) {
        edges.push({
          id: edgeId,
          source: sourceId,
          target: targetId,
          type: 'custom',
          animated: true,
          data: { originalType: 'smoothstep' },
          style: getEdgeStyle(sourceId, targetId)
        });
      }
    }
  }

  return edges;
};

// 简化的更新流程图元素
const updateFlowElements = () => {
  if (props.nodes.length === 0) {
    internalNodes.value = [];
    internalEdges.value = [];
    setNodes([]);
    setEdges([]);
    return;
  }

  const nodesData = processFlowData();
  const edgesData = processEdgeData();

  internalNodes.value = nodesData;
  internalEdges.value = edgesData;

  setNodes(nodesData);
  setEdges(edgesData);

  isInitialized.value = true;
};

// 监听nodes数据变化，使用简单的watch
watch(
  () => props.nodes,
  () => {
    updateFlowElements();
  },
  { immediate: true, deep: true }
);

// 监听缩放值变化
watch(
  () => props.scaleValue,
  scaleValue => {
    if (scaleValue && isInitialized.value) {
      handleScaleChange(scaleValue);
    }
  },
  { immediate: false }
);

// 监听自动排版变化
watch(
  () => props.shouldAutoLayout,
  shouldAutoLayout => {
    if (shouldAutoLayout && isInitialized.value) {
      handleAutoLayout();
    }
  },
  { immediate: false }
);

// 初始化布局工具
const { autoLayout } = useLayout()

// 自动布局函数
async function handleAutoLayout() {
  if (!internalNodes.value.length) return

  try {
    // 获取容器尺寸
    const containerElement = document.querySelector('.vue-flow-wrapper')
    const containerWidth = containerElement ? containerElement.clientWidth : 1200
    const containerHeight = containerElement ? containerElement.clientHeight : 600

    // 应用布局
    const { nodes: layoutedNodes, edges: layoutedEdges } = await autoLayout(
      internalNodes.value,
      internalEdges.value,
      {
        nodeSpacing: 80,
        containerWidth,
        containerHeight
      }
    )

    // 更新节点位置
    internalNodes.value = layoutedNodes
    internalEdges.value = layoutedEdges

    // 调整视图
    await nextTick()
    fitView()
  } catch (error) {
    console.error('Auto layout failed:', error)
  }
}



// 简化的加载状态
const showLoading = computed(() => props.loading);

// 评论处理函数
const handleCommentSave = (content: string) => {
  emit('comment-save', content);
};

const handleCommentClose = () => {
  emit('comment-close');
};

const handleCommentPositionChange = (position: { x: number; y: number }) => {
  emit('comment-position-change', position);
};

// 处理已保存评论的位置更新
const handleUpdateCommentPosition = (commentId: string, position: { x: number; y: number }) => {
  emit('update-comment-position', commentId, position);
};

// 处理删除评论
const handleDeleteComment = (commentId: string) => {
  emit('delete-comment', commentId);
};
</script>

<template>
  <div class="vue-flow-wrapper h-full w-full" v-if="internalNodes && internalEdges">
    <VueFlow
      :nodes="internalNodes"
      :edges="internalEdges"
      :min-zoom="0.1"
      :max-zoom="2"
      class="h-full w-full"
      :node-types="{
        treatment: renderTreatmentNode,
        group: renderGroupNode
      }"
      :edge-types="{
        custom: CustomEdge
      }"
      :nodes-connectable="true"
      :edges-updatable="true"
      :node-draggable="true"
      :nodes-selectable="true"
      :connect-on-click="false"
      :default-edge-options="{
        type: 'custom',
        animated: true
      }"
      @nodes-initialized="handleAutoLayout"
      elevate-edges-on-select
    >
      <Background />
      <Controls />

      <!-- 目录抽屉 -->
      <DirectoryDrawer />

      <!-- 设置抽屉 -->
      <SettingsDrawer />

      <!-- 评论框 -->
      <CommentBox
        :visible="props.commentBox.visible"
        :x="props.commentBox.x"
        :y="props.commentBox.y"
        @save="handleCommentSave"
        @update:visible="handleCommentClose"
        @position-change="handleCommentPositionChange"
      />

      <!-- 已保存的评论 -->
      <CommentOverlay
        :comments="props.comments"
        @update-comment-position="handleUpdateCommentPosition"
        @delete-comment="handleDeleteComment"
      />

      <!-- 加载遮罩 -->
      <div v-if="showLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
      </div>
    </VueFlow>
  </div>

  <!-- 休疗弹窗 -->
  <PlanModalAddRest ref="restModalRef" @confirm="handleRestConfirm" />
</template>

<style scoped>
/* 优化的节点样式 - 减少复杂的transition */
.parent-node,
.group-node-container {
  transition:
    opacity 0.15s ease,
    transform 0.15s ease;
  will-change: opacity, transform; /* 优化渲染性能 */
}

.parent-node:hover,
.group-node-container:hover {
  opacity: 1;
  transform: translateY(-1px); /* 减少移动距离 */
}

.parent-node.selected,
.group-node-container.selected {
  border-color: #5E88FF;
}

/* 子节点样式 */
.child-node {
  pointer-events: none; /* 子节点不阻止父节点点击事件 */
  transition: transform 0.1s ease; /* 只对必要属性使用transition */
}

.child-node:hover {
  transform: translateY(-1px);
}

/* 子节点内容样式 */
.child-node-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
  width: 100%;
  height: 100%;
  text-align: center;
}

.child-node-title {
  font-weight: 500;
  color: #333;
  text-align: center;
  line-height: 1.3;
  word-break: keep-all;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.child-node-days {
  font-size: 10px;
  color: #666;
  font-weight: 400;
  text-align: center;
}

/* 优化的Handle样式 */
:deep(.vue-flow__handle) {
  border: 1px solid #ffffff;
  border-radius: 50%;
  z-index: 1000;
  opacity: 1;
  transition: background-color 0.15s ease;
  will-change: background-color;
}

/* 父节点Handle */
:deep(.parent-node .vue-flow__handle),
:deep(.group-node-container .vue-flow__handle) {
  width: 8px;
  height: 8px;
}



/* 连接点悬停效果 - 避免移位 */
:deep(.vue-flow__handle:hover) {
  background-color: #72bbff;
  box-shadow: 0 0 0 2px rgba(114, 187, 255, 0.3);
}

/* 选中节点时连接点样式 */
:deep(.vue-flow__node.selected .vue-flow__handle) {
  background-color: #0085FF !important;
  box-shadow: 0 0 0 2px rgba(0, 133, 255, 0.3) !important;
}

/* 确保连接点在节点边框之上 */
:deep(.vue-flow__node) {
  z-index: 1 !important;
}

:deep(.vue-flow__node .vue-flow__handle) {
  z-index: 1001 !important;
}

/* 自定义选中样式，但不阻止选中行为 */
:deep(.vue-flow__node.selected) {
  outline: none !important;
}

:deep(.vue-flow__node.selected .parent-node),
:deep(.vue-flow__node.selected .group-node-container) {
  border-color: #5E88FF !important;
}

/* 优化的边线样式 */
:deep(.vue-flow__edge-path) {
  stroke-width: 2px;
  stroke-dasharray: none; /* 确保是实线 */
  transition: stroke-width 0.15s ease, stroke 0.15s ease;
}

:deep(.vue-flow__edge:hover .vue-flow__edge-path) {
  stroke-width: 3px;
}

/* 箭头标记样式 */
:deep(.vue-flow__edge .vue-flow__edge-marker) {
  transition: fill 0.15s ease;
}

/* 悬停效果 */
:deep(.vue-flow__edge:hover .vue-flow__edge-marker) {
  opacity: 0.8;
}

/* 上方盒子样式 */
.top-box {
  user-select: none;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

/* 节点包装器样式 */
.parent-node-wrapper,
.group-node-wrapper {
  position: relative;
}

/* 确保上方盒子在合适的层级 */
.parent-node-wrapper .top-box,
.group-node-wrapper .top-box {
  z-index: 1002;
}

/* 拖拽交互动画 */
:deep(.vue-flow__node.drop-animation .parent-node),
:deep(.vue-flow__node.drop-animation .group-node-container) {
  animation: dropHighlight 0.6s ease-out;
  border-color: #72bbff !important;
}

@keyframes dropHighlight {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(114, 187, 255, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 8px rgba(114, 187, 255, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(114, 187, 255, 0);
  }
}

/* 拖拽悬停效果 */
:deep(.vue-flow__node.vue-flow__node--drop-target .parent-node),
:deep(.vue-flow__node.vue-flow__node--drop-target .group-node-container) {
  border-color: #72bbff !important;
  box-shadow: 0 0 0 2px rgba(114, 187, 255, 0.3);
  transform: scale(1.01);
  transition: all 0.2s ease;
}

/* 子节点入场动画优化 */
.child-node {
  will-change: transform, opacity;
}

/* 加载遮罩样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
