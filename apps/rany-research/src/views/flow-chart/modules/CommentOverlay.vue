<script setup lang="ts">
import { onUnmounted, ref } from 'vue';

interface Comment {
  id: string;
  content: string;
  x: number;
  y: number;
}

interface Props {
  comments: Comment[];
}

interface Emits {
  (e: 'update-comment-position', commentId: string, position: { x: number; y: number }): void;
  (e: 'delete-comment', commentId: string): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

// 拖拽状态
const draggingComment = ref<string | null>(null);
const dragOffset = ref({ x: 0, y: 0 });

// 拖拽处理函数
const handleMouseMove = (event: MouseEvent) => {
  if (!draggingComment.value) return;

  const flowContainer = document.querySelector('.vue-flow-wrapper');
  const flowRect = flowContainer?.getBoundingClientRect();

  if (flowRect) {
    const newX = event.clientX - flowRect.left - dragOffset.value.x;
    const newY = event.clientY - flowRect.top - dragOffset.value.y;

    // 限制在容器边界内 - 更新为新的评论框尺寸
    const commentWidth = 200;
    const commentHeight = 80;
    const maxX = flowRect.width - commentWidth;
    const maxY = flowRect.height - commentHeight;

    const constrainedPosition = {
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    };

    emit('update-comment-position', draggingComment.value, constrainedPosition);
  }
};

const handleMouseUp = () => {
  draggingComment.value = null;
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  document.body.style.cursor = '';
  document.body.style.userSelect = '';
};

const handleMouseDown = (event: MouseEvent, comment: Comment) => {
  // 如果点击的是删除按钮，不执行拖拽
  const target = event.target as HTMLElement;
  if (target.tagName === 'BUTTON' || target.textContent === '×') {
    return;
  }

  // 阻止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();

  draggingComment.value = comment.id;

  // 获取当前评论元素和流程图容器
  const commentElement = event.currentTarget as HTMLElement;
  const flowContainer = commentElement.closest('.vue-flow-wrapper');
  const flowRect = flowContainer?.getBoundingClientRect();
  const commentRect = commentElement.getBoundingClientRect();

  if (flowRect && commentRect) {
    // 计算鼠标在评论元素内的相对位置
    dragOffset.value = {
      x: event.clientX - commentRect.left,
      y: event.clientY - commentRect.top
    };
  }

  // 设置拖拽样式
  document.body.style.cursor = 'grabbing';
  document.body.style.userSelect = 'none';

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

const handleDeleteComment = (event: MouseEvent, commentId: string) => {
  event.preventDefault();
  event.stopPropagation();
  emit('delete-comment', commentId);
};

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  document.body.style.cursor = '';
  document.body.style.userSelect = '';
});
</script>

<template>
  <!-- 显示已保存的备注 -->
  <div
    v-for="comment in comments"
    :key="comment.id"
    class="comment-item absolute z-40 cursor-grab select-none"
    :class="{ 'cursor-grabbing': draggingComment === comment.id }"
    :style="{
      left: `${comment.x}px`,
      top: `${comment.y}px`,
      width: '200px',
      minHeight: '80px',
      background: '#FFFBEF',
      border: '1px solid #FABF01',
      borderRadius: '8px',
      boxShadow: '2px 2px 12px 0px rgba(0, 0, 0, 0.15)'
    }"
    @mousedown="handleMouseDown($event, comment)"
  >
    <!-- 删除按钮 -->
    <button
      class="delete-btn pointer-events-auto absolute right-2 top-2 h-5 w-5 flex items-center justify-center text-gray-400 transition-colors hover:text-red-500"
      title="删除评论"
      @mousedown="handleDeleteComment($event, comment.id)"
    >
      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M9 3L3 9M3 3L9 9"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>

    <!-- 评论内容 -->
    <div class="pointer-events-none p-3 pt-4">
      <div class="break-words text-sm text-gray-700 leading-relaxed">
        {{ comment.content }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.comment-item:hover {
  box-shadow: 2px 2px 16px 0px rgba(0, 0, 0, 0.2);
}

.comment-item:active {
  cursor: grabbing !important;
}

.cursor-grabbing {
  cursor: grabbing !important;
}

.delete-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: 50%;
}
</style>
