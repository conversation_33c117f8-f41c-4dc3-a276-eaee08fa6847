<script setup lang="ts">
import { ref } from 'vue';
import { NButton, NDrawer } from 'naive-ui';
import DirectoryContent from './DirectoryContent.vue';

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const emit = defineEmits<Emits>();

const showButton = ref(true);
const drawerVisible = ref(false);

// 处理目录按钮点击
const handleDirectoryClick = () => {
  showButton.value = false;
  drawerVisible.value = true;
  emit('update:visible', true);
};

// 处理抽屉关闭
const handleDrawerClose = () => {
  drawerVisible.value = false;
  showButton.value = true;
  emit('update:visible', false);
};
</script>

<template>
  <div>
    <!-- 目录按钮 -->
    <NButton
      v-if="showButton"
      size="small"
      class="directory-button absolute left-4 top-4 z-40 shadow-lg"
      @click="handleDirectoryClick"
    >
      <template #icon>
        <icon-local-menu />
      </template>
      目录
    </NButton>

    <!-- 左侧抽屉 - 无遮罩 -->
    <NDrawer
      v-model:show="drawerVisible"
      :width="240"
      placement="left"
      :mask-closable="false"
      :show-mask="false"
      :close-on-esc="false"
      to=".vue-flow-wrapper"
      class="directory-drawer"
      @update:show="handleDrawerClose"
    >
      <DirectoryContent @close="handleDrawerClose" />
    </NDrawer>
  </div>
</template>

<style lang="scss">
.absolute {
  position: absolute;
}

/* 确保抽屉在VueFlow内部正确显示 */
.directory-drawer :deep(.n-drawer) {
  position: absolute !important;
}

.directory-button.n-button {
  background-color: white !important;
}

.directory-drawer :deep(.n-drawer-container) {
  position: absolute !important;
}
.directory-drawer.n-drawer.n-drawer--left-placement {
  left: 12px !important;
  top: 60px !important; /* 确保不遮挡头部，头部高度约为70-80px */
  background-color: transparent !important;
  margin-bottom: 12px !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  height: calc(100% - 70px) !important; /* 减去头部高度和边距 */
}
.directory-drawer .n-drawer-content-wrapper {
  background-color: white !important;
}
.directory-drawer .n-drawer-body-content-wrapper {
  padding: 0 16px !important;
}
</style>
