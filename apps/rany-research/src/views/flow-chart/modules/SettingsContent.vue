<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { NButton, NDrawerContent, NForm, NFormItem, NInput, NTabPane, NTabs } from 'naive-ui';
import { useRoute } from 'vue-router';
import { usePlanStore } from '@/store/modules/plan';
import DiseaseSelect from '@/components/common/disease-select.vue';
import RiskCascader from '@/components/common/risk-cascader.vue';
import GeneralSettings from './GeneralSettings.vue';

interface Emits {
  (e: 'close'): void;
}

const emit = defineEmits<Emits>();
const route = useRoute();
const planStore = usePlanStore();

const { planBaseInfo } = storeToRefs(planStore);

const activeTab = ref('basic');

// Tab选项
const tabs = [
  { key: 'basic', label: '基础信息' },
  { key: 'general', label: '整体设置' }
];

// 获取计划ID
const planId = computed(() => {
  // 优先从路由参数获取（新路由结构）
  if (route.params.id) {
    return route.params.id as string;
  }
  // 兼容旧的查询参数方式
  return route.query.planId as string;
});

const riskIds = ref<string[]>([]);

// 表单规则
const rules = {
  name: [{ required: true, message: '请输入方案名称', trigger: ['input', 'blur'] }],
  diseaseId: [{ required: true, message: '请选择疾病分型', trigger: ['blur', 'change'] }]
};

// 初始化表单数据
const initFormData = () => {
  // planBaseInfo现在直接从store获取，不需要手动初始化
  // 初始化危险度数组
  if (planBaseInfo.value.riskId) {
    riskIds.value = planBaseInfo.value.riskId.split(',').filter(id => id.trim());
  }
};

// 危险度变化处理
const handleRiskChange = (newVal: string | string[] | undefined) => {
  riskIds.value = newVal as string[];
  planBaseInfo.value.riskId = (newVal as string[])?.join(',') || '';
};

// 监听planId变化，重新初始化数据
watch(
  planId,
  () => {
    if (planId.value) {
      initFormData();
    }
  },
  { immediate: true }
);
</script>

<template>
  <NDrawerContent class="settings-content">
    <!-- 头部 -->
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <icon-line-md-cog />
          <span class="text-[14px] font-medium">设置</span>
        </div>
        <NButton size="small" text @click="emit('close')">✕</NButton>
      </div>
    </template>

    <!-- 内容区域 -->
    <div class="h-full flex flex-col">
      <!-- Tab切换 -->
      <NTabs v-model:value="activeTab" type="line" size="small" class="mb-0">
        <NTabPane v-for="tab in tabs" :key="tab.key" :name="tab.key" :tab="tab.label" />
      </NTabs>

      <!-- 内容区域 -->
      <div class="flex-1 overflow-auto">
        <!-- 基础信息表单 -->
        <div v-if="activeTab === 'basic'">
          <NForm :model="planBaseInfo" :rules="rules" label-placement="top">
            <NFormItem path="name" label="名称" required>
              <NInput v-model:value="planBaseInfo.name" placeholder="请输入方案名称" />
            </NFormItem>
            <NFormItem path="diseaseId" label="疾病分型" required>
              <DiseaseSelect v-model:value="planBaseInfo.diseaseId" />
            </NFormItem>
            <NFormItem path="riskId" label="危险度分型">
              <RiskCascader v-model:value="riskIds" :multiple="true" @update:value="handleRiskChange" />
            </NFormItem>
          </NForm>
        </div>

        <!-- 整体设置 -->
        <div v-else-if="activeTab === 'general'">
          <GeneralSettings />
        </div>
      </div>
    </div>
  </NDrawerContent>
</template>

<style lang="scss">
:deep(.n-tabs-nav) {
  padding: 0;
}

.settings-content {
  .n-drawer-header {
    padding: 12px 16px !important;
    border-bottom: none !important;
  }
  .n-tabs-tab-pad {
    --n-tab-gap: 12px;
    --n-tab-text-color-active: #0085ff;
  }
}

:deep(.n-tabs-tab) {
  padding: 8px 12px;
}
</style>
