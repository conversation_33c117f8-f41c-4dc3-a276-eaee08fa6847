<script setup lang="tsx">
import { computed, h } from 'vue';
import { NButton, NIcon, NTree } from 'naive-ui';
import { type TreeNode, useFlowChartStore } from '@/store/modules/flow-chart';

interface Props {
  tab: string;
  searchValue: string;
}

const props = defineProps<Props>();

const iconLocalExpend = () => {
  return h(NIcon, null, { default: () => <icon-local-expend /> });
};

// 使用 Pinia store
const flowChartStore = useFlowChartStore();

// 过滤数据（根据搜索值）
const filteredData = computed(() => {
  return flowChartStore.getFilteredTreeData(props.tab, props.searchValue);
});

// 包装 selectedTreeKeys 和 expandedTreeKeys 为计算属性
const selectedKeys = computed<string[]>(() => {
  return Array.isArray(flowChartStore.selectedTreeKeys) ? [...flowChartStore.selectedTreeKeys] : [];
});

const expandedKeys = computed<string[]>(() => {
  return Array.isArray(flowChartStore.expandedTreeKeys) ? [...flowChartStore.expandedTreeKeys] : [];
});

// 检查节点是否被选中的方法
const isNodeSelected = (key: string) => {
  const keys = selectedKeys.value;
  return Array.isArray(keys) && keys.includes(key);
};

// 处理节点选择
const handleSelect = (keys: string[]) => {
  flowChartStore.setSelectedTreeKeys(keys);
  // 如果选择了节点，高亮对应的流程图节点
  if (keys.length > 0) {
    flowChartStore.highlightNodeFromTree(keys[0]);
  }
};

// 处理节点展开
const handleExpand = (keys: string[]) => {
  flowChartStore.setExpandedTreeKeys(keys);
};

// 操作按钮处理
const handleCopy = (node: TreeNode) => {
  console.log('复制节点:', node);
};

const handleDelete = (node: TreeNode) => {
  console.log('删除节点:', node);
};

const handleAdd = (node: TreeNode) => {
  console.log('添加子节点:', node);
};
</script>

<template>
  <div class="directory-tree">
    <NTree
      :data="filteredData"
      :selected-keys="selectedKeys"
      :expanded-keys="expandedKeys"
      :render-switcher-icon="iconLocalExpend"
      selectable
      expand-on-click
      block-line
      show-line
      :indent="12"
      @update:selected-keys="handleSelect"
      @update:expanded-keys="handleExpand"
    >
      <template #default="{ option }">
        <div class="w-full flex items-center justify-between">
          <div class="min-w-0 w-3/4 flex items-center gap-1">
            <span class="flex-shrink-0 text-xs text-gray-500">
              {{ option.type === 'folder' ? '📁' : '📄' }}
            </span>
            <span class="truncate text-sm" :title="option.label">{{ option.label }}</span>
            <span v-if="option.days" class="ml-1 flex-shrink-0 text-xs text-gray-400">({{ option.days }})</span>
          </div>
          <div v-if="isNodeSelected(option.key as string)" class="flex flex-shrink-0 gap-1">
            <NButton size="tiny" type="primary" text title="复制" @click.stop="handleCopy(option as TreeNode)">
              📋
            </NButton>
            <NButton size="tiny" type="error" text title="删除" @click.stop="handleDelete(option as TreeNode)">
              🗑️
            </NButton>
            <NButton size="tiny" type="success" text title="添加" @click.stop="handleAdd(option as TreeNode)">
              ➕
            </NButton>
          </div>
        </div>
      </template>
    </NTree>
  </div>
</template>

<style scoped>
:deep(.n-tree) {
  --n-font-size: 12px !important;
  --n-node-text-color: #3d3d3d !important;
  --n-line-offset-top: -12px !important;
}

:deep(.n-tree-node-content) {
  padding: 4px 8px;
}

:deep(.n-tree-node-content:hover) {
  background-color: none !important;
}

:deep(.n-tree-node--selected .n-tree-node-content) {
  background-color: none !important;
}

:deep(.n-tree-node-content__text) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 第一层节点的字体样式 */
:deep(.n-tree-node-wrapper[aria-level='1'] .n-tree-node-content) {
  font-weight: 500;
}

/* 第二层节点的字体样式 */
:deep(.n-tree-node-wrapper[aria-level='2'] .n-tree-node-content) {
  font-weight: 300;
}

/* 隐藏第一级节点的连接线 */
:deep(.n-tree-node-wrapper[aria-level='1']) .n-tree-node-content::before {
  display: none !important;
}

/* 隐藏第一层节点的缩进连接线 */
:deep(.n-tree-node-wrapper[aria-level='1']) .n-tree-node-indent {
  display: none;
}

/* 保留第二层及以下节点的连接线 */
:deep(.n-tree-node-wrapper:not([aria-level='1'])) .n-tree-node-content::before {
  display: block !important;
}

:deep(.n-tree-node.n-tree-node--selectable.n-tree-node--clickable, .n-tree-node-switcher) {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 12px;
}

:deep(.n-tree .n-tree-node-content .n-tree-node-content__text) {
  line-height: 19px;
}

:deep(.n-tree .n-tree-node-switcher .n-tree-node-switcher__icon) {
  width: 16px;
  height: 16px;
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
:deep(.n-tree.n-tree--block-line .n-tree-node:not(.n-tree-node--disabled).n-tree-node--selected) {
  background-color: #e6e6e6 !important;
}
:deep(.n-tree .n-tree-node-indent.n-tree-node-indent--show-line::before) {
}
</style>
