<script setup lang="ts">
import { computed } from 'vue';
import { BaseEdge, getSmoothStepPath, type EdgeProps } from '@vue-flow/core';

const props = defineProps<EdgeProps>();

// 计算调整后的目标点，让连接线在箭头开始处结束
const adjustedTarget = computed(() => {
  const dx = props.targetX - props.sourceX;
  const dy = props.targetY - props.sourceY;
  const angle = Math.atan2(dy, dx);

  // 箭头长度，连接线应该在这里结束
  const arrowLength = 0;

  return {
    x: props.targetX - arrowLength * Math.cos(angle),
    y: props.targetY - arrowLength * Math.sin(angle)
  };
});

// 计算路径 - 统一使用smoothstep
const path = computed(() => {
  // 始终使用smoothstep类型
  return getSmoothStepPath({
    sourceX: props.sourceX,
    sourceY: props.sourceY,
    sourcePosition: props.sourcePosition as any,
    targetX: adjustedTarget.value.x,
    targetY: adjustedTarget.value.y,
    targetPosition: props.targetPosition as any,
  });
});

// 计算箭头位置和角度
const arrowProps = computed(() => {
  // 计算箭头方向
  const angle = 0;

  // 箭头长度和角度
  const arrowLength = 10;
  const arrowAngle = Math.PI / 5; // 30度

  // 箭头尖端位置 - 就在连接线的末端
  const arrowTip = {
    x: props.targetX,
    y: props.targetY
  };

  // 计算箭头的两条线
  const arrowBase1 = {
    x: arrowTip.x - arrowLength * Math.cos(angle - arrowAngle),
    y: arrowTip.y - arrowLength * Math.sin(angle - arrowAngle)
  };
  const arrowBase2 = {
    x: arrowTip.x - arrowLength * Math.cos(angle + arrowAngle),
    y: arrowTip.y - arrowLength * Math.sin(angle + arrowAngle)
  };

  return {
    tip: arrowTip,
    base1: arrowBase1,
    base2: arrowBase2,
    angle: angle * (180 / Math.PI)
  };
});

// 获取连接线颜色
const strokeColor = computed(() => {
  return props.style?.stroke || '#72BBFF';
});

// 获取连接线宽度
const strokeWidth = computed(() => {
  return props.style?.strokeWidth || 2;
});
</script>

<template>
  <BaseEdge
    :id="id"
    :style="style"
    :path="path[0]"
  />

  <!-- 自定义线性箭头 -->
  <g>
    <!-- 箭头主体线条 -->
    <line
      :x1="arrowProps.base1.x"
      :y1="arrowProps.base1.y"
      :x2="arrowProps.tip.x"
      :y2="arrowProps.tip.y"
      :stroke="strokeColor"
      :stroke-width="strokeWidth"
      stroke-linecap="round"
    />
    <line
      :x1="arrowProps.base2.x"
      :y1="arrowProps.base2.y"
      :x2="arrowProps.tip.x"
      :y2="arrowProps.tip.y"
      :stroke="strokeColor"
      :stroke-width="strokeWidth"
      stroke-linecap="round"
    />
  </g>
</template>

<style scoped>
/* 确保箭头在连接线之上 */
g {
  pointer-events: none;
}
</style>
