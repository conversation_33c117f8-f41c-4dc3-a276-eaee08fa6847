<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { NCard, NEmpty, NSpin, useMessage } from 'naive-ui';
import { useProgramStore } from '@/store/modules/program';
import { useFlowChartStore } from '@/store/modules/flow-chart';
import { fetchPostClinicStageAddProgramOrRest } from '@/service/api/clinicPlan';

const message = useMessage();
const programStore = useProgramStore();
const flowChartStore = useFlowChartStore();

const programs = ref<Api.Program.ProgramItem[]>([]);
const loading = ref(false);
const draggedProgram = ref<Api.Program.ProgramItem | null>(null);

// 加载方案列表
const loadPrograms = async () => {
  loading.value = true;
  try {
    const data = await programStore.getPrograms({
      pageNo: 1,
      pageSize: 100,
      status: 1 // 只获取已发布的方案
    });

    if (data && Array.isArray(data.records)) {
      programs.value = data.records;
    } else if (Array.isArray(data)) {
      programs.value = data;
    } else {
      programs.value = [];
    }
  } catch (error) {
    message.error('加载方案列表失败');
    programs.value = [];
  } finally {
    loading.value = false;
  }
};

// 创建自定义拖拽图像
const createCustomDragImage = async (event: DragEvent, programName: string) => {
  // 创建临时DOM元素来渲染拖拽预览
  const dragPreview = document.createElement('div');
  dragPreview.style.position = 'absolute';
  dragPreview.style.top = '-1000px';
  dragPreview.style.left = '-1000px';
  dragPreview.style.width = '200px';
  dragPreview.style.height = '50px';
  dragPreview.style.backgroundColor = '#ffffff';
  dragPreview.style.border = '2px solid #60a5fa';
  dragPreview.style.borderRadius = '8px';
  dragPreview.style.display = 'flex';
  dragPreview.style.alignItems = 'center';
  dragPreview.style.padding = '0 12px';
  dragPreview.style.gap = '8px';
  dragPreview.style.fontSize = '12px';
  dragPreview.style.color = '#374151';
  dragPreview.style.fontFamily = 'Arial, sans-serif';

  // 添加拖拽图标（使用Material Design图标的SVG路径）
  const iconSvg = document.createElement('div');
  iconSvg.innerHTML = `
    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" style="color: #60a5fa;">
      <path d="M9,3A1,1 0 0,1 10,4A1,1 0 0,1 9,5A1,1 0 0,1 8,4A1,1 0 0,1 9,3M15,3A1,1 0 0,1 16,4A1,1 0 0,1 15,5A1,1 0 0,1 14,4A1,1 0 0,1 15,3M9,8A1,1 0 0,1 10,9A1,1 0 0,1 9,10A1,1 0 0,1 8,9A1,1 0 0,1 9,8M15,8A1,1 0 0,1 16,9A1,1 0 0,1 15,10A1,1 0 0,1 14,9A1,1 0 0,1 15,8M9,13A1,1 0 0,1 10,14A1,1 0 0,1 9,15A1,1 0 0,1 8,14A1,1 0 0,1 9,13M15,13A1,1 0 0,1 16,14A1,1 0 0,1 15,15A1,1 0 0,1 14,14A1,1 0 0,1 15,13M9,18A1,1 0 0,1 10,19A1,1 0 0,1 9,20A1,1 0 0,1 8,19A1,1 0 0,1 9,18M15,18A1,1 0 0,1 16,19A1,1 0 0,1 15,20A1,1 0 0,1 14,19A1,1 0 0,1 15,18Z"/>
    </svg>
  `;

  // 添加方案名称
  const nameSpan = document.createElement('span');
  let displayName = programName;
  if (displayName.length > 20) {
    displayName = `${displayName.slice(0, 17)}...`;
  }
  nameSpan.textContent = displayName;

  // 组装元素
  dragPreview.appendChild(iconSvg);
  dragPreview.appendChild(nameSpan);

  // 将元素添加到页面（临时）
  document.body.appendChild(dragPreview);

  // 使用html2canvas或者直接设置为拖拽图像
  try {
    // 简单方法：直接使用元素作为拖拽图像
    event.dataTransfer?.setDragImage(dragPreview, 100, 25);

    // 延迟移除元素
    setTimeout(() => {
      document.body.removeChild(dragPreview);
    }, 100);
  } catch (error) {
    console.warn('设置拖拽图像失败:', error);
    // 如果失败，移除临时元素
    document.body.removeChild(dragPreview);
  }
};

// 处理拖拽开始
const onDragStart = (event: DragEvent, program: Api.Program.ProgramItem) => {
  if (!event.dataTransfer) return;

  draggedProgram.value = program;
  event.dataTransfer.setData(
    'application/json',
    JSON.stringify({
      type: 'program',
      program
    })
  );
  event.dataTransfer.effectAllowed = 'copy';

  // 创建自定义拖拽图像
  createCustomDragImage(event, program.name || '未命名方案').catch(console.error);

  // 添加拖拽时的视觉反馈
  const cardElement = (event.target as HTMLElement).closest('.program-card');
  if (cardElement) {
    cardElement.classList.add('dragging');
  }
};

// 处理拖拽结束
const onDragEnd = (event: DragEvent) => {
  const cardElement = (event.target as HTMLElement).closest('.program-card');
  if (cardElement) {
    cardElement.classList.remove('dragging');
  }
  draggedProgram.value = null;
};

// 添加程序到阶段
const addProgramToStage = async (stageId: string, programId: string) => {
  try {
    // 验证参数
    if (!programId) {
      message.error('方案ID无效');
      return false;
    }

    // 获取目标阶段节点，用于计算orderNumber
    const targetNode = flowChartStore.flowNodes.find(node => node.id === stageId);
    if (!targetNode) {
      message.error('目标阶段不存在');
      return false;
    }

    // 计算orderNumber：获取目标节点中已有的方案数量+1
    let orderNumber = 1;
    if (targetNode.data && 'nodes' in targetNode.data && Array.isArray(targetNode.data.nodes)) {
      orderNumber = targetNode.data.nodes.length + 1;
    }

    // 调用API添加方案到阶段
    const { error } = await fetchPostClinicStageAddProgramOrRest({
      stageId,
      type: 1, // 1表示方案类型
      relation: programId,
      orderNumber
    });

    if (!error) {
      message.success('方案添加成功');
      return true;
    }
    message.error('方案添加失败');
    return false;
  } catch (error) {
    console.error('添加方案失败:', error);
    message.error('添加方案失败，请重试');
    return false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadPrograms();
});

// 暴露方法给父组件
defineExpose({
  loadPrograms,
  addProgramToStage
});
</script>

<template>
  <div class="program-drag-list h-full">
    <NSpin :show="loading">
      <div v-if="programs.length === 0 && !loading" class="h-32">
        <NEmpty description="暂无可用方案" />
      </div>

      <div v-else class="space-y-1">
        <NCard
          v-for="program in programs"
          :key="program.id || program.name"
          size="small"
          hoverable
          class="program-card cursor-pointer transition-all duration-200 hover:scale-98 hover:shadow-md "
          :draggable="true"
          content-style="border:none; padding: 2px 8px; font-size: 12px;"
          @dragstart="onDragStart($event, program)"
          @dragend="onDragEnd"
        >
          <div class="flex items-center justify-between">
            <div class="min-w-0 flex flex-1 items-center gap-2">
              <div class="flex items-center justify-center">
                <icon-mdi-light:clipboard-text />
              </div>
              <span class="truncate" :title="program.name || '未命名方案'">
                {{ program.name || '未命名方案' }}
              </span>
            </div>
          </div>
        </NCard>
      </div>
    </NSpin>
  </div>
</template>

<style scoped>
.program-card {
  user-select: none;
  border: 1px solid #fff;
  cursor: grab;
  will-change: transform, box-shadow;
}

.program-card:hover {
  border-color: #60a5fa;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
}

.program-card:active {
  cursor: grabbing;
  transform: scale(0.98);
}

/* 拖拽时的样式 */
.program-card[draggable='true']:hover {
  box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
}

/* 拖拽开始时的样式 */
.program-card.dragging {
  opacity: 0.6;
  transform: scale(0.95);
  box-shadow: 0 8px 24px rgba(96, 165, 250, 0.4);
}
</style>
