<script setup lang="tsx">
import { ref } from 'vue';
import type { Ref } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import dayjs from 'dayjs';
import { useTabsStore } from 'component-library';
import { fetchPostExperimentList } from '@/service/api';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { experimentStatusRecord } from '@/constants/business';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { fetchPostExperimentDelete, fetchPostExperimentStart, fetchPostExperimentStop } from '@/service/api/experiment';
import { useRouterPush } from '@/hooks/common/router';
import TableHeaderOperation from '@/components/advanced/table-header-operation.vue';
import ExperimentSearch from './modules/exp-table-search.vue';
import ExperimentOperateModal from './modules/exp-table-operate-model.vue';

export type OperateType = NaiveUI.TableOperateType;

const { bool: visible, setTrue: openModal } = useBoolean();

const { routerPushByKey } = useRouterPush();

const appStore = useAppStore();

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination, searchParams } = useTable({
  apiFn: fetchPostExperimentList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    stage: null,
    name: null,
    type: null
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center'
    },
    {
      key: 'name',
      title: $t('page.experiment.table.name'),
      align: 'center',
      render: row => (
        <span
          class="cursor-pointer text-primary"
          onClick={() => {
            useTabsStore().addTab({
              title: row.name,
              path: `/experiment/detail/${row.id}`,
              query: {
                id: row.id
              },
              type: 'rany-research',
              closable: true,
              label: row.name,
              id: row.id
            });
            routerPushByKey('experiment_detail', { params: { id: row.id! } });
          }}
        >
          {row.name}
        </span>
      )
    },
    {
      key: 'shortName',
      title: $t('page.experiment.table.shortName'),
      align: 'center'
    },
    {
      key: 'estimatedHuman',
      title: $t('page.experiment.table.estimatedHuman'),
      align: 'center'
    },
    {
      key: 'estimatedHosp',
      title: $t('page.experiment.table.estimatedHosp'),
      align: 'center'
    },
    {
      key: 'projectDiseList',
      title: $t('page.experiment.table.disease'),
      align: 'center',
      render: row => {
        return row.diseaseName;
      }
    },
    {
      key: 'stage',
      title: $t('page.experiment.table.stage'),
      align: 'center',
      render: row => {
        return row.stageName;
      }
    },
    {
      key: 'type',
      title: $t('page.experiment.table.type'),
      align: 'center',
      render: row => {
        return row.typeName;
      }
    },
    {
      key: 'startTime',
      title: $t('page.experiment.table.startTime'),
      align: 'center',
      render: row => {
        return `${dayjs(row.startTime).format('YYYY-MM-DD')}-${dayjs(row.endTime).format('YYYY-MM-DD')}`;
      }
    },
    {
      key: 'actualStartTime',
      title: $t('page.experiment.table.actualStartTime'),
      align: 'center'
    },
    {
      key: 'status',
      title: $t('page.experiment.table.status'),
      align: 'center',
      render: row => {
        if (row.status === null) {
          return null;
        }

        const tagMap: Record<Api.Experiment.ExperimentStatus, NaiveUI.ThemeColor> = {
          1: 'success',
          0: 'warning',
          2: 'error'
        };

        const label = $t(experimentStatusRecord[row.status as Api.Common.EnableStatus]);

        return <NTag type={tagMap[row.status as Api.Common.EnableStatus]}>{label}</NTag>;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 230,
      render: row => (
        <div class="flex-center justify-end gap-8px">
          {row.status === 1 ? (
            <NPopconfirm onPositiveClick={() => handleStop(row.id!)}>
              {{
                default: () => '确定要终止该试验吗?',
                trigger: () => (
                  <NButton type="primary" ghost size="small">
                    终止
                  </NButton>
                )
              }}
            </NPopconfirm>
          ) : (
            <NPopconfirm onPositiveClick={() => handleStart(row.id!)}>
              {{
                default: () => '确定要启动该试验吗?',
                trigger: () => (
                  <NButton type="primary" ghost size="small">
                    启动
                  </NButton>
                )
              }}
            </NPopconfirm>
          )}
          <NButton type="primary" ghost size="small" onClick={() => edit(row)}>
            {$t('common.edit')}
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id!)}>
            {{
              default: () => '确定要删除该试验吗?',
              trigger: () => (
                <NButton type="primary" ghost size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

async function handleStop(id: string) {
  try {
    await fetchPostExperimentStop({
      id,
      userName: 'admin',
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
    });
    window.$message?.success('终止成功');
    getData();
  } catch {
    window.$message?.error('终止失败');
  }
}

async function handleStart(id: string) {
  const res = await fetchPostExperimentStart({
    id,
    userName: 'admin',
    userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
  });
  if (res.response.data.status === 'OK') {
    window.$message?.success('启动成功');
    getData();
  }
}

const { checkedRowKeys, onBatchDeleted } = useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  onBatchDeleted();
}

const operateType = ref<OperateType>('add');

function handleAdd() {
  operateType.value = 'add';
  openModal();
}

const editingData: Ref<Api.Experiment.Experiment | null> = ref(null);

function edit(item: Api.Experiment.Experiment) {
  operateType.value = 'edit';
  editingData.value = { ...item };
  openModal();
}

async function handleDelete(id: string) {
  await fetchPostExperimentDelete({ id, userName: 'admin', userId: 'ed2bcf1d7a584a69bf99bd0df530475a' });
  window.$message?.success('删除成功');
  getData();
}

// 处理搜索事件
function handleSearch(value: string) {
  searchParams.name = value || null;
  getDataByPage();
}

// 处理筛选变化事件
function handleFilterChange(filterParams: any) {
  Object.assign(searchParams, filterParams);
  getDataByPage();
}
</script>

<template>
  <div class="flex-col-stretch flex-1 overflow-hidden lt-sm:overflow-auto">
    <NCard
      :bordered="false"
      size="small"
      class="data-common-table sm:flex-1-hidden"
      content-style="padding:16px 24px;display:flex;flex-direction:column;"
    >
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :disabled-delete="checkedRowKeys.length === 0"
        :loading="loading"
        search-placeholder="请输入试验名称"
        @add="handleAdd"
        @delete="handleBatchDelete"
        @refresh="getData"
        @search="handleSearch"
      >
        <template #filters>
          <ExperimentSearch :search-params="searchParams" @filter-change="handleFilterChange" />
        </template>
      </TableHeaderOperation>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <ExperimentOperateModal
        v-model:visible="visible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
