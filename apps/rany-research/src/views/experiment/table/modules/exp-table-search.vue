<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import type { SelectOption } from 'naive-ui';
import { useExperimentStore } from '@/store/modules/experiment';

interface Props {
  searchParams?: any;
}

const props = withDefaults(defineProps<Props>(), {
  searchParams: () => ({
    pageNo: 1,
    pageSize: 10,
    stage: null,
    name: null,
    type: null
  })
});

interface Emits {
  (e: 'filter-change', params: any): void;
}

const emit = defineEmits<Emits>();

const experimentStore = useExperimentStore();

// 研究阶段选择器选项
const stageOptions = ref<SelectOption[]>([]);
const selectedStage = ref<string | null>(props.searchParams?.stage || null);

// 研究类型选择器选项
const typeOptions = ref<SelectOption[]>([]);
const selectedType = ref<string | null>(props.searchParams?.type || null);

// 获取研究阶段选项
async function getStageList() {
  if (stageOptions.value.length === 0 && experimentStore.researchPhaseDict.length === 0) {
    await experimentStore.getResearchPhaseDict();
  }
  stageOptions.value = experimentStore.researchPhaseDict;
}

// 获取研究类型选项
async function getTypeList() {
  if (typeOptions.value.length === 0 && experimentStore.researchTypeDict.length === 0) {
    await experimentStore.getResearchTypeDict();
  }
  typeOptions.value = experimentStore.researchTypeDict;
}

// 处理研究阶段筛选变化
const handleStageChange = (value: string | null) => {
  selectedStage.value = value;
  emit('filter-change', { stage: value });
};

// 处理研究类型筛选变化
const handleTypeChange = (value: string | null) => {
  selectedType.value = value;
  emit('filter-change', { type: value });
};

// 监听外部搜索参数变化
watch(
  () => props.searchParams?.stage,
  newValue => {
    selectedStage.value = newValue || null;
  }
);

watch(
  () => props.searchParams?.type,
  newValue => {
    selectedType.value = newValue || null;
  }
);

onMounted(() => {
  getStageList();
  getTypeList();
});
</script>

<template>
  <div class="flex gap-16px">
    <div class="filter-item">
      <div class="filter-label">研究类型：</div>
      <div class="filter-control">
        <NSelect
          v-model:value="selectedType"
          class="filter-select"
          placeholder="请选择研究类型"
          :options="typeOptions"
          clearable
          @update:value="handleTypeChange"
        />
      </div>
    </div>

    <div class="filter-item">
      <div class="filter-label">研究阶段：</div>
      <div class="filter-control">
        <NSelect
          v-model:value="selectedStage"
          class="filter-select"
          placeholder="请选择研究阶段"
          :options="stageOptions"
          clearable
          @update:value="handleStageChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  white-space: nowrap;
  font-size: 14px;
  color: var(--text-color-2);
}

.filter-select {
  min-width: 120px;
}
</style>
