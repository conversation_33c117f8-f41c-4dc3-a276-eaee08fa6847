<script setup lang="tsx">
import { NButton, NDatePicker, NForm, NFormItem, NInput, NInputNumber, NSelect, NSpace, NTransfer } from 'naive-ui';
import type { TransferRenderTargetLabel } from 'naive-ui';
import { computed, ref, watch } from 'vue';
import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
import { fetchGetExperimentDetail, fetchPostExperimentAdd, fetchPostExperimentEdit } from '@/service/api';
import { useExperimentStore } from '@/store/modules/experiment';
import { useDiseaseStore } from '@/store/modules/database/disease';
import { fetchPostHospitalList } from '@/service/api/hospital';

export type OperateType = NaiveUI.TableOperateType;

interface Props {
  operateType: OperateType;
  rowData?: Api.Experiment.Experiment | null;
}

const diseaseStore = useDiseaseStore();

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', { default: false });

const { formRef, validate, restoreValidation } = useNaiveForm();

const experimentStore = useExperimentStore();

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    add: '新增试验',
    edit: '编辑试验'
  };
  return titles[props.operateType];
});

function createDefaultModel(): Api.Experiment.Experiment {
  return {
    name: '',
    shortName: '',
    registNumber: '',
    stage: '',
    design: '',
    type: '',
    blindMethod: '',
    startTime: '',
    endTime: '',
    estimatedHosp: 0,
    estimatedHuman: 0,
    purpose: '',
    inCondition: '',
    exclCondition: '',
    outCondition: '',
    hospitalList: [],
    projectDiseList: []
  };
}

const formModel = ref<Api.Experiment.Experiment>(createDefaultModel());

const rules = {
  name: {
    required: true,
    message: '请输入试验名称',
    trigger: ['blur', 'input']
  }
};

function closeDrawer() {
  visible.value = false;
  formModel.value = createDefaultModel();
  restoreValidation();
}

const diseaseOptions = ref<Api.Common.CommonOption[]>([]);

async function getDiseaseList() {
  if (diseaseOptions.value.length === 0) {
    await diseaseStore.getDiseaseList();
    diseaseOptions.value = diseaseStore.diseaseList;
  }
}

const stageOptions = ref<Api.Common.CommonOption[]>([]);

async function getStageList() {
  if (stageOptions.value.length === 0 && experimentStore.researchPhaseDict.length === 0) {
    await experimentStore.getResearchPhaseDict();
  }
  stageOptions.value = experimentStore.researchPhaseDict;
}

const typeOptions = ref<Api.Common.CommonOption[]>([]);

async function getTypeList() {
  if (typeOptions.value.length === 0 && experimentStore.researchTypeDict.length === 0) {
    await experimentStore.getResearchTypeDict();
  }
  typeOptions.value = experimentStore.researchTypeDict;
}

const designOptions = ref<Api.Common.CommonOption[]>([]);

async function getDesignList() {
  if (designOptions.value.length === 0) {
    await experimentStore.getResearchDesignDict();
    designOptions.value = experimentStore.researchDesignDict;
  }
}

const blindMethodOptions = ref<Api.Common.CommonOption[]>([]);

async function getBlindMethodList() {
  if (blindMethodOptions.value.length === 0) {
    await experimentStore.getBlindMethodDict();
    blindMethodOptions.value = experimentStore.blindMethodDict;
  }
}

const hospitalOptions = ref<Api.Common.CommonOption[]>([]);

async function getHospitalList() {
  const res = await fetchPostHospitalList({
    pageNo: 1,
    pageSize: 1000
  });
  hospitalOptions.value = res.data?.records
    ?.filter(item => item.status === 1)
    ?.map(item => ({
      label: item.name,
      value: item.id,
      estimatedHuman: 0
    })) as any;
}

const renderLabel: TransferRenderTargetLabel = ({ option }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', margin: '6px 0' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
        <span>{option.label}</span>
        <NInputNumber
          value={(option as any).estimatedHuman}
          onUpdateValue={value => {
            (option as any).estimatedHuman = value;
          }}
          size="small"
          placeholder="预计受试者"
          style={{ width: '120px' }}
        />
      </div>
    </div>
  );
};

const renderTitle = () => {
  return (
    <div class="flex justify-end">
      <span class="text-end text-12px">预计受试者</span>
    </div>
  );
};

const timeRange = ref<[number, number] | null>(null);
async function initFormData() {
  if (props.operateType === 'edit' && props.rowData?.id) {
    try {
      const { data } = await fetchGetExperimentDetail(props.rowData.id);
      Object.assign(formModel.value, data);
      formModel.value.projectDiseList = data?.diseaseList?.map(item => item.diseasedId);
      formModel.value.hospitalList = data?.projectHospitalList?.map(item => item.hospitalId);
      hospitalOptions.value.forEach(option => {
        const hospital: any = data?.projectHospitalList?.find(item => item.hospitalId === option.value);
        if (hospital) {
          (option as any).estimatedHuman = Number(hospital.estimatedHuman);
        }
      });
      if (data?.startTime && data?.endTime) {
        timeRange.value = [new Date(data.startTime).getTime(), new Date(data.endTime).getTime()];
      }
    } catch (error) {
      console.log(error);
      window.$message?.error('获取详情失败');
    }
  }
}

async function initOptions() {
  await Promise.all([
    getStageList(),
    getTypeList(),
    getDesignList(),
    getBlindMethodList(),
    getDiseaseList(),
    getHospitalList()
  ]);
}

async function handleSubmit() {
  try {
    await validate();
    // 深拷贝
    const params = JSON.parse(JSON.stringify(formModel.value));
    params.userId = 'ed2bcf1d7a584a69bf99bd0df530475a';
    params.userName = 'admin';
    // const params = { ...formModel.value };
    if (formModel.value.projectDiseList?.length) {
      params.projectDiseList = formModel.value.projectDiseList.map(diseaseId => {
        return {
          diseasedId: diseaseId,
          diseasedName: diseaseStore.diseaseList.find(item => item.value === diseaseId)?.label
        };
      });
    }
    if (formModel.value.hospitalList?.length) {
      params.hospitalList = formModel.value.hospitalList.map(hospital => {
        let estimatedHuman = 0;
        if (hospital) {
          // 去option中找estimatedHuman值
          const option = hospitalOptions.value.find(item => item.value === hospital);
          if (option) {
            estimatedHuman = (option as any).estimatedHuman;
          }
        }
        return { hospitalId: hospital, estimatedHuman };
      });
    }
    if (props.operateType === 'edit' && props.rowData?.id) {
      params.id = props.rowData.id;
      await fetchPostExperimentEdit(params);
      window.$message?.success('编辑成功');
    } else {
      await fetchPostExperimentAdd(params);
      window.$message?.success('新增成功');
    }
    closeDrawer();
    emit('submitted');
  } catch (error) {
    console.log(error);
    window.$message?.error('表单校验失败');
  }
}

watch(visible, async newVal => {
  if (newVal) {
    await initOptions();
    await initFormData();
  } else {
    formModel.value = createDefaultModel();
    restoreValidation();
  }
});

watch(timeRange, value => {
  if (value) {
    formModel.value.startTime = new Date(value[0]).toISOString().slice(0, 19).replace('T', ' ');
    formModel.value.endTime = new Date(value[1]).toISOString().slice(0, 19).replace('T', ' ');
  } else {
    formModel.value.startTime = '';
    formModel.value.endTime = '';
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-800px">
    <NScrollbar class="h-480px pr-20px">
      <NForm ref="formRef" :model="formModel" :rules="rules" label-placement="left" label-width="120">
        <NFormItem label="试验名称" path="name" required>
          <NInput v-model:value="formModel.name" placeholder="请输入试验名称" />
        </NFormItem>

        <NFormItem label="名称简写" path="shortName">
          <NInput v-model:value="formModel.shortName" placeholder="请输入名称简写" />
        </NFormItem>

        <NFormItem label="注册号" path="registNumber">
          <NInput v-model:value="formModel.registNumber" placeholder="请输入注册号" />
        </NFormItem>

        <NFormItem label="研究疾病" path="projectDiseList">
          <NSelect
            v-model:value="formModel.projectDiseList"
            :options="diseaseOptions as any"
            placeholder="请选择研究疾病"
            multiple
          />
        </NFormItem>

        <NFormItem label="研究阶段" path="stage">
          <NSelect v-model:value="formModel.stage" :options="stageOptions" placeholder="请选择研究阶段" />
        </NFormItem>

        <NFormItem label="研究类型" path="type">
          <NSelect v-model:value="formModel.type" :options="typeOptions" placeholder="请选择研究类型" />
        </NFormItem>

        <NFormItem label="研究设计" path="design">
          <NSelect v-model:value="formModel.design" :options="designOptions" placeholder="请选择研究设计" />
        </NFormItem>

        <NFormItem label="盲法" path="blindMethod">
          <NSelect v-model:value="formModel.blindMethod" :options="blindMethodOptions" placeholder="请选择盲法" />
        </NFormItem>

        <NFormItem label="研究实施时间" path="startTime">
          <NDatePicker
            v-model:value="timeRange"
            type="datetimerange"
            clearable
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择研究实施时间范围"
          />
        </NFormItem>

        <NFormItem label="预计样本量" path="estimatedHuman">
          <NInputNumber v-model:value="formModel.estimatedHuman" :min="0" placeholder="请输入预计样本量" />
        </NFormItem>

        <NFormItem label="预计医院数" path="estimatedHosp">
          <NInputNumber v-model:value="formModel.estimatedHosp" :min="0" placeholder="请输入预计医院数" />
        </NFormItem>

        <NFormItem label="研究目的" path="purpose">
          <NInput v-model:value="formModel.purpose" type="textarea" placeholder="请输入研究目的" />
        </NFormItem>

        <NFormItem label="入组条件" path="inCondition">
          <NInput v-model:value="formModel.inCondition" type="textarea" placeholder="请输入入组条件" />
        </NFormItem>

        <NFormItem label="排除条件" path="exclCondition">
          <NInput v-model:value="formModel.exclCondition" type="textarea" placeholder="请输入排除条件" />
        </NFormItem>

        <NFormItem label="出组条件" path="outCondition">
          <NInput v-model:value="formModel.outCondition" type="textarea" placeholder="请输入出组条件" />
        </NFormItem>

        <NFormItem label="选择医院" path="hospitalList">
          <NTransfer
            v-model:value="formModel.hospitalList"
            :options="hospitalOptions"
            virtual-scroll
            :render-target-label="renderLabel"
            :target-title="renderTitle"
          ></NTransfer>
        </NFormItem>
      </NForm>
    </NScrollbar>
    <template #footer>
      <NSpace justify="end" :size="16">
        <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
        <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
