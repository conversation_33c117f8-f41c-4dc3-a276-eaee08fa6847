<script setup lang="ts">
import { ref } from 'vue';

import ExpDetailInfo from './modules/exp-detail-info.vue';
import ExpSetting from './modules/exp-setting.vue';
import ExpParticipant from './modules/exp-participant.vue';
import ExpParticipantDetail from './modules/participant/exp-participant-detail.vue';

const activeTab = ref('department');

const activePage = ref('table');

const detailId = ref('');
const handleDetail = (id: string) => {
  activePage.value = 'detail';
  detailId.value = id;
};
const handleBack = () => {
  activePage.value = 'table';
  detailId.value = '';
};
</script>

<template>
  <div class="h-full flex">
    <NCard :bordered="false" size="small" class="relative h-full overflow-y-auto card-wrapper">
      <NTabs v-model:value="activeTab" type="line" animated class="h-full" justify-content="end">
        <NTabPane name="department" tab="试验概览" class="h-full">
          <ExpDetailInfo />
        </NTabPane>
        <NTabPane name="users" tab="受试者" class="h-full">
          <ExpParticipant v-if="activePage === 'table'" @update:detail="handleDetail" />
          <ExpParticipantDetail v-else :id="detailId" type="participant" />
        </NTabPane>
        <NTabPane name="experiments" tab="试验设置" class="h-full">
          <ExpSetting />
        </NTabPane>
      </NTabs>
      <NButton v-show="activePage === 'detail'" size="small" class="absolute left-3 top-3" @click="handleBack">
        返回
      </NButton>
    </NCard>
  </div>
</template>

<style scoped>
:deep(.n-tabs-pane-wrapper) {
  height: 100%;
  display: flex;
}
</style>
