<script setup lang="tsx">
import { NButton, useDialog } from 'naive-ui';
import { reactive, ref } from 'vue';
import { useBoolean } from '@sa/hooks';
import { fetchGetRiskDel, fetchGetRiskList } from '@/service/api/risk';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import RiskCreateOperateDrawer from '@/views/data-base/risk/modules/risk-operate-drawer.vue';
import RiskOperateDialog from '../risk/exp-setting-risk-operate-model.vue';

const appStore = useAppStore();

const props = defineProps<{
  experimentId: string;
}>();

const dialog = useDialog();
const { bool: visible, setTrue: openModal } = useBoolean();

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination } = useTable({
  apiFn: fetchGetRiskList,
  showTotal: false,
  apiParams: reactive({
    pageNo: 1,
    pageSize: 10,
    projectId: props.experimentId
  }),
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 48
    },
    {
      key: 'risk',
      title: $t('page.database.risk.risk'),
      align: 'center',
      width: 60,
      render: row => {
        return <span>{row.riskName}</span>;
      }
    },
    {
      key: 'shortName',
      title: '研究疾病',
      align: 'center',
      width: 100
    },
    {
      key: 'groupCondition',
      title: $t('page.database.risk.groupCondition'),
      align: 'center'
    },

    {
      key: 'updateTime',
      title: $t('page.database.disease.updateTime'),
      align: 'center',
      width: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 160,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleEdit(row.id)}>
            {$t('common.edit')}
          </NButton>
          <NButton type="error" ghost size="small" onClick={() => handleDelete(row.id!)}>
            {$t('common.delete')}
          </NButton>
        </div>
      )
    }
  ]
});

const { operateType, editingData, checkedRowKeys, onBatchDeleted, onDeleted } = useTableOperate(data, getData);
const drawerVisible = ref(false);
const drawerEditingData = ref<any>(null);

async function handleBatchDelete() {
  // request
  onBatchDeleted();
}

function handleEdit(id: string | undefined) {
  operateType.value = 'edit';
  drawerEditingData.value = data.value.find(item => item.id === id) || null;
  drawerVisible.value = true;
}

async function handleDelete(id: string) {
  const res = await fetchGetRiskDel(id);
  if (res.data?.code !== 200) {
    dialog.create({
      title: '删除',
      content: `您确定要删除吗？
      删除后历史记录不可查询！`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        // request
        onDeleted();
      }
    });
  } else {
    // 根据后端判断显示：该疾病存在诊疗计划/疗程，不可删除，请先将方案/疗程转移！
    window.$message?.error('该疾病存在诊疗计划/疗程，不可删除，请先将方案/疗程转移！');
  }
}

// 新建风险 - 打开抽屉组件，用于创建完整的风险配置
function handleCreate() {
  operateType.value = 'add';
  drawerEditingData.value = null; // 清空编辑数据
  drawerVisible.value = true;
}

// 新增风险 - 打开弹窗组件，用于快速添加基础风险信息
function handleAdd() {
  operateType.value = 'add';
  editingData.value = null; // 清空编辑数据
  openModal();
}
</script>

<template>
  <div class="min-h-500px flex gap-16px overflow-hidden lt-sm:overflow-auto">
    <div class="data-common-table flex-col-stretch gap-1 overflow-hidden">
      <div class="flex justify-end gap-2">
        <NButton class="mr-10px" size="small" ghost @click="handleCreate">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          新建
        </NButton>

        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </div>

      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <RiskOperateDialog
        v-model:visible="visible"
        :operate-type="operateType"
        :row-data="editingData"
        :project-id="props.experimentId"
        @submitted="getDataByPage"
      />
      <RiskCreateOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="drawerEditingData"
        :project-id="props.experimentId"
        @submitted="getDataByPage"
      />
    </div>
  </div>
</template>

<style scoped></style>
