<script setup lang="tsx">
import { computed, reactive, ref, watch } from 'vue';
import { NTooltip } from 'naive-ui';
import type { VNodeChild } from 'vue';
import type { SelectOption } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchGetRiskList, fetchPostExperimentDangerAdd } from '@/service/api';
import { $t } from '@/locales';

defineOptions({
  name: 'RiskOperateModel'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Risk.Risk | null;
  /** the project id */
  projectId: string;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: $t('page.database.risk.add'),
    edit: $t('page.database.risk.edit')
  };
  return titles[props.operateType];
});

type Model = Pick<Api.Risk.Risk, 'projectId' | 'diseaseRiskId' | 'id'>;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    projectId: '',
    diseaseRiskId: '',
    id: ''
  };
}

type RuleKey = Extract<keyof Model, 'diseaseRiskId'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  diseaseRiskId: defaultRequiredRule
};

const riskOptions = ref<{ label: string; value: string; tooltip: string }[]>([]);

async function getRiskListOptions() {
  const { error, data } = await fetchGetRiskList({
    pageNo: 1,
    pageSize: 1000
  });
  if (!error) {
    const options = data.records.map(item => ({
      label: `${item.shortName || ''} / ${item.riskName || ''}`,
      value: item.id || '',
      tooltip: item.groupCondition || ''
    }));

    riskOptions.value = options;
  }
}

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeModal() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  const submitData: any = {
    ...model,
    projectId: props.projectId,
    userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
    userName: 'admin'
  };
  if (props.operateType === 'add') {
    const { error } = await fetchPostExperimentDangerAdd(submitData);
    if (!error) {
      window.$message?.success($t('common.addSuccess'));
      closeModal();
      emit('submitted');
    }
  }
}

function renderLabel(option: SelectOption): VNodeChild {
  return (
    <NTooltip trigger="hover">
      {{
        trigger: () => option.label,
        default: () => (
          <div>
            <div>分型条件:</div>
            <div>{(option as any).tooltip}</div>
          </div>
        )
      }}
    </NTooltip>
  );
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
    getRiskListOptions();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" style="width: 600px">
    <NForm ref="formRef" :model="model" :rules="rules">
      <NFormItem path="diseaseRiskId">
        <NSelect
          v-model:value="model.diseaseRiskId"
          :options="riskOptions"
          placeholder="请选择危险度分型条件"
          :consistent-menu-width="false"
          clearable
          :render-label="renderLabel"
        ></NSelect>
      </NFormItem>
    </NForm>
    <template #footer>
      <div class="w-full flex justify-end gap-4">
        <NButton @click="closeModal">{{ $t('common.cancel') }}</NButton>
        <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped></style>
