<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import type { FormInst } from 'naive-ui';
import { useBagStore } from '@/store/modules/bag';
import PositionCascader from './modal-position-cascader.vue';

interface FormModel {
  relation: number;
  pursuant: string;
  insertPoint: string | null;
}

const emit = defineEmits<{
  (e: 'confirm'): void;
}>();

const props = defineProps<{
  sbagId?: string;
}>();

const showModal = ref(false);
const formModel = ref<FormModel>({
  relation: 1,
  pursuant: '',
  insertPoint: null
});
const stageId = ref('');
const formRef = ref<FormInst | null>(null);

const store = useBagStore();
const { loading } = storeToRefs(store);

const rules = {
  relation: [
    { required: true, message: '请输入休疗天数' },
    { type: 'number', min: 1, max: 100, message: '休疗天数必须在1-100之间' }
  ]
} satisfies Record<string, App.Global.FormRule[]>;

const resetForm = () => {
  formModel.value = {
    relation: 1,
    pursuant: '',
    insertPoint: null
  };
  formRef.value?.restoreValidation();
};

const handleCancel = () => {
  showModal.value = false;
  resetForm();
};

const handleConfirm = async () => {
  try {
    await formRef.value?.validate();

    await store.bindSolutionOrRestToStage({
      relation: formModel.value.relation,
      insertPoint: formModel.value.insertPoint || undefined,
      type: 2,
      pursuant: formModel.value.pursuant,
      stageId: stageId.value
    });
    handleCancel();
    emit('confirm');
  } catch (err) {
    console.error('表单验证失败:', err);
  }
};

defineExpose({
  show: () => {
    showModal.value = true;
  }
});
</script>

<template>
  <div>
    <NModal v-model:show="showModal" title="添加休疗" class="w-xl" size="small" preset="card" @close="handleCancel">
      <NForm ref="formRef" :rules="rules" :model="formModel" label-placement="left" label-width="80">
        <NFormItem label="插入位置" path="insertPoint">
          <PositionCascader
            v-model:value="formModel.insertPoint"
            :sbag-id="props.sbagId"
            @update-stage="stageId = $event!"
          />
        </NFormItem>
        <NFormItem label="休疗天数" path="relation">
          <NInputNumber
            v-model:value="formModel.relation"
            placeholder="请输入休疗天数"
            :min="1"
            :max="100"
            class="w-full"
          />
        </NFormItem>
        <NFormItem label="依据" path="pursuant">
          <NInput v-model:value="formModel.pursuant" type="textarea" placeholder="请输入依据" :rows="3" />
        </NFormItem>
      </NForm>
      <div class="flex justify-end gap-3 px-6 pb-4">
        <NButton class="w-24" :disabled="loading" @click="handleCancel">取消</NButton>
        <NButton type="primary" :loading="loading" class="w-24" @click="handleConfirm">确定</NButton>
      </div>
    </NModal>
  </div>
</template>

<style scoped></style>
