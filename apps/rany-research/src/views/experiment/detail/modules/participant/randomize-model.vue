<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useNaiveForm } from '@/hooks/common/form';
import { useBagStore } from '@/store/modules/bag';
import { getParticipantDetail } from '@/service/api/participant';

const store = useBagStore();

const { loading, randomizeModel } = storeToRefs(store);
const { data, load } = store.getPatientBagDetail();

const visible = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();

const patientId = ref('');

const tranchesOptions = [
  {
    label: '非随机',
    value: 'non-random'
  }
];

const options = computed(() => {
  return data.value?.sbag.stages.map(stage => ({
    label: `${stage.name}`,
    value: stage.id,
    children: stage.solutions.map(program => ({
      label: program.soluName,
      value: program.solutionId
    }))
  }));
});

watch(visible, () => {
  if (visible.value) {
    randomizeModel.value.patientId = patientId.value;
    restoreValidation();
  } else {
    store.resetRandomizeModel();
  }
});

const sbagId = ref('');

function handleFocus() {
  if (sbagId.value) {
    load(sbagId.value);
  }
}

function handleCancel() {
  visible.value = false;
}

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

function handleConfirm() {
  validate().then(async () => {
    const result = await store.generateRandomizeAction();
    if (result) {
      visible.value = false;
      emit('submitted');
    }
  });
}

async function getsBagId(id: string) {
  const res = await getParticipantDetail(id);
  sbagId.value = res.data?.patient?.sbagId ?? '';
}

defineExpose({
  show: (patientId1: string, id: string) => {
    visible.value = true;
    patientId.value = patientId1;
    getsBagId(id);
  }
});
</script>

<template>
  <NModal v-model:show="visible" class="w-2xl" title="随机化" :mask-closable="false" preset="card">
    <NForm
      ref="formRef"
      :model="randomizeModel"
      label-placement="top"
      :label-width="100"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="组别" path="tranches" :required="true">
        <NSelect v-model:value="randomizeModel.tranches" :options="tranchesOptions" placeholder="请选择组别" />
      </NFormItem>
      <NFormItem label="诊疗计划" path="clinicPlanId">
        <PlanSelect v-model:value="randomizeModel.clinicPlanId" placeholder="请选择诊疗计划" />
      </NFormItem>
      <NFormItem label="计划插入合并位置" path="sbagSoluId">
        <NSelect
          v-model:value="randomizeModel.sbagSoluId"
          :options="options"
          placeholder="请选择计划插入合并位置"
          @focus="handleFocus"
        />
      </NFormItem>
    </NForm>

    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" :disabled="loading" @click="handleCancel">取消</NButton>
      <NButton type="primary" :loading="loading" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>
