<script setup lang="ts">
import { ref } from 'vue';
import dayjs from 'dayjs';
import { NCollapse, NCollapseItem } from 'naive-ui';
import PatientOperateDrawer from '@/views/data-base/patient/modules/patient-operate-drawer.vue';
import { fetchPostPatientRiskList, fetchPostTreatmentList } from '@/service/api/patient';

const props = defineProps<{
  rowData: Api.Participant.Participant | null;
  type?: 'participant' | 'patient';
}>();

const emit = defineEmits<{
  (e: 'updateData'): void;
}>();

// 状态管理
const drawerVisible = ref(false);
const operateType = ref<'add' | 'edit'>('add');
const editingData = ref<any>(null);
const riskHistory: any = ref([]);

// 获取历史危险度
async function getRiskHistory() {
  if (!props.rowData?.patient?.id || !props.rowData?.treatment?.id) {
    return;
  }

  const params = {
    patientId: props.rowData.patient.id,
    treatmentId: props.rowData.treatment.id,
    pageNo: 1,
    pageSize: 999
  };
  try {
    const res: any = await fetchPostPatientRiskList(params);

    if (res.data) {
      riskHistory.value = res.data.records;
    }
  } catch (error) {
    console.error('获取历史危险度失败:', error);
  }
}

const treatmentHistory: any = ref([]);

// 获取治疗历史
async function getTreatmentHistory() {
  if (!props.rowData?.patient?.id) {
    return;
  }

  const params = {
    patientId: props.rowData.patient.id,
    pageNo: 1,
    pageSize: 999
  };
  try {
    const res: any = await fetchPostTreatmentList(params);

    if (res.data) {
      treatmentHistory.value = res.data.records.map((item: any) => ({
        hospital: item.hospital?.name,
        outpatientNumber: item.outpatientNumber
      }));
    }
  } catch (error) {
    console.error('获取历史危险度失败:', error);
  }
}

// 处理编辑操作
function handleEdit() {
  if (!props.rowData?.patient) {
    window.$message?.error('患者信息不能为空');
    return;
  }
  operateType.value = 'edit';
  editingData.value = { ...props.rowData.patient };
  drawerVisible.value = true;
}

// 计算年龄
function getAge(birthday: string) {
  if (!birthday) return '-';
  const now = dayjs();
  const birth = dayjs(birthday);
  const years = now.diff(birth, 'year');
  const months = now.diff(birth.add(years, 'year'), 'month');
  const days = now.diff(birth.add(years, 'year').add(months, 'month'), 'day');
  return `${years}岁${months}月${days}天`;
}

// 处理提交完成
function handleSubmitted() {
  drawerVisible.value = false;
  emit('updateData');
}
</script>

<template>
  <div>
    <NCollapse :default-expanded-names="['1', '2', '3']" class="mb-4">
      <NCollapseItem v-if="type === 'participant'" name="1" class="patient-collapse-item">
        <template #header>
          <span class="text-base font-600">受试者基本信息</span>
        </template>
        <div class="flex items-center">
          <div class="flex-1 pr-4">
            <NDescriptions label-placement="left" :column="1">
              <NDescriptionsItem label="受试者编号">
                <span class="text-base">{{ rowData?.number || '-' }}</span>
              </NDescriptionsItem>
              <NDescriptionsItem label="诊疗计划">
                {{ rowData?.clinicPlanId || '-' }}
              </NDescriptionsItem>
              <NDescriptionsItem label="创建人">
                {{ rowData?.createUser || '-' }}
              </NDescriptionsItem>
            </NDescriptions>
          </div>
          <div class="flex-[2]">
            <NDescriptions label-placement="left" :column="3">
              <NDescriptionsItem label="危险度">
                {{ rowData?.diseaseRiskName || '-' }}
              </NDescriptionsItem>
              <NDescriptionsItem label="入组状态">
                {{
                  rowData?.status === 0
                    ? '出组'
                    : rowData?.status === 1
                      ? '入组'
                      : rowData?.status === 2
                        ? '推荐入组'
                        : rowData?.status === 3
                          ? '推荐出组'
                          : '未入组'
                }}
              </NDescriptionsItem>
              <NDescriptionsItem label="疾病分型">
                {{ rowData?.diseaseName || '-' }}
              </NDescriptionsItem>
              <NDescriptionsItem label="医院">
                {{ rowData?.hospitalName || '-' }}
              </NDescriptionsItem>
              <NDescriptionsItem label="创建时间">
                {{ rowData?.createTime ? dayjs(rowData.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
              </NDescriptionsItem>
              <NDescriptionsItem label="更新时间">
                {{ rowData?.updateTime ? dayjs(rowData.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
              </NDescriptionsItem>
            </NDescriptions>
          </div>
        </div>
      </NCollapseItem>

      <NCollapseItem name="2" class="patient-collapse-item">
        <template #header>
          <span class="text-base font-600">诊断信息</span>
        </template>
        <NDescriptions label-placement="left" :columns="4">
          <NDescriptionsItem label="门诊号">
            {{ rowData?.treatment?.outpatientNumber || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="诊断日期">
            {{ rowData?.treatment?.diagnosisTime || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="初步诊断">
            {{ rowData?.treatment?.diagnosisInit || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="最终诊断">
            {{ rowData?.treatment?.diagnosisFinal || '-' }}
          </NDescriptionsItem>
        </NDescriptions>
      </NCollapseItem>

      <NCollapseItem name="3" class="patient-collapse-item">
        <template #header>
          <div class="w-full flex items-center justify-between">
            <div class="text-base font-600">患者基本信息</div>
            <div class="mt-2">
              <NButtonGroup size="small">
                <NButton @click="handleEdit">编辑患者</NButton>
              </NButtonGroup>
            </div>
          </div>
        </template>
        <div class="grid grid-cols-4 gap-4">
          <NDescriptions label-placement="left" :column="1">
            <NDescriptionsItem label="患者姓名">
              {{ rowData?.patient.name || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="性别">
              {{ rowData?.patient.gender === '1' ? '男' : '女' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="年龄">
              {{ rowData?.patient.birthday ? getAge(rowData.patient.birthday) : '-' }}
            </NDescriptionsItem>

            <NDescriptionsItem label="出生日期">
              {{ rowData?.patient.birthday ? dayjs(rowData.patient.birthday).format('YYYY-MM-DD') : '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="联系电话">
              {{ rowData?.patient.phoneNumber || '-' }}
            </NDescriptionsItem>
          </NDescriptions>

          <NDescriptions label-placement="left" :column="1">
            <NDescriptionsItem label="民族">
              {{ rowData?.patient.nationName || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="身高">{{ rowData?.patient.height || '-' }} cm</NDescriptionsItem>
            <NDescriptionsItem label="体重">{{ rowData?.patient.weight || '-' }} kg</NDescriptionsItem>
            <NDescriptionsItem label="体表面积">{{ rowData?.patient.BSA + 'm²' }}</NDescriptionsItem>
            <NDescriptionsItem v-if="props.type === 'patient'" label="临床危险度">
              <NTooltip trigger="click">
                <template #trigger>
                  <span class="cursor-pointer underline" @click="getRiskHistory">
                    {{ rowData?.risk.name || '-' }}
                  </span>
                </template>

                <div>
                  <div class="font-bold">历史危险度</div>

                  <div class="flex justify-between gap-3">
                    <span>危险度</span>
                    <span>调整时间</span>
                  </div>

                  <div v-for="(item, index) in riskHistory" :key="index">
                    <div class="flex justify-between gap-5">
                      <span>{{ item.riskName }}</span>
                      <span>{{ dayjs(item.createTime).format('YY/MM/DD') }}</span>
                    </div>
                  </div>
                </div>
              </NTooltip>
            </NDescriptionsItem>
          </NDescriptions>

          <NDescriptions label-placement="left" :column="1">
            <NDescriptionsItem label="籍贯">
              {{ rowData?.patient.nativePlace || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="证件号">
              {{ rowData?.patient.credNumber || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="亲属姓名">
              {{ rowData?.patient.relativeName || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="出生地">
              {{ rowData?.patient.birthplace || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem v-if="props.type === 'patient'" label="疾病分型">
              {{ rowData?.patient?.diseaseName || '-' }}
            </NDescriptionsItem>
          </NDescriptions>

          <NDescriptions label-placement="left" :column="1">
            <NDescriptionsItem label="户口住址">
              {{ rowData?.patient.permanentAddress || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="现住址">
              {{ rowData?.patient.familyAddress || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="婚姻状况">
              {{ rowData?.patient.maritalStatusName || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem label="门诊号">
              {{ rowData?.patient.outpatientNumber || '-' }}
            </NDescriptionsItem>
            <NDescriptionsItem v-if="props.type === 'patient'" label="就诊医院">
              <NTooltip trigger="click">
                <template #trigger>
                  <span class="cursor-pointer underline" @click="getTreatmentHistory">
                    {{ rowData?.patient?.hospital?.name || '-' }}
                  </span>
                </template>

                <div>
                  <div class="font-bold">历史危险度</div>
                  <div class="flex justify-between gap-3">
                    <span>门诊号</span>
                    <span>就诊医院</span>
                  </div>
                  <div v-for="(item, index) in treatmentHistory" :key="index">
                    <div class="flex justify-between gap-5">
                      <span>{{ item.hospital }}</span>
                      <span>{{ item.outpatientNumber }}</span>
                    </div>
                  </div>
                </div>
              </NTooltip>
            </NDescriptionsItem>
          </NDescriptions>
        </div>
      </NCollapseItem>
    </NCollapse>

    <PatientOperateDrawer
      v-model:visible="drawerVisible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="handleSubmitted"
    />
  </div>
</template>

<style scoped>
:deep(.n-statistic) {
  display: flex;
  align-items: center;
}
:deep(.n-statistic .n-statistic__label) {
  font-size: 14px;
  font-weight: 400;
  margin-right: 8px;
}
:deep(.n-statistic .n-statistic-value .n-statistic-value__content) {
  font-size: 14px;
}
:deep(.n-statistic .n-statistic-value) {
  margin-top: 0px;
}

:deep(.patient-collapse-item) {
  .n-descriptions-table-header {
    font-size: 14px;
  }
  .n-descriptions-table-content {
    font-size: 14px;
  }
}
</style>
