<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { CascaderOption } from 'naive-ui';
import { fetchGetMethodListByStageId, fetchGetStageListBySbagId } from '@/service/api/bag';

interface Props {
  sbagId?: string;
}

const props = defineProps<Props>();

const positionValue = defineModel<string | null>('value', {});

const emit = defineEmits<{
  (e: 'update-stage', value: string | null): void;
}>();

const stages = ref<Api.ClinicStage.StageVO[]>([]);

const cascaderOptions = computed<CascaderOption[]>(() => {
  return stages.value.map((stage: any) => ({
    label: stage.relation,
    value: stage.id,
    children: stage.solutions?.map((solution: any) => ({
      // 方案类型为2时，显示休疗
      label: solution.type === 2 ? '休疗' : solution.soluName,
      value: solution.relation
    }))
  }));
});

const onUpdateValue = (value: string | null, _option: any, pathValues: CascaderOption[]) => {
  positionValue.value = value;
  emit('update-stage', pathValues[0]?.value?.toString() ?? null);
};

const loadStages = async () => {
  if (!props.sbagId) {
    stages.value = [];
    return;
  }

  try {
    const stageList = await fetchGetStageListBySbagId(props.sbagId);

    if (!stageList?.data?.length) {
      stages.value = [];
      return;
    }

    const stagesWithMethods = await Promise.all(
      stageList.data.map(async stage => {
        if (!stage.id) return stage;

        const { data: methods } = await fetchGetMethodListByStageId(stage.id);
        return {
          ...stage,
          solutions: methods
        };
      })
    );

    stages.value = stagesWithMethods;
  } catch (err) {
    console.error('获取阶段和方案数据失败:', err);
    stages.value = [];
  }
};

watch(() => props.sbagId, loadStages, { immediate: true });
</script>

<template>
  <NCascader
    v-model:value="positionValue"
    :options="cascaderOptions"
    placeholder="请选择插入位置(不选则插入到最后)"
    check-strategy="child"
    clearable
    :on-update:value="onUpdateValue"
  />
</template>

<style scoped></style>
