<script setup lang="tsx">
import { NButton, NDatePicker, NForm, NFormItem, NInput, NModal, NSelect } from 'naive-ui';
import type { UploadFileInfo } from 'naive-ui';
import { useRoute } from 'vue-router';
import { computed, onMounted, ref, watch } from 'vue';
// import { $t } from '@/locales';
import { useNaiveForm } from '@/hooks/common/form';
import {
  addParticipant,
  fetchGetExperimentDetail,
  fetchPostPatientList,
  getParticipantDetail,
  updateParticipant
} from '@/service/api';
import DiseaseSelect from '@/components/common/disease-select.vue';
import RiskCascader from '@/components/common/risk-cascader.vue';
import { useUserStore } from '@/store/modules/user';

const serviceBaseUrl = import.meta.env.VITE_SERVICE_BASE_URL;

const route = useRoute();

const userStore = useUserStore();

defineOptions({
  name: 'ParticipantOperateModel'
});

interface Props {
  /** 操作类型 */
  operateType: NaiveUI.TableOperateType;
  /** 编辑行数据 */
  rowData?: Api.Participant.Participant | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const visible = defineModel<boolean>('visible', { default: false });

const actionLoading = ref(false);

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '添加受试者',
    edit: '编辑受试者'
  };
  return titles[props.operateType];
});

const patientInfoTab = ref('patientInfo');

function createDefaultModel(): Api.Participant.Participant {
  return {
    number: '',
    projectId: '',
    projectHospitalId: null,
    attachments: [],
    patientId: null,
    risk: {
      id: '',
      name: '',
      parentId: ''
    },
    patient: {
      birthday: null,
      birthplace: '',
      credNumber: '',
      credType: '',
      diagnosisFinal: '',
      diagnosisInit: '',
      diagnosisTime: null,
      familyAddress: '',
      gender: '',
      hospitalId: '',
      maritalStatus: '',
      name: '',
      nation: '',
      nativePlace: '',
      outpatientNumber: '',
      permanentAddress: '',
      phoneNumber: '',
      relativeName: '',
      riskId: '',
      userName: 'admin',
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
    },
    treatment: {},
    userName: 'admin',
    userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
  };
}

const patientModal = ref<Api.Participant.Participant>(createDefaultModel());

async function handleInitModel() {
  Object.assign(patientModal.value, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    const res = await getParticipantDetail(props.rowData.id as string);
    if (!res.error) {
      handlePatientNameSearch(res.data.patient.name!);
      Object.assign(patientModal.value, res.data);
    }
  }
}

const rules = {
  number: {
    required: true,
    message: '请输入受试者编号',
    trigger: ['blur', 'input']
  },
  projectHospitalId: {
    required: true,
    message: '请选择就诊医院',
    trigger: ['blur', 'change']
  },
  'patient.name': {
    required: true,
    message: '请输入患者姓名',
    trigger: ['blur', 'input']
  },
  'patient.gender': {
    required: true,
    message: '请选择性别',
    trigger: ['blur', 'change']
  },
  'patient.height': {
    required: true,
    message: '请输入身高',
    trigger: ['blur', 'input']
  },
  'patient.weight': {
    required: true,
    message: '请输入体重',
    trigger: ['blur', 'input']
  }
};

const genderoptions = [
  {
    label: '男',
    value: '1'
  },
  {
    label: '女',
    value: '2'
  }
];

const maritalOptions = [
  { label: '未婚', value: '1' },
  { label: '已婚', value: '2' },
  { label: '离异', value: '3' },
  { label: '丧偶', value: '4' }
];

function handleCredNumberChange(value: string) {
  if (value.length === 18) {
    // 从身份证提取出生日期
    const year = value.substring(6, 10);
    const month = value.substring(10, 12);
    const day = value.substring(12, 14);
    patientModal.value.birthday = `${year}-${month}-${day}`;

    // 提取性别
    const gender = Number.parseInt(value.charAt(16), 10) % 2 === 1 ? '1' : '2';
    patientModal.value.gender = gender;
  }
}

function handleUploadFinish(options: { file: UploadFileInfo; event?: Event }) {
  const target = options.event?.target as XMLHttpRequest;
  patientModal.value.attachments = JSON.parse(target.response).data[0];
}

const patientOptions = ref<Api.Common.CommonOption[]>([]);
const patientLoading = ref(false);

async function handlePatientNameSearch(query: string) {
  patientLoading.value = true;
  try {
    const res = await fetchPostPatientList({
      pageNo: 1,
      pageSize: 999,
      name: query,
      hospitalId: patientModal.value.projectHospitalId
    });
    patientOptions.value = res.data?.records.map((item: Api.Patient.PatientBase) => ({
      label: item.name,
      value: item.id
    })) as any;
  } finally {
    patientLoading.value = false;
  }
}

async function handleSubmit() {
  try {
    actionLoading.value = true;
    await validate();
    patientModal.value.projectId = route.params.id as string;
    // 调用保存API
    let res;
    if (props.operateType === 'add') {
      res = await addParticipant(patientModal.value as Api.Participant.ParticipantAddReqVO);
    } else {
      res = await updateParticipant(patientModal.value);
    }
    if (res.response.data?.status === 'OK') {
      window.$message?.success('操作成功');
    }
    emit('submitted');
    visible.value = false;
    resetForm();
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    actionLoading.value = false;
  }
}

function handlePatientInfoTabChange(value: string) {
  if (value === 'newPatient') {
    patientModal.value.patientId = null;
  } else {
    patientModal.value.patient = {
      birthday: null,
      birthplace: '',
      credNumber: '',
      credType: '',
      diagnosisFinal: '',
      diagnosisInit: '',
      diagnosisTime: null,
      familyAddress: '',
      gender: '',
      hospitalId: '',
      maritalStatus: '',
      name: '',
      nation: '',
      nativePlace: '',
      outpatientNumber: '',
      permanentAddress: '',
      phoneNumber: '',
      relativeName: '',
      riskId: '',
      userName: 'admin',
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
    };
  }
}

function resetForm() {
  visible.value = false;
  restoreValidation();
  patientModal.value = createDefaultModel();
}

watch(visible, () => {
  // handlePatientNameSearch('');
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

const hospitalList = ref<string[]>([]);

async function getHospitalOptions() {
  const res: any = await fetchGetExperimentDetail(route.params.id as string);
  if (res) {
    hospitalList.value = res.data.projectHospitalList
      .filter((item: any) => item.id)
      .map((item: any) => item.hospitalId);
  }
}

onMounted(() => {
  getHospitalOptions();
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-800px">
    <NForm
      ref="formRef"
      :model="patientModal"
      :rules="rules"
      label-placement="left"
      :loading="actionLoading"
      label-width="100"
      @close="resetForm"
    >
      <div class="grid grid-cols-3">
        <NFormItem path="number" label="受试者编号">
          <NInput v-model:value="patientModal.number" placeholder="请输入受试者编号" />
        </NFormItem>

        <NFormItem path="projectHospitalId" label="就诊医院">
          <HospitalSelect v-model:value="patientModal.projectHospitalId" :hospital-list="hospitalList" />
        </NFormItem>

        <!--
 <NFormItem path="outpatientNumber" label="门诊号">
          <NInput v-model:value="patientModal.outpatientNumber" placeholder="请输入门诊号" />
        </NFormItem>
-->

        <NFormItem path="attachments" label="附件上传">
          <NUpload
            :action="serviceBaseUrl + '/common/uploadFile'"
            :headers="{
              type: 'multipart/form-data',
              Authentication: userStore.token
            }"
            :max="1"
            @finish="handleUploadFinish"
          >
            <NButton>上传文件</NButton>
          </NUpload>
        </NFormItem>
      </div>
      <NTabs v-model:value="patientInfoTab" type="line" animated @update-value="handlePatientInfoTabChange">
        <NTabPane name="patientInfo" tab="已有患者">
          <h2 class="font-700">患者管理</h2>
          <NFormItem path="patientId" label="患者姓名">
            <NSelect
              v-model:value="patientModal.patientId"
              filterable
              :options="patientOptions"
              :loading="patientLoading"
              placeholder="请输入患者姓名"
              remote
              clearable
              @search="handlePatientNameSearch"
            />
          </NFormItem>
        </NTabPane>
        <NTabPane v-if="props.operateType === 'add'" name="newPatient" tab="新增患者">
          <h2 class="font-700">患者管理</h2>
          <div class="grid grid-cols-3 gap-4">
            <NFormItem path="patient.name" label="患者姓名">
              <NInput v-model:value="patientModal.patient.name" placeholder="请输入患者姓名" />
            </NFormItem>
            <NFormItem path="patient.gender" label="性别">
              <NSelect v-model:value="patientModal.patient.gender" placeholder="请选择性别" :options="genderoptions" />
            </NFormItem>

            <NFormItem path="patient.height" label="身高">
              <NInput v-model:value="patientModal.patient.height" placeholder="请输入身高">
                <template #suffix>cm</template>
              </NInput>
            </NFormItem>

            <NFormItem path="patient.weight" label="体重">
              <NInput v-model:value="patientModal.patient.weight" placeholder="请输入体重">
                <template #suffix>kg</template>
              </NInput>
            </NFormItem>

            <NFormItem path="patient.credNumber" label="身份证号码">
              <NInput
                v-model:value="patientModal.patient.credNumber"
                placeholder="请输入身份证号码"
                maxlength="18"
                @change="handleCredNumberChange"
              />
            </NFormItem>

            <NFormItem path="patient.birthday" label="出生日期">
              <NDatePicker
                v-model:value="patientModal.patient.birthday as any"
                type="date"
                placeholder="请选择出生日期"
                :is-date-disabled="(timestamp: number) => timestamp > Date.now()"
              />
            </NFormItem>

            <NFormItem path="patient.nation" label="民族">
              <SelectNation v-model:value="patientModal.patient.nation" />
            </NFormItem>

            <NFormItem path="patient.birthplace" label="出生地">
              <NInput v-model:value="patientModal.patient.birthplace" placeholder="请输入出生地" />
            </NFormItem>

            <NFormItem path="patient.nativePlace" label="籍贯">
              <NInput v-model:value="patientModal.patient.nativePlace" placeholder="请输入籍贯" />
            </NFormItem>

            <NFormItem path="patient.permanentAddress" label="户口地址">
              <NInput v-model:value="patientModal.patient.permanentAddress" placeholder="请输入户口地址" />
            </NFormItem>

            <NFormItem path="patient.familyAddress" label="现住址">
              <NInput v-model:value="patientModal.patient.familyAddress" placeholder="请输入现住址" />
            </NFormItem>

            <NFormItem path="patient.maritalStatus" label="婚姻状况">
              <NSelect
                v-model:value="patientModal.patient.maritalStatus"
                placeholder="请选择婚姻状况"
                :options="maritalOptions"
              />
            </NFormItem>

            <NFormItem path="patient.relativeName" label="亲属姓名">
              <NInput v-model:value="patientModal.patient.relativeName" placeholder="请输入亲属姓名" />
            </NFormItem>

            <NFormItem path="patient.phoneNumber" label="联系电话">
              <NInput v-model:value="patientModal.patient.phoneNumber" placeholder="请输入联系电话" maxlength="11" />
            </NFormItem>
          </div>
          <h2 class="font-700">诊断信息</h2>
          <div class="grid grid-cols-3 gap-4">
            <NFormItem path="patient.diagnosisTime" label="诊断时间">
              <NDatePicker
                v-model:formatted-value="patientModal.patient.diagnosisTime"
                type="datetime"
                placeholder="请选择诊断时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                :disabled-date="(ts: number) => ts > Date.now()"
              />
            </NFormItem>
            <NFormItem path="patient.diagnosisInit" label="初步诊断">
              <NInput v-model:value="patientModal.patient.diagnosisInit" placeholder="请输入初步诊断" />
            </NFormItem>

            <NFormItem path="patient.diagnosisFinal" label="最终诊断">
              <!-- <NInput v-model:value="patientModal.patient.diagnosisFinal" placeholder="请输入最终诊断" /> -->
              <DiseaseSelect v-model:value="patientModal.patient.diagnosisFinal" />
            </NFormItem>

            <NFormItem path="patient.riskId" label="临床危险度">
              <RiskCascader v-model:value="patientModal.patient.riskId" />
            </NFormItem>
            <NFormItem path="outpatientNumber" label="门诊号">
              <NInput v-model:value="patientModal.outpatientNumber" placeholder="请输入门诊号" />
            </NFormItem>
          </div>
        </NTabPane>
      </NTabs>

      <div class="mt-24px flex justify-end gap-12px">
        <NButton @click="resetForm">取消</NButton>
        <NButton type="primary" :loading="actionLoading" @click="handleSubmit">确定</NButton>
      </div>
    </NForm>
  </NModal>
</template>

<style scoped></style>
