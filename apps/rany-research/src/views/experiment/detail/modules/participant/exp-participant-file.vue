<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NUpload } from 'naive-ui';
import type { UploadFileInfo } from 'naive-ui';
import { useRoute } from 'vue-router';
import { $t } from '@/locales';
import { fetchDeleteFile, fetchGetFileList, fetchPostUploadFile } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
import FileOpenPage from '@/components/common/common-file-show.vue';
import { useUserStore } from '@/store/modules/user';

const appStore = useAppStore();
const route = useRoute();
const props = defineProps<{
  relevanceId: string;
}>();

const userStore = useUserStore();

const { columns, columnChecks, data, getData, loading, mobilePagination } = useTable({
  apiFn: fetchGetFileList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    relevanceType: '0',
    relevanceId: props.relevanceId
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: '文件名称',
      align: 'center',
      width: 200
    },
    {
      key: 'fileSize',
      title: '文件大小',
      align: 'center',
      width: 120,
      render: row => {
        if (!row.fileSize) return '-';
        const size = row.fileSize;
        if (size < 1024) {
          return <span>{size.toFixed(2)}B</span>;
        } else if (size < 1024 * 1024) {
          return <span>{(size / 1024).toFixed(2)}KB</span>;
        } else if (size < 1024 * 1024 * 1024) {
          return <span>{(size / 1024 / 1024).toFixed(2)}MB</span>;
        }
        return <span>{(size / 1024 / 1024 / 1024).toFixed(2)}GB</span>;
      }
    },
    {
      key: 'sourceName',
      title: '试验项目',
      align: 'center',
      width: 200
    },
    {
      key: 'createBy',
      title: '上传人',
      align: 'center',
      width: 200
    },
    {
      key: 'createTime',
      title: '上传时间',
      align: 'center',
      width: 160
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 160,
      render: row => (
        <div class="flex-center justify-end gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleView(row)}>
            查看
          </NButton>
          <NButton type="error" ghost size="small" onClick={() => handleDelete(row)}>
            删除
          </NButton>
        </div>
      )
    }
  ]
});
const serviceBaseUrl = import.meta.env.VITE_SERVICE_BASE_URL;

const showFileVisible = ref(false);
const currentFilePath = ref('');
const currentFileExt = ref('');

function handleView(row: any) {
  // 获取文件扩展名
  const fileExt = row.fileAddress.split('.').pop()?.toLowerCase();
  const filePath = `/filestorage${row.fileAddress}`;

  currentFilePath.value = filePath;
  currentFileExt.value = fileExt;
  showFileVisible.value = true;
}

async function handleDelete(row: any) {
  window.$dialog?.warning({
    title: '提示',
    content: '确定要删除该文件吗?',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await fetchDeleteFile(row.id);
        window.$message?.success('删除成功');
        await getData();
      } catch (error) {
        console.error('删除失败:', error);
      }
    }
  });
}

const fileInfo = ref<Api.Common.UploadFileParams>({
  fileAddress: '',
  name: '',
  fileSize: 0,
  relevanceId: '',
  relevanceType: '',
  sourceId: '',
  userName: 'admin',
  createBy: 'admin',
  userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
});

function handleUploadFinish(options: { file: UploadFileInfo; event?: Event }) {
  const target = options.event?.target as XMLHttpRequest;
  fileInfo.value.fileAddress = JSON.parse(target.response).data[0];
  fileInfo.value.relevanceId = props.relevanceId;
  fileInfo.value.relevanceType = '0';
  fileInfo.value.name = options.file.file?.name;
  fileInfo.value.fileSize = options.file.file?.size;
  fileInfo.value.sourceId = route.params.id as string;
}

function handleAdd() {
  // 上传文件逻辑
  window.$dialog?.create({
    title: '上传文件',
    content: () => {
      return (
        <NUpload
          action={`${serviceBaseUrl}/common/uploadFile?id=${props.relevanceId}&type=p`}
          headers={{
            Authentication: userStore.token
          }}
          max={1}
          onFinish={handleUploadFinish}
        >
          <NButton>上传文件</NButton>
        </NUpload>
      );
    },
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const res = await fetchPostUploadFile(fileInfo.value);
      if (res.response.status === 200) {
        window.$message?.success('上传成功');
        await getData();
      }
    }
  });
}
</script>

<template>
  <div class="flex gap-2 overflow-hidden lt-sm:overflow-auto">
    <div class="flex-col-stretch gap-2 overflow-hidden">
      <TableHeaderOperation
        v-model:columns="columnChecks"
        :loading="loading"
        class="mx-4"
        @refresh="getData"
        @add="handleAdd"
      />
      <NCard :bordered="false" size="small" class="data-common-table h-full card-wrapper">
        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="1200"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="mobilePagination"
          class="h-full"
        />
      </NCard>
      <FileOpenPage
        key="currentFilePath"
        v-model:show="showFileVisible"
        :file-path="currentFilePath"
        :file-ext="currentFileExt"
      />
    </div>
  </div>
</template>

<style scoped></style>
