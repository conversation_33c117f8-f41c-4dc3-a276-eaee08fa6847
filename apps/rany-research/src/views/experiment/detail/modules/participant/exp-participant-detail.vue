<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { getParticipantDetail } from '@/service/api/participant';
import { usePatientStore } from '@/store/modules/patient';
import { useExperimentStore } from '@/store/modules/experiment';
import ExpParticipantDetailBaseInfo from './exp-participant-detail-baseInfo.vue';
import ExpParticipantFile from './exp-participant-file.vue';
import ExpParticipantBag from './exp-participant-bag.vue';

const props = withDefaults(
  defineProps<{
    id: string;
    type?: 'participant' | 'patient';
  }>(),
  { type: 'participant' }
);

const rowData = ref<Api.Participant.Participant | null>(null);

const patientStore = usePatientStore();
const { patientModal } = storeToRefs(patientStore);

const store = useExperimentStore();
const { experimentInfo } = storeToRefs(store);

async function getData() {
  if (props.type === 'participant') {
    const res = await getParticipantDetail(props.id);
    rowData.value = res.data;
  } else {
    await patientStore.fetchGetPatientDetail(props.id);
    rowData.value = patientModal.value as any;
    rowData.value!.patient = patientModal.value as any;
    // TODO: 临时获取登记首页ID，等待后端在患者对象加上绑定的rpaId
    experimentInfo.value = {
      rpaId: 'd58696413f644805bb0730997bf14e31'
    };
  }
}

watch(
  () => props.id,
  newVal => {
    if (newVal) {
      getData();
    }
  },
  { immediate: true }
);

const name = ref('specialBag');

const bagID = computed(() => {
  if (props.type === 'participant') {
    return rowData.value?.patient.sbagId;
  }
  return rowData.value?.sbagId;
});
</script>

<template>
  <div class="h-full flex flex-col gap-2">
    <ExpParticipantDetailBaseInfo :type="props.type" :row-data="rowData" @update-data="getData" />
    <NTabs v-model:value="name" type="card" size="small" class="flex-1">
      <!--
 <NTabPane tab="登记首页" name="register">
        <ExpSettingRegister />
      </NTabPane>
-->
      <NTabPane tab="专科袋" name="specialBag">
        <ExpParticipantBag :id="rowData?.patient?.sbagId" />
      </NTabPane>
      <NTabPane tab="不良事件" name="adverse">
        <div>不良事件内容</div>
      </NTabPane>
      <NTabPane tab="文件" name="files" class="h-full flex">
        <ExpParticipantFile :relevance-id="props.id" />
      </NTabPane>
    </NTabs>
  </div>
</template>

<style scoped></style>
