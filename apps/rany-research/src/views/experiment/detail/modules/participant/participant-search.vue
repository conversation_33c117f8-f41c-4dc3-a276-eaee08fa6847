<script setup lang="ts">
import { ref } from 'vue';
import { $t } from '@/locales';

defineOptions({
  name: 'ExperimentSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const model = defineModel<Api.Participant.ParticipantSearchParams>('model', { required: true });

async function reset() {
  emit('reset');
}

async function search() {
  emit('search');
}

const statusOptions = ref([
  { label: '未操作', value: -1 },
  { label: '出组', value: 0 },
  { label: '入组', value: 1 },
  { label: '推荐入组', value: 2 },
  { label: '推荐出组', value: 3 }
]);
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="experiment-search">
        <NForm :model="model" label-placement="left" :label-width="100">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="受试者编号" path="number" class="pr-16px">
              <NInput v-model:value="model.number" placeholder="请输入受试者编号" clearable />
            </NFormItemGi>

            <NFormItemGi span="24 s:12 m:6" label="就诊医院" path="projectHospitalId" class="pr-16px">
              <HospitalSelect v-model:value="model.projectHospitalId" />
            </NFormItemGi>

            <NFormItemGi span="24 s:12 m:6" label="入组状态" path="status" class="pr-16px">
              <NSelect v-model:value="model.status" :options="statusOptions" placeholder="请选择入组状态" clearable />
            </NFormItemGi>

            <NFormItemGi span="24" class="mt-4">
              <NSpace class="w-full" justify="end" :size="16">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
