<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { computed, onMounted, ref, toRefs, watchEffect } from 'vue';
import { useBagStore } from '@/store/modules/bag';
import { useItemStore } from '@/store/modules/program/item';
import { BagDataSource } from '@/store/modules/bag/BagDataSource';
import AddRestModal from './add-rest-modal.vue';

interface Props {
  // Bag's id
  id?: string;
}
const props = defineProps<Props>();
const { id } = toRefs(props);
const itemStore = useItemStore();

const addRestModalRef = ref();

onMounted(() => {
  itemStore.dataSource = new BagDataSource();
});

const store = useBagStore();
const { loading } = storeToRefs(store);
const { data, load } = store.getPatientBagDetail();

const editMode = ref(false);

watchEffect(() => {
  if (id.value) {
    load(id.value);
  }
});

const restHeaders = ref<Api.Program.Header[]>([
  {
    title: '天数',
    unit: undefined,
    comment: undefined,
    key: 'day'
  },
  {
    title: '用药及剂量',
    unit: undefined,
    comment: undefined
  },
  {
    title: '检验',
    unit: undefined,
    comment: undefined
  },
  {
    title: '其他',
    unit: undefined,
    comment: undefined
  }
]);

const restTableData = (time: number) => {
  return Array.from({ length: time }, (_, index) => ({
    day: index + 1,
    itemCustomMap: {},
    itemActualMap: {}
  }));
};

const anchorItems = computed(() => {
  return data.value?.sbag.stages.map(stage => ({
    title: `${stage.name}`,
    href: `#${stage.id}.stage`,
    children: stage.solutions.map(program => ({
      title: program.soluName,
      href: `#${stage.id}${program.solutionId}.program`
    }))
  }));
});
</script>

<template>
  <div>
    <AddRestModal ref="addRestModalRef" :sbag-id="id" @confirm="load(id!)" />
    <NSkeleton v-if="loading" :height="250" :repeat="6" class="mt-2" />
    <div v-else>
      <NDivider />
      <div class="flex gap-2">
        <NButton v-show="editMode === false" size="small" @click="editMode = true">编辑</NButton>
        <NButton v-show="editMode === true" size="small" @click="editMode = false">取消编辑</NButton>
        <NButton size="small" @click="addRestModalRef.show()">添加休疗</NButton>
      </div>

      <section v-if="data?.sbag.stages?.length" class="flex">
        <CommonAnchor v-if="anchorItems" class="mt-5" :anchor-items="anchorItems" />

        <div class="document-scroll-container flex-1">
          <div v-for="stage in data.sbag.stages" :key="stage.id" class="mb-4">
            <p :id="`${stage.id}.stage`" class="mt-1 color-info-300 font-bold">{{ stage.name }}</p>
            <div
              v-for="program in stage.solutions"
              :id="`${stage.id}${program.solutionId}.program`"
              :key="program.solutionId"
              class="mb-3"
            >
              <p class="mt-1 color-info-300 font-bold">方案名称：{{ program.soluName }}</p>
              <ProgramTable
                v-if="program.soluType === 1"
                :headers="program.soluItems?.headers"
                :edit-mode="editMode"
                :loading="loading"
                :table-data="program.soluItems?.tableData"
                :clinic-id="stage?.sbagId"
                :solu-id="program.solutionId"
                :is-bag="true"
              />
              <ProgramRestTable
                v-if="program.soluType === 2"
                :headers="restHeaders"
                :table-data="restTableData(program.restDayCount ?? 0)"
              />
            </div>
          </div>
        </div>
      </section>
      <NEmpty v-else description="暂无方案数据" />
    </div>
  </div>
</template>
