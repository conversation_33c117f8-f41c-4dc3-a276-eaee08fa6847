<script setup lang="tsx">
import { ref, watch } from 'vue';
import { NButton, NDataTable, NModal } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { fetchPostExperimentHosMemberList, fetchPostExperimentHosMemberRemove } from '@/service/api';

defineOptions({
  name: 'HospitalMembersModel'
});

interface Props {
  /** 医院ID */
  hospitalId: string;
  /** 项目ID */
  projectId: string;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const searchValue = ref('');
const loading = ref(false);
const data = ref<any[]>([]);

const columns = ref<DataTableColumns>([
  {
    title: '姓名',
    key: 'name',
    align: 'center'
  },
  {
    title: '组织',
    key: 'organization',
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    align: 'center',
    render: row => {
      return (
        <NButton type="error" size="small" onClick={() => handleRemove(row)}>
          移除
        </NButton>
      );
    }
  }
]);

async function getData() {
  loading.value = true;
  try {
    const { data: res } = await fetchPostExperimentHosMemberList({
      projectId: props.projectId,
      hospitalId: props.hospitalId,
      pageNo: 1,
      pageSize: 10
    });
    data.value = res?.records || [];
  } finally {
    loading.value = false;
  }
}

async function handleRemove(row: any) {
  window.$dialog?.warning({
    title: '提示',
    content: '确定要移除该成员吗?',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await fetchPostExperimentHosMemberRemove(row.id, props.projectId);
        window.$message?.success('移除成功');
        getData();
      } catch {
        window.$message?.error('移除失败');
      }
    }
  });
}

function closeModal() {
  visible.value = false;
}

watch(visible, newVal => {
  if (newVal) {
    getData();
  }
});

watch(searchValue, () => {
  getData();
});
</script>

<template>
  <NModal v-model:show="visible" title="成员管理" preset="card" style="width: 800px">
    <div class="mb-16px flex-y-center">
      <NInput v-model:value="searchValue" placeholder="请输入关键词搜索" style="width: 200px" />
    </div>

    <NDataTable
      :columns="columns"
      :data="data"
      :loading="loading"
      size="small"
      :scroll-x="800"
      remote
      :row-key="row => row.id"
      class="data-common-table"
    />

    <template #footer>
      <div class="w-full flex justify-end gap-4">
        <NButton @click="closeModal">关闭</NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped></style>
