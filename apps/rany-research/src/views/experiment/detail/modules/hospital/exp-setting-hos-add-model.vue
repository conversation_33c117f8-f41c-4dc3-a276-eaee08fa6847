<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import type { UploadFileInfo } from 'naive-ui';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPostHospitalList, fetchPostProjectHospitalAdd, fetchPutProjectHospitalUpdate } from '@/service/api';
import { useUserStore } from '@/store/modules/user';

defineOptions({
  name: 'HospitalAddModel'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Hospital.Hospital | null;
  /** the experiment id */
  experimentId: string;
}

const props = defineProps<Props>();

const userStore = useUserStore();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const serviceBaseUrl = import.meta.env.VITE_SERVICE_BASE_URL;

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '添加医院',
    edit: '编辑医院'
  };
  return titles[props.operateType];
});

type Model = {
  hospitalId: string;
  estimatedHuman: number;
  attachment: string;
  users: string[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    hospitalId: '',
    estimatedHuman: 0,
    attachment: '',
    users: []
  };
}

type RuleKey = keyof Model;

const rules: Partial<Record<RuleKey, App.Global.FormRule>> = {
  hospitalId: defaultRequiredRule,
  estimatedHuman: defaultRequiredRule
  // attachments 和 users 不是必填项,所以不需要校验规则
};

/** 医院选项 */
const hospitalOptions = ref<CommonType.Option<string>[]>([]);

async function getHospitalListOptions() {
  const { error, data } = await fetchPostHospitalList({
    pageNo: 1,
    pageSize: 1000
  });

  if (!error) {
    const options = data.records.map(item => ({
      label: item.name || '',
      value: item.id || ''
    }));

    hospitalOptions.value = [...options];
  }
}

function handleUploadFinish(options: { file: UploadFileInfo; event?: Event }) {
  const target = options.event?.target as XMLHttpRequest;
  model.attachment = [JSON.parse(target.response).data[0]].join(',');
}

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeModal() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  // 处理users
  const params = JSON.parse(JSON.stringify(model));
  params.users = params.users.map((item: string) => ({
    hospitalId: params.hospitalId,
    userId: item,
    projectId: props.experimentId
  }));

  const submitData: Api.Hospital.ProjectHospitalAddParams = {
    ...params,
    projectId: props.experimentId
  };
  if (props.operateType === 'add') {
    const res = await fetchPostProjectHospitalAdd(submitData);
    if (!res.error) {
      window.$message?.success('添加成功');
      closeModal();
      emit('submitted');
    }
  } else if (props.operateType === 'edit') {
    submitData.id = props.rowData?.id;
    const res = await fetchPutProjectHospitalUpdate(submitData);
    if (!res.error) {
      window.$message?.success('更新成功');
      closeModal();
      emit('submitted');
    }
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
    getHospitalListOptions();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" style="width: 600px">
    <NForm ref="formRef" :model="model" :rules="rules">
      <NFormItem label="医院名称" path="hospitalId">
        <NSelect v-model:value="model.hospitalId" :options="hospitalOptions" placeholder="请选择医院" />
      </NFormItem>
      <NFormItem label="预计受试者" path="estimatedHuman">
        <NInputNumber v-model:value="model.estimatedHuman" :min="0" placeholder="请输入预计受试者数量" />
      </NFormItem>
      <NFormItem label="附件" path="attachment">
        <NUpload
          :action="serviceBaseUrl + '/common/uploadFile'"
          :headers="{
            type: 'multipart/form-data',
            Authentication: userStore.token
          }"
          :max="1"
          @finish="handleUploadFinish"
        >
          <NButton>上传文件</NButton>
        </NUpload>
      </NFormItem>
      <NFormItem label="选择成员" path="users">
        <UsersSelect v-model:value="model.users" :hospital-collection-id="model.hospitalId" />
      </NFormItem>
    </NForm>
    <template #footer>
      <div class="w-full flex justify-end gap-4">
        <NButton @click="closeModal">取消</NButton>
        <NButton type="primary" @click="handleSubmit">确定</NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped></style>
