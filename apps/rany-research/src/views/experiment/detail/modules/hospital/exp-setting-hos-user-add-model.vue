<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NForm, NFormItem, NModal } from 'naive-ui';
import { fetchPostExperimentHosMemberAdd } from '@/service/api';

const props = defineProps<{
  experimentId: string;
  hospitalId?: string;
}>();

defineOptions({
  name: 'HospitalMembersModel'
});

const visible = defineModel<boolean>('visible', {
  default: false
});

const selectedUser = ref([]);
const loading = ref(false);

const emit = defineEmits(['success']);

// 重置表单
function resetForm() {
  selectedUser.value = [];
}

// 关闭弹窗
function closeModal() {
  visible.value = false;
  resetForm();
}

// 表单校验
function validateForm() {
  if (selectedUser.value.length === 0) {
    window.$message?.error('请选择成员');
    return false;
  }
  if (!props.hospitalId || !props.experimentId) {
    window.$message?.error('参数错误');
    return false;
  }
  return true;
}

// 提交处理
async function handleSubmit() {
  if (!validateForm()) return;

  try {
    loading.value = true;
    const params = selectedUser.value.map(item => {
      return {
        hospitalId: props.hospitalId,
        userId: item,
        projectId: props.experimentId
      };
    });
    const { error, response } = await fetchPostExperimentHosMemberAdd(params);

    if (!error && response?.data?.status === 'OK') {
      window.$message?.success('添加成功');
      emit('success');
      closeModal();
    } else {
      window.$message?.error(response?.data?.message || '添加失败');
    }
  } catch (err) {
    console.error('添加成员失败:', err);
    window.$message?.error('添加成员失败,请稍后重试');
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <NModal
    v-model:show="visible"
    title="成员添加"
    preset="card"
    style="width: 800px"
    :mask-closable="false"
    @close="closeModal"
  >
    <NForm>
      <NFormItem label="选择成员" required>
        <UsersSelect v-model:value="selectedUser" :disabled="loading" :hospital-collection-id="hospitalId!" />
      </NFormItem>
    </NForm>
    <template #footer>
      <div class="w-full flex justify-end gap-4">
        <NButton :disabled="loading" @click="closeModal">取消</NButton>
        <NButton type="primary" :loading="loading" :disabled="!selectedUser" @click="handleSubmit">确定</NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped></style>
