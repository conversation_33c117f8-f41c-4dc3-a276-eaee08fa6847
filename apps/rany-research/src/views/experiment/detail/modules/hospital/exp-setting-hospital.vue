<script setup lang="tsx">
import { ref } from 'vue';
import type { Ref } from 'vue';
import { NButton } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchPostProjectHospitalList } from '@/service/api';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { fetchDeleteProjectHospitalRemove } from '@/service/api/hospital';
import { useRouterPush } from '@/hooks/common/router';
import HospitalOperateModal from '@/views/hospital/table/modules/hospital-operate-modal.vue';
import ExpSettingHosAddModel from '../hospital/exp-setting-hos-add-model.vue';
import ExpSettingHosUserAddModel from '../hospital/exp-setting-hos-user-add-model.vue';

export type OperateType = NaiveUI.TableOperateType;

// 使用组合式API管理状态
const { bool: visible, setTrue: openModal } = useBoolean();
const { bool: visibleAddModel, setTrue: openAddModel } = useBoolean();

const props = defineProps<{
  experimentId: string;
}>();

const { routerPushByKey } = useRouterPush();
const appStore = useAppStore();

// 表格相关状态
const operateType = ref<OperateType>('add');
const operateTypeAddModel = ref<OperateType>('add');
const editingData: Ref<Api.Hospital.Hospital | null> = ref(null);
const editingDataAddModel: Ref<Api.Hospital.Hospital | null> = ref(null);

const { columns, columnChecks, data, getData, getDataByPage, loading, mobilePagination } = useTable({
  apiFn: fetchPostProjectHospitalList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    projectId: props.experimentId
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'name',
      title: $t('page.hospital.table.name'),
      align: 'center',
      width: 120,
      render: row => (
        <span
          class="cursor-pointer transition-colors hover:text-primary"
          onClick={() => routerPushByKey('hospital_detail', { params: { id: row.id! } })}
        >
          {row.name}
        </span>
      )
    },
    {
      key: 'codeName',
      title: '医院代号',
      align: 'center',
      width: 120
    },
    {
      key: 'estimatedHuman',
      title: '预计受试者',
      align: 'center',
      width: 120
    },
    {
      key: 'existingNumber',
      title: '已有受试者',
      align: 'center',
      width: 120,
      render: row => (
        <span class="cursor-pointer transition-colors hover:text-primary" onClick={() => handleMembersManage(row)}>
          {row.existingNumber}
        </span>
      )
    },
    {
      key: 'humanNumber',
      title: '已有成员',
      align: 'center',
      width: 100
    },
    {
      key: 'attachment',
      title: '附件',
      align: 'center',
      width: 160,
      render: row => <span>{row.attachment?.split('/')?.pop()}</span>
    },
    {
      key: 'createTime',
      title: $t('page.hospital.table.createTime'),
      align: 'center',
      width: 180
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 230,
      render: row => (
        <div class="flex-center justify-end gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleShowRemoveConfirm(row.id!)}>
            移除
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => handleMembersAdd(row)}>
            添加成员
          </NButton>
        </div>
      )
    }
  ]
});

// 批量操作相关
const { checkedRowKeys } = useTableOperate(data, getData);

// 处理函数
async function handleRemove(id: string) {
  try {
    const res = await fetchDeleteProjectHospitalRemove(id);
    if (res.response.data.status === 'OK') {
      window.$message?.success('移除成功');
      await getData();
    } else {
      window.$message?.error(res.response.data.message || '移除失败');
    }
  } catch (error) {
    console.error('移除医院失败:', error);
    window.$message?.error('移除失败');
  }
}

const { bool: visibleUserAddModel, setTrue: openUserAddModel } = useBoolean();

const hospitalId = ref('');
function handleMembersAdd(row: Api.Hospital.ProjectHospitalAddParams) {
  hospitalId.value = row.id!;
  openUserAddModel();
}

function handleShowRemoveConfirm(id: string) {
  window.$dialog?.warning({
    title: '提示',
    content: '确定要移除该医院吗?',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => handleRemove(id)
  });
}

function handleAdd() {
  operateType.value = 'add';
  openModal();
}

function handleAddHosOfProject() {
  operateTypeAddModel.value = 'add';
  openAddModel();
}

function handleMembersManage(row: Api.Hospital.ProjectHospitalAddParams) {
  console.log('管理成员:', row);
}
</script>

<template>
  <div class="min-h-500px flex gap-16px overflow-hidden lt-sm:overflow-auto">
    <div class="flex-col-stretch gap-16px overflow-hidden">
      <NCard
        :title="$t('page.hospital.table.title')"
        :bordered="false"
        size="small"
        class="data-common-table sm:flex-1-hidden card-wrapper shadow-sm"
      >
        <template #header-extra>
          <div class="flex items-center gap-2">
            <NButton class="mr-10px" size="small" ghost @click="handleAdd">
              <template #icon>
                <icon-ic-round-plus class="text-icon" />
              </template>
              新建
            </NButton>
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @add="handleAddHosOfProject"
              @refresh="getData"
            />
          </div>
        </template>

        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="962"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="mobilePagination"
          class="sm:h-full"
        />

        <HospitalOperateModal
          v-model:visible="visible"
          :operate-type="operateType"
          :row-data="editingData"
          @submitted="getDataByPage"
        />

        <ExpSettingHosAddModel
          v-model:visible="visibleAddModel"
          :operate-type="operateTypeAddModel"
          :row-data="editingDataAddModel"
          :experiment-id="props.experimentId"
          @submitted="getDataByPage"
        />
        <ExpSettingHosUserAddModel
          v-model:visible="visibleUserAddModel"
          :experiment-id="props.experimentId"
          :hospital-id="hospitalId"
          @success="getDataByPage"
        />
      </NCard>
    </div>
  </div>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.card-wrapper:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
