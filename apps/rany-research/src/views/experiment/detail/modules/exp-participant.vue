<script setup lang="tsx">
import { ref } from 'vue';
import type { Ref } from 'vue';
import { NButton, NTag } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { useRoute } from 'vue-router';
import { $t } from '@/locales';
import { fetchPostParticipantList, inGroupParticipant, outGroupParticipant } from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable } from '@/hooks/common/table';
import ExperimentSearch from './participant/participant-search.vue';
import ParticipantOperateModal from './participant/participant-operate-model.vue';
import RandomizeModel from './participant/randomize-model.vue';

export type OperateType = NaiveUI.TableOperateType;

const route = useRoute();

const emit = defineEmits(['update:detail']);

const { bool: visibleParticipant, setTrue: openModalParticipant, setFalse: closeModalParticipant } = useBoolean(false);

const appStore = useAppStore();

const handleDetail = (row: Api.Participant.Participant) => {
  emit('update:detail', row.id);
};

const randomizeRef = ref<InstanceType<typeof RandomizeModel>>();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchPostParticipantList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    number: '',
    projectHospitalId: null,
    status: null,
    projectId: route.params.id as string
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'number',
      title: '受试者编号',
      align: 'center',
      width: 120,
      render: row => (
        <NButton text size="small" onClick={() => handleDetail(row)}>
          {row.number}
        </NButton>
      )
    },
    {
      key: 'projectHospitalId',
      title: '就诊医院',
      align: 'center',
      width: 160,
      render: row => <span>{row.hospitalName}</span>
    },
    {
      key: 'patientId',
      title: '患者姓名',
      align: 'center',
      width: 120,
      render: row => <span>{row.patientName}</span>
    },
    {
      key: 'sex',
      title: '性别',
      align: 'center',
      width: 80,
      render: row => <span>{row.sexName}</span>
    },
    {
      key: 'diagnosisFinal',
      title: '最终诊断',
      align: 'center',
      width: 160,
      render: row => <span>{row.diseaseName}</span>
    },
    {
      key: 'riskId',
      title: '危险度',
      align: 'center',
      width: 100,
      render: row => <span>{row.diseaseRiskName?.name}</span>
    },
    {
      key: 'tranches',
      title: '组别',
      align: 'center',
      width: 100
    },
    {
      key: 'specialBag',
      title: '专科袋',
      align: 'center',
      width: 100
    },
    {
      key: 'status',
      title: '入组状态',
      align: 'center',
      width: 160,
      render: row => <NTag>{row.statusName}</NTag>
    },
    {
      key: 'createBy',
      title: '创建人',
      align: 'center',
      width: 120,
      render: row => <span>{row.createUser}</span>
    },
    {
      key: 'attachment',
      title: '附件',
      align: 'center',
      width: 120
    },
    {
      key: 'createTime',
      title: '创建时间',
      align: 'center',
      width: 160
    },
    {
      key: 'updateTime',
      title: '更新时间',
      align: 'center',
      width: 160
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      width: 200,
      render: row => (
        <div class="flex-center justify-end gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => handleQuit(row)} v-show={row.status === 1}>
            出组
          </NButton>
          <NButton
            type="primary"
            ghost
            size="small"
            onClick={() => handleInGroup(row)}
            v-show={row.status === -1 || row.status === 0}
          >
            入组
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => edit(row)}>
            编辑
          </NButton>
          <NButton
            type="primary"
            ghost
            size="small"
            onClick={() => handleRandomize(row.patientId!, row.id!)}
            disabled={!row.status}
          >
            随机化
          </NButton>
        </div>
      )
    }
  ]
});

const operateType = ref<OperateType>('add');
const editingData: Ref<Api.Participant.Participant | null> = ref(null);

async function handleQuit(row: Api.Participant.Participant) {
  window.$dialog?.warning({
    title: '提示',
    content: '您确定该患者要出组吗?',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const res = await outGroupParticipant({
          id: row.id,
          userName: 'admin',
          userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
        });
        if (res.response.data?.status === 'OK') {
          window.$message?.success('出组成功');
          await getData();
        }
      } catch (error) {
        console.error('出组失败:', error);
      }
    }
  });
}
async function handleInGroup(row: Api.Participant.Participant) {
  try {
    const res = await inGroupParticipant({
      id: row.id,
      userName: 'admin',
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
    });
    if (res.response.data?.status === 'OK') {
      window.$message?.success('入组成功');
      await getData();
    }
  } catch (error) {
    console.error('入组失败:', error);
  }
}

async function handleRandomize(patientId: string, id: string) {
  randomizeRef.value?.show(patientId, id);
}

function handleAdd() {
  operateType.value = 'add';
  editingData.value = null; // 清空编辑数据
  openModalParticipant();
}

function edit(item: Api.Participant.Participant) {
  operateType.value = 'edit';
  editingData.value = { ...item };
  openModalParticipant();
}

function handleSubmitted() {
  closeModalParticipant();
  getDataByPage();
}
</script>

<template>
  <div class="h-full flex-1 flex-grow gap-16px overflow-hidden lt-sm:overflow-auto">
    <div class="h-full flex-col-stretch gap-16px overflow-hidden">
      <ExperimentSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
      <NCard title="受试者列表" :bordered="false" size="small" class="data-common-table sm:flex-1-hidden card-wrapper">
        <template #header-extra>
          <TableHeaderOperation v-model:columns="columnChecks" :loading="loading" @refresh="getData" @add="handleAdd" />
        </template>
        <NDataTable
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="1800"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="mobilePagination"
          class="sm:h-full"
        />
        <ParticipantOperateModal
          v-model:visible="visibleParticipant"
          :operate-type="operateType"
          :row-data="editingData"
          @submitted="handleSubmitted"
        />
        <RandomizeModel ref="randomizeRef" @submitted="getData" />
      </NCard>
    </div>
  </div>
</template>

<style scoped></style>
