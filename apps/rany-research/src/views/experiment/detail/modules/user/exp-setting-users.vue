<script setup lang="tsx">
import { onUnmounted, ref } from 'vue';
import { NButton } from 'naive-ui';
import { useBoolean } from '@sa/hooks';
import { fetchPostExperimentHosMemberList, fetchPostExperimentHosMemberRemove } from '@/service/api';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import ExpSettingHosUserAddModel from '../hospital/exp-setting-hos-user-add-model.vue';

export type OperateType = NaiveUI.TableOperateType;

// 使用组合式API管理状态
const { bool: visible, setTrue: openModal, setFalse: closeModal } = useBoolean();

const props = defineProps<{
  experimentId: string;
}>();

const appStore = useAppStore();

// 表格相关状态
const operateType = ref<OperateType>('add');

// 添加错误处理
const errorHandler = (error: any, message: string) => {
  console.error(message, error);
  window.$message?.error(message);
};

const { columns, columnChecks, data, getData, loading, mobilePagination } = useTable({
  apiFn: fetchPostExperimentHosMemberList,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    projectId: props.experimentId
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'nickName',
      title: '姓名',
      align: 'center',
      width: 100
    },
    {
      key: 'hospitalName',
      title: '医院名称',
      align: 'center',
      width: 120
    },
    {
      key: 'wardId',
      title: '工号',
      align: 'center',
      width: 100
    },
    {
      key: 'sex',
      title: '性别',
      align: 'center',
      width: 80,
      render: row => {
        if (row.sex === 1) return '男';
        if (row.sex === 0) return '女';
        if (row.sex === 8) return '未说明';
        if (row.sex === 9) return '未知';
        return '未知';
      }
    },
    {
      key: 'personTypes',
      title: '人员类别',
      align: 'center',
      width: 100
    },
    {
      key: 'roleId',
      title: '角色',
      align: 'center',
      width: 100,
      render: row => row.roleId || '-'
    },
    {
      key: 'deptName',
      title: '组织',
      align: 'center',
      width: 120,
      render: row => row.deptName || '-'
    },
    {
      key: 'mobile',
      title: '手机',
      align: 'center',
      width: 120,
      render: row => row.mobile || '-'
    },
    {
      key: 'email',
      title: '邮箱',
      align: 'center',
      width: 160,
      render: row => row.email || '-'
    },
    {
      key: 'createTime',
      title: '创建时间',
      align: 'center',
      width: 180
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      width: 100,
      render: row => (row.status === '1' ? '启用' : '禁用')
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 100,
      render: row => (
        <div class="flex-center justify-end gap-8px">
          <NButton
            type="primary"
            ghost
            size="small"
            disabled={loading.value}
            onClick={() => handleShowRemoveConfirm(row.id!)}
          >
            移除
          </NButton>
        </div>
      )
    }
  ]
});

// 批量操作相关
const { checkedRowKeys } = useTableOperate(data, getData);

// 处理函数
async function handleRemove(id: string) {
  if (!id || !props.experimentId) {
    window.$message?.error('参数错误');
    return;
  }

  try {
    const res = await fetchPostExperimentHosMemberRemove(id, props.experimentId);
    if (res?.response?.data?.status === 'OK') {
      window.$message?.success('移除成功');
      await getData();
    } else {
      window.$message?.error(res?.response?.data?.message || '移除失败');
    }
  } catch (error) {
    errorHandler(error, '移除用户失败');
  }
}

function handleShowRemoveConfirm(id: string) {
  if (!id) {
    window.$message?.error('用户ID不能为空');
    return;
  }

  window.$dialog?.warning({
    title: '提示',
    content: '确定要移除该用户吗?',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => handleRemove(id)
  });
}

function handleAdd() {
  operateType.value = 'add';
  openModal();
}

// 组件卸载时清理状态
onUnmounted(() => {
  closeModal();
  operateType.value = 'add';
});
</script>

<template>
  <div class="min-h-500px flex gap-16px overflow-hidden lt-sm:overflow-auto">
    <div class="flex-col-stretch gap-16px overflow-hidden">
      <NCard
        title="成员列表"
        :bordered="false"
        size="small"
        class="data-common-table sm:flex-1-hidden card-wrapper shadow-sm"
      >
        <template #header-extra>
          <div class="flex items-center gap-2">
            <NButton class="mr-10px" size="small" ghost :disabled="loading" @click="handleAdd">
              <template #icon>
                <icon-ic-round-plus class="text-icon" />
              </template>
              添加成员
            </NButton>
            <TableHeaderOperation
              v-model:columns="columnChecks"
              :disabled-delete="checkedRowKeys.length === 0"
              :loading="loading"
              @refresh="getData"
            />
          </div>
        </template>

        <NDataTable
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="data"
          size="small"
          :flex-height="!appStore.isMobile"
          :scroll-x="1500"
          :loading="loading"
          remote
          :row-key="row => row.id"
          :pagination="mobilePagination"
          class="sm:h-full"
        />
        <ExpSettingHosUserAddModel v-model:visible="visible" :experiment-id="props.experimentId" />
      </NCard>
    </div>
  </div>
</template>

<style scoped>
.card-wrapper {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.card-wrapper:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
