<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import { usePlanStore } from '@/store/modules/plan';
const emit = defineEmits<{
  (e: 'confirm'): void;
}>();
const showModal = ref(false);

const store = usePlanStore();
const { loading } = storeToRefs(store);

const handleCancel = () => {
  showModal.value = false;
};

const handleConfirm = async () => {
  await store.linkClinicPlanToProject();
  handleCancel();
  emit('confirm');
};

defineExpose({
  show: () => {
    showModal.value = true;
  }
});
</script>

<template>
  <NModal v-model:show="showModal" title="添加诊疗计划" class="w-2xl" size="small" preset="card" @close="handleCancel">
    <PlanSelect v-model:value="store.formModel.id" placeholder="输入" class="p-4" />
    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" :disabled="loading" @click="handleCancel">取消</NButton>
      <NButton type="primary" :loading="loading" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>

<style scoped>
/* Add any necessary styles here */
</style>
