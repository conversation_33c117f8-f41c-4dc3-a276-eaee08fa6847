<script setup lang="tsx">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { NButton, useDialog } from 'naive-ui';
import { $t } from '@/locales';
import { fetchGetClinicPlanByProject } from '@/service/api/clinicPlan';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { tableColumnArrayStringRender } from '@/utils/table';
import { usePlanStore } from '@/store/modules/plan';
import ExpSettingPlanAddModal from './exp-setting-plan-add-modal.vue';
import ExpSettingPlanSearch from './exp-setting-plan-search.vue';

const route = useRoute();
const projectId = computed(() => route.params.id as string);
const store = usePlanStore();
const dialog = useDialog();
const addModalRef = ref();

const {
  columns,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  columnChecks,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetClinicPlanByProject,
  showTotal: false,
  apiParams: {
    pageNo: 1,
    pageSize: 10,
    projectId: projectId.value,
    clinicPlanName: ''
  },
  columns: () => [
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 48
    },
    {
      key: 'name',
      title: '诊疗计划名称',
      align: 'center',
      width: 260
    },
    {
      key: 'tranches',
      title: '组别',
      align: 'center',
      width: 100
    },
    {
      key: 'weekCount',
      title: '计划周数',
      align: 'center'
    },
    {
      key: 'riskName',
      title: '危险度分型',
      align: 'center',
      render: tableColumnArrayStringRender('riskName')
    },
    {
      title: '疾病分型',
      key: 'diseaseName'
    },
    {
      title: '干预措施',
      key: 'intervention'
    },

    {
      key: 'updateTime',
      title: $t('page.database.disease.updateTime'),
      align: 'center',
      width: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 160,
      render: row => (
        <div class="flex-center gap-8px">
          {/* <NButton type="primary" ghost size="small" onClick={() => handleEdit(row.id)}>
            {$t('common.edit')}
          </NButton> */}
          <NButton type="error" ghost size="small" onClick={() => handleDelete(row.id)}>
            {$t('common.delete')}
          </NButton>
        </div>
      )
    }
  ]
});

const { checkedRowKeys } = useTableOperate(data, getData);

// function handleEdit(id?: string) {
//   if (id) {
//     store.formModel.id = id;
//     store.getDetail(id).then(() => {
//       addModalRef.value.show();
//     });
//   }
// }

function handleDelete(id?: string) {
  if (id) {
    dialog.warning({
      title: '删除',
      content: () => (
        <div class="text-center">
          <h1 class="font-700">您确认要删除吗?</h1>
          <h1 class="mt-1 text-xs">删除后记录不可查询！</h1>
        </div>
      ),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        await store.deletePlan(id);
        getData();
      }
    });
  }
}

function handleAdd() {
  store.formModel.projectId = projectId.value;
  store.$reset();
  addModalRef.value.show();
}

function handleBatchDelete() {
  if (checkedRowKeys.value.length) {
    dialog.warning({
      title: '批量删除',
      content: () => (
        <div class="text-center">
          <h1 class="font-700">您确认要删除选中的项目吗?</h1>
          <h1 class="mt-1 text-xs">删除后记录不可查询！</h1>
        </div>
      ),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        await Promise.all(checkedRowKeys.value.map(id => store.deletePlan(id as string)));
        getData();
      }
    });
  }
}
</script>

<template>
  <div class="data-common-table">
    <ExpSettingPlanAddModal ref="addModalRef" @confirm="getData" />
    <ExpSettingPlanSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <TableHeaderOperation
      v-model:columns="columnChecks"
      :disabled-delete="checkedRowKeys.length === 0"
      :loading="loading"
      @add="handleAdd"
      @delete="handleBatchDelete"
      @refresh="getData"
    />

    <NDataTable :columns="columns" :data="data" size="small" :loading="loading" :pagination="mobilePagination" />
  </div>
</template>

<style scoped></style>
