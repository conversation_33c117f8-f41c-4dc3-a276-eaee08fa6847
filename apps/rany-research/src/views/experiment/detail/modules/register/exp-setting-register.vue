<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { fetchGetRegisterPageDetail } from '@/service/api';
import JsonView from '@/views/data-base/register/detail/modules/register-detail-json.vue';
import RegisterDetailFormBlitzar from '@/views/data-base/register/detail/modules/register-detail-form-blitzar.vue';
import { useExperimentStore } from '@/store/modules/experiment';
import ExpSettingRegisterFile from '../register/exp-setting-register-file.vue';

const store = useExperimentStore();
const { experimentInfo } = storeToRefs(store);

const jsonData = ref<any>({});
const jsonHeight = ref<number>(0);

const updateHeight = () => {
  const divElement = document.getElementById('jsonForm1');
  if (!divElement) return;
  jsonHeight.value = divElement?.getBoundingClientRect().height;
};

const registerInfo = ref<any>({});

async function getRegisterInfo(rpaId?: string) {
  if (!rpaId) return;
  try {
    const res = await fetchGetRegisterPageDetail(rpaId);
    registerInfo.value = res.data || {};
    jsonData.value = JSON.parse(String(res.data?.template));
  } catch (error) {
    console.log(error);
    window.$message?.error('获取登记信息失败');
    jsonData.value = {};
  }
}

// 监听实验信息中的 rpaId 变化
watch(
  () => experimentInfo.value?.rpaId,
  newRpaId => {
    if (newRpaId) {
      getRegisterInfo(newRpaId);
      updateHeight();
    } else {
      registerInfo.value = {};
    }
  },
  { immediate: true }
);

onMounted(async () => {
  setTimeout(() => {
    updateHeight();
  }, 1000);
  window.addEventListener('resize', updateHeight);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateHeight);
});
</script>

<template>
  <div>
    <NCard :bordered="false" size="small" class="mb-2 h-full overflow-hidden card-wrapper">
      <ExpSettingRegisterFile :register-info="registerInfo" @update:register-info="getRegisterInfo($event)" />
    </NCard>
    <div id="jsonForm1" class="flex gap-2">
      <NCard :bordered="false" size="small" class="mr-2 flex-[3] card-wrapper">
        <RegisterDetailFormBlitzar v-if="jsonData" :json-data="jsonData" />
        <NResult v-else class="h-" status="418" title="暂无数据" description="请先上传文件"></NResult>
      </NCard>
      <NCard :bordered="false" size="small" class="flex-[1] overflow-hidden card-wrapper">
        <JsonView
          v-model:json-data="jsonData"
          :json-height="jsonHeight"
          :register-id="registerInfo.id!"
          @update:data="getRegisterInfo"
        />
      </NCard>
    </div>
  </div>
</template>

<style scoped></style>
