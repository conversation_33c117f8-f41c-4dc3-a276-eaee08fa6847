<script setup lang="ts">
import { reactive, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useExperimentStore } from '@/store/modules/experiment';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { fetchPostExperimentEdit } from '@/service/api/experiment';
import RegisterOperateModal from '@/views/data-base/register/table/modules/register-operate-modal.vue';

// store相关
const store = useExperimentStore();
const { experimentInfo } = storeToRefs(store);

// 表单相关
const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

// 类型定义
export type OperateType = NaiveUI.TableOperateType;
type Model = {
  rpaId: string;
};
type RuleKey = keyof Model;

// props & emits
const props = defineProps<{
  registerInfo: Api.Register.Register;
}>();

const emit = defineEmits<{
  (e: 'update:registerInfo', id: string): void;
}>();

// 状态管理
const showSelectModal = ref(false);
const visible = ref(false);
const operateType = ref<OperateType>('add');
const editingData = ref<Api.Register.Register>({});
const checkoutRef = ref();
const model = reactive<Model>({
  rpaId: ''
});

// 表单校验规则
const rules: Partial<Record<RuleKey, App.Global.FormRule>> = {
  rpaId: defaultRequiredRule
};

// 处理提交
async function handleSubmit() {
  try {
    await validate();

    if (!experimentInfo.value?.id) {
      window.$message?.error('试验ID不能为空');
      return;
    }

    const res = await fetchPostExperimentEdit({
      id: experimentInfo.value.id,
      rpaId: model.rpaId,
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      userName: 'admin'
    });

    if (res.response.data.status === 'OK') {
      window.$message?.success('保存成功');
      await store.getExperimentDetail(experimentInfo.value.id);
      emit('update:registerInfo', model.rpaId);
      closeModal();
    } else {
      window.$message?.error(res.response.data.message);
    }
  } catch (error) {
    console.error('提交失败:', error);
    window.$message?.error('提交失败');
  }
}

// 打开选择弹窗
function handleSelect() {
  showSelectModal.value = true;
}

// 打开新建弹窗
function handleCreate() {
  operateType.value = 'add';
  visible.value = true;
}

// 关闭选择弹窗
function closeModal() {
  showSelectModal.value = false;
  restoreValidation();
}

// 刷新列表
function getDataByPage() {
  checkoutRef.value.reload();
}
</script>

<template>
  <div>
    <div class="flex items-center justify-between gap-4">
      <div class="flex items-center gap-4">
        <span>{{ props.registerInfo.name || 'xxx登记首页（未选择）' }}</span>
        <NButton type="primary" size="small" @click="handleSelect">选择</NButton>
        <NButton size="small" @click="handleCreate">新建</NButton>
      </div>
      <div class="flex items-center gap-2">
        <span class="text-gray-500">创建时间：</span>
        <span>{{ props.registerInfo.createTime }}</span>
        <span class="text-gray-500">更新时间：</span>
        <span>{{ props.registerInfo.updateTime }}</span>
      </div>
    </div>
    <NModal v-model:show="showSelectModal" preset="card" style="width: 600px" title="选择">
      <!-- 登记表选择内容 -->
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="登记首页" path="rpaId">
          <CheckoutSelect ref="checkoutRef" v-model:model-value="model.rpaId" />
        </NFormItem>
      </NForm>
      <template #footer>
        <div class="w-full flex justify-end gap-4">
          <NButton @click="closeModal">取消</NButton>
          <NButton type="primary" @click="handleSubmit">确定</NButton>
        </div>
      </template>
    </NModal>
    <RegisterOperateModal
      v-model:visible="visible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="getDataByPage"
    />
  </div>
</template>

<style scoped></style>
