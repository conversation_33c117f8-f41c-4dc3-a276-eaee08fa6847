<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { NButton, NSpace } from 'naive-ui';
import { useRoute } from 'vue-router';
import { useExperimentStore } from '@/store/modules/experiment';
import { fetchPostExperimentStart, fetchPostExperimentStop } from '@/service/api';
import ExperimentOperateModal from '../../table/modules/exp-table-operate-model.vue';

const store = useExperimentStore();

// 获取路由参数中的试验ID
const experimentId = computed(() => useRoute().params.id as string);

export type OperateType = NaiveUI.TableOperateType;

// 状态管理
const loading = ref(false);
const visible = ref(false);
const operateType = ref<OperateType>('edit');
const editingData = ref<Api.Experiment.ExperimentDetail | null>(null);

const { experimentInfo } = storeToRefs(store);

// 获取试验详情
async function getInfo() {
  if (!experimentId.value) {
    window.$message?.error('试验ID不能为空');
    return;
  }

  loading.value = true;
  store.getExperimentDetail(experimentId.value);
  loading.value = false;
}

// 处理试验状态变更
async function handleExperimentStatusChange(type: number) {
  if (!experimentId.value) {
    window.$message?.error('试验ID不能为空');
    return;
  }

  const isStop = type === 1;
  const actionText = isStop ? '终止' : '启动';

  try {
    loading.value = true;
    const api = isStop ? fetchPostExperimentStop : fetchPostExperimentStart;
    await api({
      id: experimentId.value,
      userName: 'admin',
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
    });
    window.$message?.success(`${actionText}成功`);
    await getInfo();
  } catch (error) {
    window.$message?.error(`${actionText}失败: ${error}`);
  } finally {
    loading.value = false;
  }
}

// 处理编辑操作
function handleEdit() {
  if (!experimentInfo) {
    window.$message?.error('试验信息不能为空');
    return;
  }
  operateType.value = 'edit';
  editingData.value = { ...experimentInfo.value };
  visible.value = true;
}

// 处理提交完成
async function handleSubmitted() {
  visible.value = false;
  await getInfo();
}

// 监听试验ID变化,重新获取详情
watch(
  experimentId,
  newId => {
    if (newId) {
      getInfo();
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="h-full">
    <div class="flex gap-24px">
      <!-- 左侧内容 -->
      <div class="flex-[1]">
        <div class="mb-6 flex items-center gap-2">
          <div class="w-90px flex">
            <NProgress type="circle">{{ experimentInfo?.userSamplePercentage ?? 0 }}</NProgress>
          </div>
          <div class="w-80px flex flex-col items-center text-center">
            <div class="text-16px">{{ experimentInfo?.useSampleCount ?? 0 }}</div>
            <div>样本量</div>
          </div>

          <div class="w-90px flex">
            <NProgress type="circle">{{ experimentInfo?.useHospitalPercentage ?? 0 }}</NProgress>
          </div>
          <div class="w-80px flex flex-col items-center text-center">
            <div class="text-16px">{{ experimentInfo?.useHospitalCount ?? 0 }}</div>
            <div>医院数</div>
          </div>
        </div>
        <!--
 <div class="flex">
          <NStatistic class="flex-1" label="不良事件" :value="12" />
          <NStatistic class="flex-1" label="严重不良事件" :value="3" />
        </div>
-->
      </div>

      <!-- 右侧内容 -->
      <div class="flex-[3]">
        <div class="mb-16px flex items-center justify-between gap-16px">
          <div class="flex items-center gap-16px">
            <span class="text-16px font-bold">{{ experimentInfo?.name }}</span>
            <NTag :type="experimentInfo?.status === 1 ? 'success' : 'error'">
              {{ experimentInfo?.status === 1 ? '进行中' : '停用' }}
            </NTag>
          </div>
          <div class="flex gap-16px">
            <NSpace>
              <NButton type="primary" size="small" :loading="loading" :disabled="!experimentInfo" @click="handleEdit">
                编辑
              </NButton>
              <NButton
                size="small"
                :type="experimentInfo?.status === 1 ? 'error' : 'primary'"
                :loading="loading"
                :disabled="!experimentInfo"
                @click="handleExperimentStatusChange(experimentInfo?.status ?? 0)"
              >
                {{ experimentInfo?.status === 1 ? '终止' : '启动' }}
              </NButton>
            </NSpace>
          </div>
        </div>

        <div class="mb-16px flex items-center gap-16">
          <NText>简称: {{ experimentInfo?.shortName || '-' }}</NText>
          <NText>注册号: {{ experimentInfo?.registNumber || '-' }}</NText>
          <NText>研究疾病: {{ experimentInfo?.diseaseName || '-' }}</NText>
        </div>

        <div class="mb-16px flex items-center gap-16">
          <NText>研究阶段: {{ experimentInfo?.stageName || '-' }}</NText>
          <NText>研究类型: {{ experimentInfo?.typeName || '-' }}</NText>
          <NText>研究设计: {{ experimentInfo?.design || '-' }}</NText>
          <NText>盲法: {{ experimentInfo?.blindMethod || '-' }}</NText>
        </div>

        <div class="flex items-center gap-16">
          <NText>研究实施时间: {{ experimentInfo?.startTime || '-' }} - {{ experimentInfo?.endTime || '-' }}</NText>
          <NText>预计样本量: {{ experimentInfo?.estimatedHuman || '-' }}</NText>
          <NText>预计医院数: {{ experimentInfo?.estimatedHosp || '-' }}</NText>
        </div>
      </div>
    </div>
    <NDivider />
    <div class="mb-16px text-16px font-bold">研究目的</div>
    <NText>{{ experimentInfo?.purpose || '-' }}</NText>
    <NDivider />
    <div class="mb-16px text-16px font-bold">入组条件</div>
    <NText>{{ experimentInfo?.inCondition || '-' }}</NText>
    <NDivider />
    <div class="mb-16px text-16px font-bold">排除条件</div>
    <NText>{{ experimentInfo?.exclCondition || '-' }}</NText>
    <NDivider />
    <div class="mb-16px text-16px font-bold">出组条件</div>
    <NText>{{ experimentInfo?.outCondition || '-' }}</NText>
    <ExperimentOperateModal
      v-model:visible="visible"
      :operate-type="operateType"
      :row-data="editingData"
      @submitted="handleSubmitted"
    />
  </div>
</template>

<style scoped>
:deep(.n-statistic) {
  display: flex;
  align-items: center;
}
:deep(.n-statistic .n-statistic__label) {
  font-size: 16px;
  font-weight: 400;
  margin-right: 8px;
}
:deep(.n-statistic .n-statistic-value .n-statistic-value__content) {
  font-size: 18px;
}
:deep(.n-statistic .n-statistic-value) {
  margin-top: 0px;
}
</style>
