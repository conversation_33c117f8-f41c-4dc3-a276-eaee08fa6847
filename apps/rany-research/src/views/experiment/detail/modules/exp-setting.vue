<script setup lang="ts">
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import ExpSettingRisk from './risk/exp-setting-risk.vue';
import ExpSettingRegister from './register/exp-setting-register.vue';
import ExpSettingHospital from './hospital/exp-setting-hospital.vue';
import ExpSettingUsers from './user/exp-setting-users.vue';
import ExpSettingPlan from './plan/exp-setting-plan.vue';

const experimentId = useRoute().params.id as string;

const name = ref('risk');
</script>

<template>
  <NTabs v-model:value="name" type="card" size="small" class="h-full">
    <NTabPane tab="危险度设置" name="risk">
      <ExpSettingRisk :experiment-id="experimentId" />
    </NTabPane>
    <NTabPane tab="诊疗计划" name="treatment">
      <ExpSettingPlan />
    </NTabPane>
    <NTabPane tab="登记首页" name="register">
      <ExpSettingRegister />
    </NTabPane>
    <NTabPane tab="不良事件" name="adverse">
      <div>不良事件内容</div>
    </NTabPane>
    <NTabPane tab="医院" name="hospital">
      <ExpSettingHospital :experiment-id="experimentId" />
    </NTabPane>
    <NTabPane tab="成员" name="member">
      <ExpSettingUsers :experiment-id="experimentId" />
    </NTabPane>
  </NTabs>
</template>

<style scoped></style>
