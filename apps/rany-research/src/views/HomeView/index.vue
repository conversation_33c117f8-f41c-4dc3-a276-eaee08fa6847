<script setup lang="ts">
import { AiChatHomeView, CreateAiChat, useAiChatStore } from '@rany/component-ai';
import { useRouter } from 'vue-router';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useQuickActionsStore } from 'component-library';
import emitter from '@/utils/mitt';

const quickActionsStore = useQuickActionsStore();
const router = useRouter();
const aiChatStore = useAiChatStore();
const aiChatHomeViewRef = ref<InstanceType<typeof AiChatHomeView> | null>(null);
const createAiChatRef = ref<InstanceType<typeof CreateAiChat> | null>(null);

const showTopBtn = computed(() => {
  return router.currentRoute.value.path === '/ai/home';
});

// 检查并触发scrollToNextPage方法
const checkAndScrollToNext = async () => {
  const hasPlatform = Boolean(router.currentRoute.value.params.id);
  if (aiChatHomeViewRef.value) {
    if (hasPlatform) {
      // 有参数调用scrollToNextPage
      aiChatHomeViewRef.value.scrollToNextPage();
    } else {
      // 无参数调用scrollToHome
      aiChatHomeViewRef.value.scrollToHome();
    }
  } else {
    // 如果组件还未挂载，等待100ms后再次尝试
    setTimeout(checkAndScrollToNext, 100);
  }
};

// 监听路由参数变化
watch(
  () => router.currentRoute.value.query,
  () => {
    checkAndScrollToNext();
  },
  { deep: true, immediate: true }
);

// 确保在组件挂载后检查
onMounted(() => {
  checkAndScrollToNext();
  emitter.on('open-ai-drawer', () => {
    createAiChatRef.value?.toggleDrawer();
  });
});

onUnmounted(() => {
  emitter.off('open-ai-drawer');
});

// 处理侧边栏打开事件
const openSideBar = () => {
  router.push('/home/<USER>');
  // 这里需要新增一个tab标签
};

// 处理开始QA事件
const startQA = async (message: { content: string; files: any[] }) => {
  // 使用store发送消息
  router.push('/aiChat/QA');
  await aiChatStore.sendMessage(message);
  if (aiChatStore.currentMessageId) {
    router.replace(`/aiChat/QA/${aiChatStore.currentMessageId}`);
  }
};

// 处理工具点击事件
const handleToolClick = (tool: any) => {
  // 也许我们需要执行一些操作，例如导航或者打开一个特定功能
  if (tool && tool.path) {
    // 重新打开一个页面
    window.open(`${window.location.origin}${tool.path}`, '_blank');
  }
};

// 处理页面滚动事件
const scrollPageActive = (pageIndex: number) => {
  if (pageIndex === 1) {
    quickActionsStore.setActionStatus('platform', true);
    quickActionsStore.setActionStatus('home', false);
  } else {
    quickActionsStore.setActionStatus('home', true);
    quickActionsStore.setActionStatus('platform', false);
  }
};

// 处理导航点击事件
const handleNavClick = (item: any) => {
  router.push(item.path);
};
</script>

<template>
  <div class="relative h-full w-full">
    <div class="absolute left-[0rem] top-[0rem] z-[3000]">
      <CreateAiChat ref="createAiChatRef" :show-top-btn="showTopBtn" @open-side-bar="openSideBar" />
    </div>
    <AiChatHomeView
      ref="aiChatHomeViewRef"
      :show-top-btn="showTopBtn"
      @open-side-bar="openSideBar"
      @start-qa="startQA"
      @handle-tool-click="handleToolClick"
      @handle-nav-click="handleNavClick"
      @scroll-page-active="scrollPageActive"
    />
  </div>
</template>

<style scoped>
/* 确保CreateAiChat组件正常显示 */
:deep(.transparent-drawer) {
  background-color: rgba(255, 255, 255, 1);
}
</style>
