<script setup lang="ts">
import { useRouter } from 'vue-router';
// Import from the package entry point instead of src
import { AiChatHistoryManager, AiChatQAView, CreateAiChat, useAiChatStore } from '@rany/component-ai';
import emitter from '@/utils/mitt';

const router = useRouter();
const aiChatStore = useAiChatStore();
const createAiChatRef = ref<InstanceType<typeof CreateAiChat> | null>(null);

// --- Event Handlers ---

const handleClose = () => {
  // Implement closing logic specific to easy-emr, e.g., navigate back
  if (router.getRoutes().length > 1) {
    router.go(-1);
  } else {
    // Handle case where there's no history, e.g., navigate to a default page
    router.push({ name: 'HomePage' }); // Assuming a named route 'HomePage' exists
  }
};

const handleToggleMenu = () => {
  // Implement menu toggle logic if needed in easy-emr
  console.log('Toggle menu action triggered');
  // Example: aiChatStore.toggleSidebar(); or emit event upwards
};

const handleToggleScale = () => {
  // Implement scale toggle logic if needed
  console.log('Toggle scale action triggered');
};
onMounted(() => {
  emitter.on('open-ai-drawer', () => {
    createAiChatRef.value?.toggleDrawer();
  });
});
onUnmounted(() => {
  emitter.off('open-ai-drawer');
});
</script>

<template>
  <div class="relative h-full w-full">
    <div class="absolute left-[1rem] top-[2rem] z-[3000]">
      <CreateAiChat ref="createAiChatRef" :show-top-btn="false" :show-header="true" />
    </div>
    <AiChatQAView
      :messages="aiChatStore.messages"
      :show-stop-btn="aiChatStore.showStopBtn"
      :answer-type="aiChatStore.answerType"
      :chat-records="aiChatStore.chatRecords"
      :history-manager-visible="aiChatStore.historyManagerVisible"
      :show-creation-area="aiChatStore.showAiHeadPlusBtn"
      doctor-name="小然医生"
      header-title="AI 对话"
      @send-message="aiChatStore.sendMessage"
      @retry="aiChatStore.retryMessage"
      @stop-generate="aiChatStore.stopGenerate"
      @update-records="aiChatStore.updateChatRecords"
      @update:history-manager-visible="(visible: boolean) => (aiChatStore.historyManagerVisible = visible)"
      @close="handleClose"
      @toggle-menu="handleToggleMenu"
      @toggle-scale="handleToggleScale"
    >
      <!-- Provide slots content if needed, or rely on defaults -->
      <template #creationArea>
        <!-- Example: Add a button to start a new chat -->
        <button @click="aiChatStore.newChat">开始新对话</button>
      </template>

      <template #historyManager="{ visible, records, updateRecords, updateVisibility }">
        <!-- Use the default history manager or provide a custom one -->
        <AiChatHistoryManager
          v-if="records && records.length > 0"
          :model-value="visible"
          :chat-records="records"
          @update:model-value="updateVisibility"
          @update-records="updateRecords"
        />
        <div v-else-if="visible">无历史记录</div>
      </template>

      <!-- Add other slots like #headerLeft, #mindMapArea etc. if customization is needed -->
    </AiChatQAView>
  </div>
</template>

<style scoped></style>
