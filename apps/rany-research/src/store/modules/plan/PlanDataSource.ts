import {
  fetchGetProgramCustomItemDelete,
  fetchGetProgramDetailByClinic,
  fetchGetProgramItemDelete,
  fetchGetProgramItemDetail,
  fetchPostProgramCustomItemAdd,
  fetchPostProgramCustomItemUpdate,
  fetchPostProgramItemAdd,
  fetchPostProgramItemUpdate,
  fetchPostProgramRowItemDelete
} from '@/service/api/program';
import type { request } from '@/service/request';
import type { DataSourceStrategy } from '../DataSourceStrategy';
import { useItemStore } from '../program/item';
import { usePlatformStore } from '../platform';

export class PlanDataSource implements DataSourceStrategy {
  itemStore = useItemStore();
  platformStore = usePlatformStore();
  detailItem(id: string): ReturnType<typeof request<Api.ProgramItem.ProgramItemRecord>> {
    return fetchGetProgramItemDetail(id);
  }
  addItem(data: Api.ProgramItem.ProgramItemRecord): ReturnType<typeof request<string | null>> {
    return fetchPostProgramItemAdd(data);
  }
  updateItem(data: Api.ProgramItem.ProgramItemRecord): ReturnType<typeof request<string | null>> {
    return fetchPostProgramItemUpdate(data);
  }
  deleteItem(id: string): ReturnType<typeof request<string | null>> {
    return fetchGetProgramItemDelete(id);
  }
  deleteRowItem(rowNum: number, solutionId: string): ReturnType<typeof request<string | null>> {
    return fetchPostProgramRowItemDelete(rowNum, solutionId);
  }
  addCustomItem(data: Api.ProgramItem.AddCustomItemVO): ReturnType<typeof request<string | null>> {
    return fetchPostProgramCustomItemAdd(data);
  }
  deleteCustomItem(id: string): ReturnType<typeof request<string | null>> {
    return fetchGetProgramCustomItemDelete(id);
  }
  updateCustomItem(data: Api.ProgramItem.AddCustomItemVO): ReturnType<typeof request<string | null>> {
    return fetchPostProgramCustomItemUpdate(data);
  }

  transformData(data: Api.Program.ItemResponseData) {
    if (!data) return Promise.resolve([]);
    return Promise.all(
      data.tableData.map(async ov => {
        return Object.entries(ov).reduce(
          async (accPromise, [key, value]) => {
            const acc = await accPromise;
            if (typeof value === 'boolean') {
              if (value) {
                let mapItem = data.itemMap[key];
                const selfItem = ov.itemCustomMap[key];
                if (selfItem) {
                  mapItem = selfItem;
                }
                // 药品值填充
                if (mapItem.type === this.itemStore.Type.medicine) {
                  let frequency = await this.platformStore.getLabelByKey(
                    this.platformStore.dictKeys.Frequency,
                    mapItem.frequency
                  );
                  if (frequency?.toString().includes('_')) {
                    frequency = frequency?.toString().split('_')[1];
                  }
                  acc[key] = mapItem ? `${mapItem.singleDose},${mapItem.mediRoute || '-'}, ${frequency}` : value;
                } else if (mapItem.type === this.itemStore.Type.stack || mapItem.type === this.itemStore.Type.labour) {
                  acc[key] = mapItem
                    ? {
                        value: `${mapItem.singleDose}`,
                        popContent: mapItem.content
                      }
                    : {
                        value,
                        popContent: ''
                      };
                } else {
                  acc[key] = mapItem ? `${mapItem.singleDose}` : value;
                }
              } else {
                acc[key] = '';
              }
            } else {
              acc[key] = value;
            }
            return acc;
          },
          Promise.resolve({} as Api.Program.TableDatum)
        );
      })
    );
  }
  getProgramDetail(id: string, moduleId: string): ReturnType<typeof request<Api.Program.ItemResponseData>> {
    return fetchGetProgramDetailByClinic(id, moduleId);
  }
}
