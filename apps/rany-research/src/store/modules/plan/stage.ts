import { defineStore } from 'pinia';
import { ref } from 'vue';
import { SetupStoreId } from '@/enum';
import {
  fetchGetClinicStageDelete,
  fetchGetClinicStageDeleteProgramOrRest,
  fetchPostClinicStageAdd,
  fetchPostClinicStageAddProgramOrRest,
  fetchPostClinicStageUpdate
} from '@/service/api/clinicPlan';
import { useLoading } from '~/packages/hooks/src';

export const useStageStore = defineStore(SetupStoreId.Stage, () => {
  const StageType = ref<Api.ClinicStage.StageType>({
    stage: 0,
    solution: 1,
    rest: 2
  });
  const stageModel = ref<Api.ClinicStage.StageVO>({
    clinicPlanId: '',
    orderNumber: 0,
    relation: '',
    type: 0,
    id: ''
  });

  const currentStageTab = ref<Partial<Api.ClinicStage.StageItem & Api.ClinicPlan.PanelType>>();

  const { loading, startLoading, endLoading } = useLoading();

  function $reset() {
    stageModel.value = {
      clinicPlanId: '',
      orderNumber: 0,
      relation: '',
      type: 0,
      id: ''
    };
  }

  async function addClinicStage() {
    startLoading();
    const { error, data } = await fetchPostClinicStageAdd(stageModel.value);

    if (!error) {
      window.$message?.success('添加成功');
      endLoading();
      return true;
    }
    endLoading();
    return false;
  }

  async function updateClinicStage() {
    startLoading();
    const { error } = await fetchPostClinicStageUpdate(stageModel.value);

    if (!error) {
      window.$message?.success('保存成功');
      endLoading();
      return true;
    }
    endLoading();
    return false;
  }
  let lastSuccessTime = 0;
  const THROTTLE_DURATION = 2000; // 2秒内不重复提示

  async function updateClinicStageOrder(data: Api.ClinicStage.StageVO) {
    startLoading();
    const { error } = await fetchPostClinicStageUpdate(data);

    if (!error) {
      const now = Date.now();
      if (now - lastSuccessTime > THROTTLE_DURATION) {
        window.$message?.success('保存成功');
        lastSuccessTime = now;
      }
      endLoading();
      return true;
    }
    endLoading();
    return false;
  }

  async function addClinicStageProgram(programId: string) {
    startLoading();
    let orderNumber = 1;
    if (currentStageTab.value?.solutions?.length) {
      orderNumber += currentStageTab.value.solutions.length;
    }
    const { error } = await fetchPostClinicStageAddProgramOrRest({
      stageId: currentStageTab.value?.id,
      type: StageType.value.solution,
      relation: programId,
      orderNumber
    });

    if (!error) {
      window.$message?.success('添加成功');
    }
    endLoading();
  }

  async function deleteClinicStage(id: string) {
    startLoading();
    if (currentStageTab.value?.id) {
      const { error } = await fetchGetClinicStageDelete(id);
      if (!error) {
        window.$message?.success('删除成功');
      }
    } else {
      window.$message?.error('阶段不存在');
    }

    endLoading();
  }

  async function addClinicStageRest(restDays: any) {
    startLoading();
    let orderNumber = 1;
    if (currentStageTab.value?.solutions?.length) {
      orderNumber += currentStageTab.value.solutions.length;
    }
    const { error } = await fetchPostClinicStageAddProgramOrRest({
      stageId: currentStageTab.value?.id,
      type: StageType.value.rest,
      relation: restDays,
      orderNumber
    });

    if (!error) {
      window.$message?.success('添加成功');
    }
    endLoading();
  }

  async function deleteClinicStageItem(id: string) {
    startLoading();
    const { error } = await fetchGetClinicStageDeleteProgramOrRest(id);
    if (!error) {
      window.$message?.success('方案项删除成功');
    }
    endLoading();
  }

  return {
    StageType,
    stageModel,
    addClinicStage,
    updateClinicStage,
    updateClinicStageOrder,
    loading,
    currentStageTab,
    addClinicStageRest,
    addClinicStageProgram,
    deleteClinicStageItem,
    deleteClinicStage,
    $reset
  };
});
