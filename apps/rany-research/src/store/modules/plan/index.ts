import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import type { SelectOption } from 'naive-ui';
import { SetupStoreId } from '@/enum';
import {
  fetchGetClinicDelete,
  fetchGetClinicDetail,
  fetchGetClinicPreview,
  fetchGetClinicSpecificDetail,
  fetchPostClinicAdd,
  fetchPostClinicLinkProject,
  fetchPostClinicList,
  fetchPostClinicPlanCopy,
  fetchPostClinicUpdate
} from '@/service/api/clinicPlan';
import { useLoading } from '~/packages/hooks/src';
import { useStageStore } from './stage';

export const usePlanStore = defineStore(SetupStoreId.Plan, () => {
  const stageStore = useStageStore();

  const formModel = ref<Api.ClinicPlan.ClinicPlanItem>({
    name: '',
    restriction: 1,
    diseaseId: '',
    projectId: ''
  });

  const planBaseInfo = ref<Api.ClinicPlan.ClinicPlanItem>({
    name: '',
    restriction: 1,
    diseaseId: '',
    riskId: ''
  });

  const planInfo = ref<Api.ClinicPlan.ClinicPlanDetail>();

  const previewModel = ref<Api.ClinicPlan.ClinicPlanDetail>();

  const { loading, startLoading, endLoading } = useLoading();

  async function copyPlan(id: string) {
    startLoading();
    const { error } = await fetchPostClinicPlanCopy({
      clinicPlanId: id
    });
    if (!error) {
      window.$message?.success('复制成功');
    }
    endLoading();
  }

  async function addPlan() {
    startLoading();
    const { data, error } = await fetchPostClinicAdd(formModel.value);
    if (!error && data) {
      window.$message?.success('添加成功');
      endLoading();
      return data; // 返回新建计划的ID
    }
    window.$message?.error('添加失败');
    endLoading();
    return null;
  }

  function getPlanOptions() {
    const options = ref<SelectOption[]>([]);
    const load = async () => {
      startLoading();
      const response = await fetchPostClinicList({
        pageNo: 1,
        pageSize: 999
      });
      if (response && !('records' in response)) {
        response.data?.records.forEach(record => {
          options.value.push({
            label: record.name,
            value: record.id
          });
        });
      }
      endLoading();
    };

    return { data: options, load, loading: loading.value }; // Assuming loading is a ref
  }

  async function getDetail(id?: string) {
    if (id) {
      startLoading();
      const { error, data } = await fetchGetClinicDetail(id);
      if (!error) {
        formModel.value = data;
      }
      endLoading();
    }
  }

  async function getPlanInfoPreview(id?: string) {
    const { error, data } = await fetchGetClinicPreview(id);
    if (!error) {
      planInfo.value = data;
      planBaseInfo.value = data.clinicPlan;
    }
  }

  function getDetailPreview(id?: string) {
    const model = ref<Api.ClinicPlan.ClinicPlanDetail>();
    const stages = computed(() => model.value?.clinicPlan.stages);
    const panels = ref<Api.ClinicPlan.PanelType[]>([]);

    /** @param name 默认选的tabname */
    async function load(name?: string) {
      if (!id) return;

      startLoading();
      const { error, data } = await fetchGetClinicPreview(id);
      if (!error) {
        model.value = data;
        planBaseInfo.value = data.clinicPlan;
        panels.value = [];
        stages.value!.forEach(stage => {
          panels.value.push({
            name: stage.name,
            temporaryName: stage.name,
            isEditing: false,
            id: stage.id,
            solutions: stage.solutions,
            orderNumber: stage.orderNumber
          });
        });
        if (!name) {
          stageStore.currentStageTab = stages.value![0];
        } else {
          setCurrentStageByName(name);
        }
      }
      endLoading();
    }

    load();

    /**
     * 根据阶段名称删除阶段
     *
     * @param name 阶段名称
     */
    async function deleteClinicStageByName(name: string) {
      // 根据名称查找对应的阶段
      const stage = stages.value?.find(d => d.name === name);
      if (stage?.id) {
        // 调用删除阶段的接口
        await stageStore.deleteClinicStage(stage.id);
        // 如果删除的是当前选中的tab，则切换到第一个tab
        if (stageStore.currentStageTab?.name === name) {
          stageStore.currentStageTab = panels.value[0];
        }
        // 重新加载数据
        load();
      }
    }

    function setCurrentStageByName(name: string) {
      const stage = model.value?.clinicPlan.stages.find(d => d.name === name);
      if (stage) {
        stageStore.currentStageTab = stage;
      }
    }

    async function editStage(name: string, index: number) {
      if (formModel.value.id && stageStore.currentStageTab) {
        stageStore.currentStageTab.name = name;
        stageStore.stageModel.relation = stageStore.currentStageTab.name;
        stageStore.stageModel.orderNumber = index;
        stageStore.stageModel.clinicPlanId = formModel.value.id;

        // Adding
        if (!stageStore.currentStageTab.id) {
          // 在阶段添加成功前，先将阶段名称暂存到temporaryName字段中
          stageStore.stageModel.relation = stageStore.currentStageTab.temporaryName as string;
          const res = await stageStore.addClinicStage();
          if (!res) {
            panels.value.pop();
            stageStore.$reset();
            return;
          }
        } else {
          stageStore.stageModel.id = stageStore.currentStageTab.id;
          stageStore.stageModel.relation = stageStore.currentStageTab.temporaryName as string;
          await stageStore.updateClinicStage();
        }

        stageStore.currentStageTab.isEditing = false;
        load(stageStore.stageModel.relation);
        stageStore.$reset();
      } else {
        window.$dialog?.info({ content: '请选择计划', title: '提示' });
      }
    }

    return { model, load, panels, deleteClinicStageByName, setCurrentStageByName, editStage };
  }

  async function updatePlan() {
    startLoading();
    const { error } = await fetchPostClinicUpdate(formModel.value);
    if (!error) {
      window.$message?.success('计划信息更新完成');
    }
    $reset();
    endLoading();
  }

  async function updatePlanStatus() {
    startLoading();
    const { error } = await fetchPostClinicUpdate({
      id: formModel.value.id,
      status: formModel.value.status
    });
    if (!error) {
      window.$message?.success('计划状态更新完成');
    }
    endLoading();
  }

  async function deletePlan(id: string) {
    startLoading();
    const { error } = await fetchGetClinicDelete(id);
    if (!error) {
      window.$message?.success('计划已删除');
    }
    endLoading();
  }

  function getSpecificDetail() {
    const model = ref<Api.ClinicPlan.ClinicPlanDetail>();

    async function load(id: string) {
      startLoading();
      const { error, data } = await fetchGetClinicSpecificDetail(id);
      if (!error) {
        model.value = data;
      }
      endLoading();
    }

    return { data: model, load };
  }

  async function linkClinicPlanToProject() {
    startLoading();
    const { error } = await fetchPostClinicLinkProject({
      projectId: formModel.value.projectId,
      clinicPlanId: formModel.value.id
    });
    if (!error) {
      window.$message?.success('关联成功');
    }
    endLoading();
    $reset();
  }

  function $reset() {
    formModel.value = { name: '', restriction: 1, diseaseId: '', projectId: '', status: 0 };
  }

  function updatePlanName(name: string) {
    planBaseInfo.value.name = name;
  }

  return {
    formModel,
    linkClinicPlanToProject,
    $reset,
    copyPlan,
    addPlan,
    loading,
    planBaseInfo,
    planInfo,
    getDetail,
    updatePlan,
    updatePlanStatus,
    deletePlan,
    getDetailPreview,
    getPlanInfoPreview,
    previewModel,
    getSpecificDetail,
    updatePlanName,
    getPlanOptions
  };
});
