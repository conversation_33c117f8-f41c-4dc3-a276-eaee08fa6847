import type { request } from '@/service/request';

// 定义数据源策略接口(专科袋)
export interface DataSourceStrategy {
  // 添加小项
  addItem?(data: Api.ProgramItem.ProgramItemRecord): ReturnType<typeof request<string | null>>;
  // 更新小项
  updateItem?(data: Api.ProgramItem.ProgramItemRecord): ReturnType<typeof request<string | null>>;
  // 删除方案小项
  deleteItem?(id: string): ReturnType<typeof request<string | null>>;
  // 小项详情
  detailItem?(id: string): ReturnType<typeof request<Api.ProgramItem.ProgramItemRecord>>;
  // 删除行小项
  deleteRowItem?(rowNum: number, solutionId: string): ReturnType<typeof request<string | null>>;
  // 添加自定义小项
  addCustomItem?(data: Api.ProgramItem.AddCustomItemVO): ReturnType<typeof request<string | null>>;
  // 删除自定义小项
  deleteCustomItem?(id: string): ReturnType<typeof request<string | null>>;
  // 自定义小项详情
  detailCustomItem?(id: string): ReturnType<typeof request<Api.ProgramItem.ProgramItemRecord>>;
  // 更新自定义小项
  updateCustomItem?(data: Api.ProgramItem.AddCustomItemVO): ReturnType<typeof request<string | null>>;

  // 数据转换
  transformData?(data: any): any;
  // 获取方案详情
  getProgramDetail(id: string, moduleId?: string): ReturnType<typeof request<Api.Program.ItemResponseData>>;
}
