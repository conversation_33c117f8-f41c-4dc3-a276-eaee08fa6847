import { ref } from 'vue';
import { defineStore } from 'pinia';
import { SetupStoreId } from '@/enum';
import { fetchGetExperimentDetail, fetchGetExperimentDict, fetchPostExperimentList } from '@/service/api';

export const useExperimentStore = defineStore(SetupStoreId.Experiment, () => {
  const experimentDict = ref<Api.Common.CommonOption[]>([]);
  const researchTypeDict = ref<Api.Common.CommonOption[]>([]);
  const researchPhaseDict = ref<Api.Common.CommonOption[]>([]);
  const blindMethodDict = ref<Api.Common.CommonOption[]>([]);
  const researchDesignDict = ref<Api.Common.CommonOption[]>([]);
  const ethnicityDict = ref<Api.Common.CommonOption[]>([]);
  const medicationFrequencyDict = ref<Api.Common.CommonOption[]>([]);
  const administrationRouteDict = ref<Api.Common.CommonOption[]>([]);

  const experimentList = ref<Api.Common.CommonOption[]>([]);
  async function getExperimentList() {
    const { data, error } = await fetchPostExperimentList({
      pageNo: 1,
      pageSize: 1000
    });
    if (!error && data) {
      experimentList.value = data.records.map(item => ({
        label: item.name,
        value: item.id
      })) as Api.Common.CommonOption[];
    }
  }

  async function getExperimentDict() {
    const { data, error } = await fetchGetExperimentDict('dictyjlx');
    if (!error) {
      experimentDict.value = data.map(item => ({
        label: item.itemText,
        value: item.itemValue
      }));
    }
  }

  async function getResearchTypeDict() {
    const { data, error } = await fetchGetExperimentDict('dictyjlx');
    if (!error) {
      researchTypeDict.value = data.map(item => ({
        label: item.itemValue,
        value: item.id
      })) as Api.Common.CommonOption[];
    }
  }

  async function getResearchPhaseDict() {
    const { data, error } = await fetchGetExperimentDict('dictyjjd');
    if (!error) {
      researchPhaseDict.value = data.map(item => ({
        label: item.itemValue,
        value: item.id
      })) as Api.Common.CommonOption[];
    }
  }

  async function getBlindMethodDict() {
    const { data, error } = await fetchGetExperimentDict('dictmafa');
    if (!error) {
      blindMethodDict.value = data.map(item => ({
        label: item.itemValue,
        value: item.id
      })) as Api.Common.CommonOption[];
    }
  }

  async function getResearchDesignDict() {
    const { data, error } = await fetchGetExperimentDict('dictyjsj');
    if (!error) {
      researchDesignDict.value = data.map(item => ({
        label: item.itemValue,
        value: item.id
      })) as Api.Common.CommonOption[];
    }
  }

  async function getEthnicityDict() {
    const { data, error } = await fetchGetExperimentDict('dict12');
    if (!error) {
      ethnicityDict.value = data.map(item => ({
        label: item.itemValue,
        value: item.id
      })) as Api.Common.CommonOption[];
    }
  }

  async function getMedicationFrequencyDict() {
    const { data, error } = await fetchGetExperimentDict('dict5');
    if (!error) {
      medicationFrequencyDict.value = data.map(item => ({
        label: item.itemValue,
        value: item.id
      })) as Api.Common.CommonOption[];
    }
  }

  async function getAdministrationRouteDict() {
    const { data, error } = await fetchGetExperimentDict('dict3');
    if (!error) {
      administrationRouteDict.value = data.map(item => ({
        label: item.itemValue,
        value: item.id
      })) as Api.Common.CommonOption[];
    }
  }

  const experimentInfo = ref<Api.Experiment.ExperimentDetail>();
  async function getExperimentDetail(id: string) {
    const { data, error } = await fetchGetExperimentDetail(id);
    if (!error) {
      experimentInfo.value = data;
    }
  }

  const maritalStatusOptions: any = ref([]);

  async function getMaritalStatusOptions() {
    const { data, error } = await fetchGetExperimentDict('dict22');
    if (!error) {
      maritalStatusOptions.value = data.map(item => ({
        label: item.itemValue,
        value: item.id
      }));
    }
  }

  return {
    experimentInfo,
    maritalStatusOptions,
    getMaritalStatusOptions,
    getExperimentDetail,
    experimentList,
    experimentDict,
    researchTypeDict,
    researchPhaseDict,
    blindMethodDict,
    researchDesignDict,
    ethnicityDict,
    medicationFrequencyDict,
    administrationRouteDict,
    getExperimentDict,
    getExperimentList,
    getResearchTypeDict,
    getResearchPhaseDict,
    getBlindMethodDict,
    getResearchDesignDict,
    getEthnicityDict,
    getMedicationFrequencyDict,
    getAdministrationRouteDict
  };
});
