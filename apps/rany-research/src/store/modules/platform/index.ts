import { defineStore } from 'pinia';
import type { CascaderOption, SelectOption, TreeSelectOption } from 'naive-ui';
import { ref } from 'vue';
import { SetupStoreId } from '@/enum';
import { useLoading } from '~/packages/hooks/src';
import {
  fetchGetCheckCategoryById,
  fetchGetCheckRecords,
  fetchGetMedicineDictFrequency,
  fetchGetMedicineDictList,
  fetchGetMedicineDictRoute,
  fetchGetOperationRecords
} from '@/service/api/platform';
import { fetchGetExperimentDict } from '@/service/api/experiment';

export const usePlatformStore = defineStore(
  SetupStoreId.Platform,
  () => {
    const dictMapCache = ref<Record<string, any>>({});
    const dictKeys = ref({
      Medicine: 'Medicine',
      Frequency: 'Frequency',
      Operation: 'Operation',
      Route: 'Route'
    });

    /**
     * Creates a set of variables for loading options of a select/dropdown component. The function returns an object
     * with three properties:
     *
     * - `loading`: a boolean indicating whether the options are being loaded
     * - `options`: an array of options
     * - `load`: a function that loads the options
     *
     * @param fetchFunction a function that returns a promise of an object with two properties: `error` and `data`
     * @param mapFunction a function that maps an item of the `data` array to a select option
     * @returns an object with the three properties mentioned above
     */
    function createOptionsLoader(
      fetchFunction: () => Promise<{ error: any; data: any }>,
      mapFunction: (item: any) => SelectOption,
      key: string
    ) {
      const { loading, startLoading, endLoading } = useLoading();
      const options = ref<SelectOption[]>([]);
      const load = async () => {
        startLoading();
        if (options.value.length <= 0) {
          if (!dictMapCache.value[key]) {
            const { error, data } = await fetchFunction();
            if (error) {
              window.$dialog?.error({ content: error.message });
            }
            if (data) {
              dictMapCache.value[key] = data.map(mapFunction);
            }
          }
          options.value = dictMapCache.value[key];
        }
        endLoading();
      };

      return {
        loading,
        options,
        load
      };
    }

    async function getLabelByKey(key: (typeof dictKeys.value)[keyof typeof dictKeys.value], dictKey?: string) {
      if (dictKey && key === dictKeys.value.Frequency) {
        const { load, options } = getMedicineFrequencyDictOptions();
        await load();
        return options.value.find(item => item.value === dictKey)?.label;
      }
      return '';
    }

    function getMedicineDictOptions() {
      return createOptionsLoader(
        fetchGetMedicineDictList,
        item => ({ label: item.groupName, value: item.id }),
        dictKeys.value.Medicine
      );
    }
    function getMedicineFrequencyDictOptions() {
      return createOptionsLoader(
        fetchGetMedicineDictFrequency,
        (item: DataPlatformApi.Medicine.DictionaryItem) => ({
          label: item.itemValue,
          value: item.itemValue
        }),
        dictKeys.value.Frequency
      );
    }
    function getMedicineRouteDictTree() {
      const { loading, startLoading, endLoading } = useLoading();

      const load = async (parentId?: string, depth: number = 1) => {
        startLoading();
        const { error, data } = await fetchGetExperimentDict(parentId ?? 'dict3');

        if (error) {
          window.$dialog?.error({ content: error.message });
        }
        const options = ref<TreeSelectOption[]>([]);

        if (data) {
          options.value = data.map(d => ({
            label: d.itemValue,
            key: d.id,
            depth,
            isLeaf: !d.hasChild
          }));
        }
        endLoading();
        return options.value;
      };

      return {
        loading,
        load
      };
    }

    function getCheckOptions() {
      const options = ref<CascaderOption[]>([]);
      const { loading, startLoading, endLoading } = useLoading();

      async function load(parentId: string, depth: number, option?: CascaderOption) {
        startLoading();

        if (!dictMapCache.value[parentId]) {
          const { data } = await fetchGetCheckCategoryById(parentId);
          dictMapCache.value[parentId] = data;
        }

        const dataOptions = dictMapCache.value[parentId] as DataPlatformApi.Category.ItemRecord[];

        if (depth === 1) {
          options.value = [];
          dataOptions?.forEach(item =>
            options.value.push({
              label: item.catName,
              value: item.id,
              depth,
              isLeaf: item.hasChild <= 0
            })
          );
        } else if (option) {
          option.children = dataOptions?.map(item => ({
            label: item.catName,
            value: item.id,
            depth,
            isLeaf: item.hasChild <= 0
          }));
        }

        endLoading();
      }
      return { load, loading, options };
    }

    function getOperationOptions() {
      const options = ref<CascaderOption[]>([]);
      const { loading, startLoading, endLoading } = useLoading();

      async function load(parentId: string = 'ICD-9-CM-3', depth: number = 1, option: CascaderOption | null = null) {
        startLoading();

        if (!dictMapCache.value[parentId]) {
          const { data } = await fetchGetOperationRecords(parentId);
          dictMapCache.value[parentId] = data;
        }

        const dataOptions = dictMapCache.value[parentId] as DataPlatformApi.OperationItem.OperationRecord[];

        if (depth === 1) {
          options.value = [];
          dataOptions?.forEach(item =>
            options.value.push({
              label: item.name,
              value: item.id,
              depth,
              isLeaf: item.hasChild <= 0
            })
          );
        } else if (option) {
          option.children = dataOptions?.map(item => ({
            label: item.name,
            value: item.id,
            depth,
            isLeaf: item.hasChild <= 0
          }));
        }

        endLoading();
      }
      return { load, loading, options };
    }

    async function getCheckOptionsByCategory(categoryId: string) {
      const options = ref<CascaderOption[]>([]);
      const { data } = await fetchGetCheckRecords(categoryId);
      data?.forEach(item =>
        options.value.push({
          label: item.itemName,
          value: item.id,
          depth: 2,
          isLeaf: true
        })
      );

      return options.value;
    }

    return {
      dictMapCache,
      dictKeys,
      getLabelByKey,
      getMedicineDictOptions,
      getMedicineFrequencyDictOptions,
      getMedicineRouteDictTree,
      getCheckOptions,
      getCheckOptionsByCategory,
      getOperationOptions
    };
  },
  { persist: true }
);
