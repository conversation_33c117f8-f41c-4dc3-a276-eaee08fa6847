import type { Mock } from 'vitest';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createPinia, setActivePinia } from 'pinia';
import { fetchPostPatientAdd, fetchPostPatientList } from '@/service/api';
import { usePatientStore } from '.';

// Mocks
vi.mock('@/service/api');

describe('Patient', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    setActivePinia(createPinia());
  });

  it('should return true when fetchAddPatient succeeds', async () => {
    const patientStore = usePatientStore();
    const mockPatient: Api.Patient.PatientItem & Api.Common.BaseRequestParams = {
      id: '1',
      diagnosisFinals: ['1', '2'],
      hospitalName: 'Example Hospital',
      riskName: 'Low Risk',
      status: 1,
      treatmentId: '1',
      treatment: {
        diagnosisFinal: 'Final Diagnosis',
        diagnosisInit: 'Initial Diagnosis',
        id: '1',
        hospitalId: '1',
        outpatientNumber: '123456',
        currFlag: '1',
        createTime: '2024-01-01',
        createBy: 'admin',
        delFlag: 0,
        riskId: '1',
        patientId: '1',
        diagnosisTime: '2024-01-01',
        diagnosisFinals: ['1', '2']
      },
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      userName: 'admin'
    };

    // Mock implementation of fetchAddPatient
    (fetchPostPatientAdd as Mock).mockResolvedValue({ data: mockPatient, error: null });

    // Act
    const result = await patientStore.addPatient(mockPatient);

    // Assert
    expect(result).toBe(true);
    expect(fetchPostPatientAdd).toHaveBeenCalledWith(mockPatient);
  });

  it('should return false when fetchAddPatient fails', async () => {
    const patientStore = usePatientStore();
    const mockPatient: Api.Patient.PatientItem & Api.Common.BaseRequestParams = {
      id: '1',
      hospitalName: 'Example Hospital',
      riskName: 'Low Risk',
      status: 1,
      diagnosisFinals: ['1', '2'],
      treatment: {
        diagnosisFinals: ['1', '2'],
        diagnosisFinal: 'Final Diagnosis',
        diagnosisInit: 'Initial Diagnosis',
        id: '1',
        hospitalId: '1',
        outpatientNumber: '123456',
        currFlag: '1',
        createTime: '2024-01-01',
        createBy: 'admin',
        delFlag: 0,
        riskId: '1',
        patientId: '1',
        diagnosisTime: '2024-01-01'
      },
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
      userName: 'admin'
    };
    const mockError = new Error('Failed to add patient');

    // Mock implementation of fetchAddPatient
    (fetchPostPatientAdd as Mock).mockResolvedValue({ data: null, error: mockError });

    const result = await patientStore.addPatient(mockPatient);

    expect(result).toBe(false);
    expect(fetchPostPatientAdd).toHaveBeenCalledWith(mockPatient);
  });

  it('should test getPatients function with different search parameters', async () => {
    const patientStore = usePatientStore();
    const mockSearchParams: Api.Patient.CommonSearchParams = {
      name: 'John Doe',
      pageSize: 10,
      pageNo: 1
    };

    // Mock implementation of fetchPostPatientList
    (fetchPostPatientList as Mock).mockResolvedValue({ data: mockSearchParams, error: null });

    // Act
    const result = await patientStore.getPatients(mockSearchParams);

    // Assert
    expect(result).toEqual(mockSearchParams);
    expect(fetchPostPatientList).toHaveBeenCalledWith(mockSearchParams);
  });
});
