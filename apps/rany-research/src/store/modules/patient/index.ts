import { defineStore } from 'pinia';
import { ref } from 'vue';
import dayjs from 'dayjs';
import { SetupStoreId } from '@/enum';
import { fetchGetPatientInfo, fetchPostPatientAdd, fetchPostPatientList, fetchPutPatientUpdate } from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

export const usePatientStore = defineStore(SetupStoreId.Patient, () => {
  const { loading: actionLoading, startLoading, endLoading } = useLoading();
  async function addPatient(patient: Api.Patient.PatientItem & Api.Common.BaseRequestParams) {
    startLoading();
    const { error } = await fetchPostPatientAdd(patient);
    if (!error) {
      return true;
    }
    window.$message?.error(error.message);
    endLoading();
    return false;
  }

  const patientModal = ref<Api.Patient.PatientItem>({
    id: '',
    hospitalName: '',
    riskName: '',
    nationName: '',
    treatment: {
      diagnosisFinal: '',
      diagnosisInit: '',
      id: '',
      hospitalId: '',
      outpatientNumber: '',
      currFlag: '',
      createTime: '',
      createBy: '',
      delFlag: 0,
      riskId: '',
      patientId: '',
      diagnosisTime: '',
      diagnosisFinals: []
    },
    diagnosisFinals: [],
    sbagId: ''
  });
  async function fetchGetPatientDetail(id: string) {
    startLoading();
    const { error, data } = await fetchGetPatientInfo(id);
    if (!error) {
      patientModal.value = data;
      if (data.treatment) {
        patientModal.value.diagnosisFinal = data.treatment.diagnosisFinal;
        patientModal.value.diagnosisInit = data.treatment.diagnosisInit;
        patientModal.value.riskId = data.treatment.riskId;
        patientModal.value.hospitalId = data.treatment.hospitalId;
        patientModal.value.outpatientNumber = data.treatment.outpatientNumber;
        patientModal.value.diagnosisTime = data.treatment.diagnosisTime;
        patientModal.value.treatmentId = data.treatment.id;
      }
    } else {
      window.$message?.error(error.message);
    }
    endLoading();
  }

  async function updatePatient(patient: Api.Patient.PatientItem) {
    const { error } = await fetchPutPatientUpdate(patient);
    if (!error) {
      return true;
    }
    return false;
  }

  async function getPatients(patient: Api.Patient.CommonSearchParams) {
    const { error, data } = await fetchPostPatientList(patient);
    if (!error) {
      return data;
    }
    return error;
  }
  function reset() {
    patientModal.value = {
      id: '',
      name: '',
      phoneNumber: '',
      gender: '',
      diagnosisFinals: [],
      height: 0,
      weight: 0,
      nation: '',
      birthplace: '',
      nativePlace: '',
      maritalStatus: '',
      familyAddress: '',
      permanentAddress: '',
      birthday: dayjs().format('YYYY-MM-DD HH:MM:DD'),
      diagnosisTime: dayjs().format('YYYY-MM-DD HH:MM:DD'),
      credNumber: '',
      riskId: '',
      hospitalId: '',
      treatmentId: '',
      outpatientNumber: '',
      diagnosisInit: '',
      diagnosisFinal: '',
      sbagFlag: 0,
      sbagId: '',
      diseaseId: '',
      hospitalName: '',
      riskName: '',
      treatment: {
        diagnosisFinals: [],
        diagnosisFinal: '',
        diagnosisInit: '',
        id: '',
        hospitalId: '',
        outpatientNumber: '',
        currFlag: '',
        createTime: '',
        createBy: '',
        delFlag: 0,
        riskId: '',
        patientId: '',
        diagnosisTime: ''
      }
    };
  }
  return {
    actionLoading,
    patientModal,
    fetchGetPatientDetail,
    addPatient,
    updatePatient,
    getPatients,
    reset
  };
});
