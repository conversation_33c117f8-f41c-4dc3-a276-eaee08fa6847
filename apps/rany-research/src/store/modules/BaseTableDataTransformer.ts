import { usePlatformStore } from './platform';
import { useItemStore } from './program/item';

const itemStore = useItemStore();
const platformStore = usePlatformStore();

interface ValueObject {
  value: string;
  popContent: string;
}

// 处理频率标签
const formatFrequency = async (frequency: string | undefined): Promise<string> => {
  const label = await platformStore.getLabelByKey(platformStore.dictKeys.Frequency, frequency);
  if (label?.toString().includes('_')) {
    return label.toString().split('_')[1];
  }
  return label?.toString() || '-';
};

// 处理药品和堆栈类型的共同逻辑
const handleMedicineOrStack = async (mapItem: any): Promise<string> => {
  const frequency = await formatFrequency(mapItem.frequency);
  return `${mapItem.singleDose},${mapItem.mediRoute || '-'}, ${frequency}`;
};

// 处理劳务类型
const handleLabourContent = (content: string): string[] => {
  try {
    const contentObject = JSON.parse(content);
    return Object.values<DataPlatformApi.CheckItem.ItemRecord>(contentObject)
      .filter(v => Object.hasOwn(v, 'itemName') && v.itemName)
      .map(v => v.itemName!)
      .filter((name): name is string => name !== undefined);
  } catch (error) {
    console.error(error);
    return [];
  }
};

// 根据不同类型处理值
const processItemByType = async (mapItem: any): Promise<string | ValueObject> => {
  switch (mapItem.type) {
    case itemStore.Type.medicine:
      return await handleMedicineOrStack(mapItem);

    case itemStore.Type.stack: {
      const value = await handleMedicineOrStack(mapItem);
      const popContent =
        mapItem.stackItems?.map((item: any) => `${item.name}:${mapItem.singleDose}${item.unit}`).join(',') || '';
      return { value, popContent };
    }

    case itemStore.Type.labour: {
      const values = handleLabourContent(mapItem.content as string);
      return {
        value: `${mapItem.singleDose}`,
        popContent: values.join(',')
      };
    }

    default:
      return `${mapItem.singleDose}`;
  }
};

// 处理单个键值对
interface ProcessKeyValueParams {
  key: string;
  value: any;
  itemMap: any;
  itemCustomMap: any;
}

const processKeyValue = async ({
  key,
  value,
  itemMap,
  itemCustomMap
}: ProcessKeyValueParams): Promise<[string, any]> => {
  if (typeof value !== 'boolean') {
    return [key, value];
  }

  if (!value) {
    return [key, ''];
  }

  let mapItem = itemMap[key];
  // 默认情况下的comment为空,只有itemCustomMap有值
  mapItem.comment = '';
  const selfItem = itemCustomMap[key];
  if (selfItem) {
    mapItem = selfItem;
  }
  const processedValue = await processItemByType(mapItem);
  return [key, processedValue];
};

export const transformData = async (data: Api.Program.ItemResponseData) => {
  if (!data) return [];

  const transformRow = async (row: any) => {
    const entries = await Promise.all(
      Object.entries(row).map(async ([key, value]) => {
        return processKeyValue({
          key,
          value,
          itemMap: data.itemMap,
          itemCustomMap: row.itemCustomMap
        });
      })
    );
    return Object.fromEntries(entries);
  };

  return Promise.all(data.tableData.map(transformRow));
};
