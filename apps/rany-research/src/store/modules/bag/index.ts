import { defineStore } from 'pinia';
import { ref } from 'vue';
import { fetchGetPatientBag, fetchPostBagAdd } from '@/service/api/bag';
import { useLoading } from '~/packages/hooks/src';
import { SetupStoreId } from '@/enum';
import { fetchGetRegisterPageDetail, fetchPostBindSolutionToStage } from '@/service/api';

export const useBagStore = defineStore(SetupStoreId.Bag, () => {
  const { loading, startLoading, endLoading } = useLoading();
  /** 患者生成专科袋 */
  const addingModel = ref<Api.Bag.GenerateBagParams>({
    patientId: '',
    registPage: ''
  });

  function resetAddingModel() {
    addingModel.value = {
      patientId: '',
      registPage: ''
    };
  }

  async function generateBagAction() {
    startLoading();
    let result = false;
    const { data } = await fetchGetRegisterPageDetail(addingModel.value.registPage);
    if (data?.template) {
      addingModel.value.registPage = JSON.stringify(data.template);
    } else {
      window.$message?.warning('登记首页数据异常，请更换数据后重试');
      resetAddingModel();
      return result;
    }
    const { error } = await fetchPostBagAdd(addingModel.value);
    if (!error) {
      window.$message?.success('专科袋生成成功！');
      result = true;
      resetAddingModel();
    }
    endLoading();
    return result;
  }

  // 随机化--随机组别，诊疗计划，计划插入合并位置
  const randomizeModel = ref<Api.Bag.RandomizeParams>({
    // 诊疗计划
    clinicPlanId: '',
    // 专科袋生成插入位置
    sbagSoluId: '',
    // 组别
    tranches: '',
    patientId: ''
  });

  function resetRandomizeModel() {
    randomizeModel.value = {
      clinicPlanId: '',
      sbagSoluId: '',
      tranches: '',
      patientId: ''
    };
  }

  async function generateRandomizeAction() {
    startLoading();
    let result = false;
    const { error } = await fetchPostBagAdd(randomizeModel.value);
    if (!error) {
      window.$message?.success('随机化成功！');
      result = true;
      resetAddingModel();
    }
    endLoading();
    return result;
  }

  function getPatientBagDetail() {
    const model = ref<Api.Bag.PatientBagResult>();

    async function load(id: string) {
      startLoading();
      const { error, data } = await fetchGetPatientBag(id);
      if (!error) {
        const solutionPromises = Array<Promise<Api.ClinicPlan.ClinicSolutionItem>>();
        data.sbag.stages.forEach(stage => {
          if (stage.solutions) {
            stage.solutions.forEach(solution => {
              solutionPromises.push(Promise.resolve(solution));
            });
          }
        });

        model.value = data;
      }
      endLoading();
    }

    return { data: model, load };
  }

  async function bindSolutionOrRestToStage(data: Partial<Api.Bag.BindSolutionToStageParams>) {
    startLoading();
    const { error } = await fetchPostBindSolutionToStage(data);
    if (!error) {
      window.$message?.success('绑定成功');
    }
    endLoading();
  }

  return {
    loading,
    addingModel,
    randomizeModel,
    resetAddingModel,
    resetRandomizeModel,
    bindSolutionOrRestToStage,
    generateBagAction,
    generateRandomizeAction,
    getPatientBagDetail
  };
});
