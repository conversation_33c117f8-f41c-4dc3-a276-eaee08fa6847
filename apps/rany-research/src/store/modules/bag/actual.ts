import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useLoading } from '~/packages/hooks/src';
import {
  fetchGetActualValueToBagDetail,
  fetchPostActualValueToBagAdd,
  fetchPostActualValueToBagUpdate
} from '@/service/api';
import { ItemType } from '../program/item';

export const useActualStore = defineStore('actual-item-store', () => {
  const model = ref<Partial<Api.Bag.ActualValueItem & Api.Bag.ActualValueParams>>({});
  const { loading, startLoading, endLoading } = useLoading();

  function transformData(item: Api.Bag.ActualValueParams) {
    if (Array.isArray(item.mediRoute)) {
      item.mediRoute = item.mediRoute.join(',');
    }
    if (typeof item.singleDose === 'object') {
      item.singleDose = JSON.stringify(item.singleDose);
    }
  }

  async function addActualValue(item: Api.Bag.ActualValueParams) {
    startLoading();
    transformData(item);
    try {
      const res = await fetchPostActualValueToBagAdd(item);
      if (!res.error) {
        window.$message?.success('保存成功');
      }
    } finally {
      endLoading();
    }
  }
  async function updateActualValue(item: Api.Bag.ActualValueParams) {
    startLoading();
    transformData(item);
    try {
      const res = await fetchPostActualValueToBagUpdate(item);
      if (!res.error) {
        window.$message?.success('保存成功');
      }
    } finally {
      endLoading();
    }
  }
  function getActualValue(id: string) {
    startLoading();
    fetchGetActualValueToBagDetail(id)
      .then(res => {
        if (!res.error && res.data) {
          model.value = res.data;

          if (model.value.mediRoute && typeof model.value.mediRoute === 'string') {
            const routes = model.value.mediRoute.split(',');
            model.value.mediRoute = routes;
          }
        }
      })
      .finally(() => {
        endLoading();
      });
  }

  const currentItem = ref<Api.ProgramItem.ProgramItemRecord>({
    moduleId: '',
    moduleType: 0,
    name: '',
    orderNumber: 0,
    type: ItemType.labour
  });
  function getActualValueByItemId(id: string, itemDetail: Api.Program.ItemResponseData, index: number) {
    const day = itemDetail.tableData[index].day;
    const itemActualMap = itemDetail.tableData[index].itemActualMap;
    const actualItem = itemActualMap[id];

    currentItem.value = itemDetail.itemMap[id];
    if (actualItem) {
      model.value = actualItem;
      if (model.value.mediRoute && typeof model.value.mediRoute === 'string')
        model.value.mediRoute = model.value.mediRoute.split(',');
      model.value.dayNum = day;

      // 检验类型的数据从item中content获取
      if (currentItem.value.type === ItemType.labour) {
        // 首次从content赋予初始值
        if (!model.value.singleDose) model.value.singleDose = currentItem.value.content;
      }
    } else {
      const item = itemDetail.itemMap[id];
      model.value.singleDose = item.singleDose;
      model.value.unit = item.unit;
      if (item.mediRoute && typeof item.mediRoute === 'string') model.value.mediRoute = item.mediRoute.split(',');
      model.value.frequency = item.frequency;
      model.value.dayNum = day;
    }
  }

  function $reset() {
    model.value = { id: '' };
    currentItem.value = {
      moduleId: '',
      moduleType: 0,
      name: '',
      orderNumber: 0,
      type: ItemType.labour
    };
  }
  return {
    model,
    loading,
    addActualValue,
    getActualValue,
    getActualValueByItemId,
    updateActualValue,
    $reset,
    currentItem
  };
});
