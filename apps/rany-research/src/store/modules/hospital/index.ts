import { defineStore } from 'pinia';
import { ref } from 'vue';
import { SetupStoreId } from '@/enum';
import { fetchGetProvinceSubset, fetchPostDepartmentList } from '@/service/api';

export const useHosStore = defineStore(SetupStoreId.Region, () => {
  /** 省份列表 */
  const provinceList = ref<Api.Region.RegionList>([]);
  /** 城市列表 */
  const cityList = ref<Api.Region.RegionList>([]);
  /** 区县列表 */
  const districtList = ref<Api.Region.RegionList>([]);

  /**
   * 获取省市区列表
   *
   * @param type - 行政区域类型:1-省,2-市,3-区县,4-乡镇
   * @param parentId - 父级ID
   * @returns 省市区列表
   */
  async function getRegionList(type: number, parentId?: string) {
    const { error, data } = await fetchGetProvinceSubset(type, parentId);
    if (!error) {
      switch (type) {
        case 1:
          provinceList.value = data;
          break;
        case 2:
          cityList.value = data;
          break;
        case 3:
          districtList.value = data;
          break;
        default:
          break;
      }
      return data;
    }
    return [];
  }

  const departmentList = ref<Api.Dept.Department[]>([]);

  async function getDepartmentList(hospitalId: string) {
    const res: any = await fetchPostDepartmentList({ hospitalId, pageNo: 1, pageSize: 100 });
    departmentList.value = res.data.records;
  }

  return {
    provinceList,
    cityList,
    districtList,
    departmentList,
    getDepartmentList,
    getRegionList
  };
});
