import type { request } from '@/service/request';
import {
  fetchGetProgramCustomItemDelete,
  fetchGetProgramItemDelete,
  fetchGetProgramItemDetail,
  fetchGetProgramItemsDetail,
  fetchPostProgramCustomItemAdd,
  fetchPostProgramCustomItemDetail,
  fetchPostProgramCustomItemUpdate,
  fetchPostProgramItemAdd,
  fetchPostProgramItemUpdate,
  fetchPostProgramRowItemDelete
} from '@/service/api/program';
import type { DataSourceStrategy } from '../DataSourceStrategy';
import { transformData } from '../BaseTableDataTransformer';

export class ProgramDataSource implements DataSourceStrategy {
  getProgramDetail(id: string): ReturnType<typeof request<Api.Program.ItemResponseData>> {
    return fetchGetProgramItemsDetail(id);
  }

  deleteItem(id: string): ReturnType<typeof request<string | null>> {
    return fetchGetProgramItemDelete(id);
  }

  deleteRowItem(rowNum: number, solutionId: string): ReturnType<typeof request<string | null>> {
    return fetchPostProgramRowItemDelete(rowNum, solutionId);
  }

  detailItem(id: string): ReturnType<typeof request<Api.ProgramItem.ProgramItemRecord>> {
    return fetchGetProgramItemDetail(id);
  }
  addItem(data: Api.ProgramItem.ProgramItemRecord): ReturnType<typeof request<string | null>> {
    return fetchPostProgramItemAdd(data);
  }
  updateItem(data: Api.ProgramItem.ProgramItemRecord): ReturnType<typeof request<string | null>> {
    return fetchPostProgramItemUpdate(data);
  }

  transformData(data: any) {
    return transformData(data);
  }
  detailCustomItem(id: string): ReturnType<typeof request<Api.ProgramItem.ProgramItemRecord>> {
    return fetchPostProgramCustomItemDetail(id);
  }

  addCustomItem(data: Api.ProgramItem.AddCustomItemVO): ReturnType<typeof request<string | null>> {
    return fetchPostProgramCustomItemAdd(data);
  }
  deleteCustomItem(id: string): ReturnType<typeof request<string | null>> {
    return fetchGetProgramCustomItemDelete(id);
  }
  updateCustomItem(data: Api.ProgramItem.AddCustomItemVO): ReturnType<typeof request<string | null>> {
    return fetchPostProgramCustomItemUpdate(data);
  }
}
