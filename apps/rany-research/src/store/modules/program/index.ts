import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import type { SelectOption } from 'naive-ui';
import { SetupStoreId } from '@/enum';
import {
  fetchGetProgramDetail,
  fetchPostProgramAdd,
  fetchPostProgramCopy,
  fetchPostProgramDelete,
  fetchPostProgramList,
  fetchPostProgramUpdate
} from '@/service/api/program';
import { useLoading } from '~/packages/hooks/src';

export const useProgramStore = defineStore(SetupStoreId.Program, () => {
  const { loading: actionLoading, startLoading, endLoading } = useLoading();
  const Status = ref<Api.Program.Status>({
    deleted: -1,
    drafted: 0,
    published: 1,
    disabled: 2
  });
  const statusOptions = computed(() => [
    {
      label: '已停用',
      value: Status.value.disabled
    },
    {
      label: '已发布',
      value: Status.value.published
    },
    {
      label: '草稿',
      value: Status.value.drafted
    }
  ]);

  const statusLabel = (value?: number) => {
    const find = statusOptions.value.find(option => option.value === value);
    if (find) {
      return find.label;
    }
    return '';
  };
  const programModal = ref<Api.Program.ProgramItem>({
    status: 0,
    name: '未命名方案',
    riskId: ''
  });

  function restModel() {
    programModal.value = {
      status: 0,
      name: '未命名方案',
      diseaseId: '',
      riskId: '',
      comment: '',
      restriction: 1
    };
  }

  async function copyProgram(data: Api.Program.CopyProgramParams) {
    startLoading();
    const { error, data: responseData } = await fetchPostProgramCopy(data);
    if (!error) {
      window.$message?.success('方案已复制完成');
      endLoading();
      return responseData; // 返回复制后的方案ID
    }
    endLoading();
    return null;
  }

  async function getProgramDetail(id?: string) {
    if (id) {
      startLoading();
      const { error, data } = await fetchGetProgramDetail(id);
      if (!error) {
        programModal.value = data;
      }
      endLoading();
    }
  }
  async function updateProgram(isRestModel = true) {
    startLoading();
    const { error } = await fetchPostProgramUpdate(programModal.value);
    if (!error) {
      window.$message?.success('方案信息更新完成');
    }
    if (isRestModel) {
      restModel();
    }
    endLoading();
  }
  async function addProgram() {
    startLoading();
    const { error, data } = await fetchPostProgramAdd(programModal.value);
    if (!error) {
      window.$message?.success('方案已添加完成');
      return data;
    }
    restModel();
    endLoading();
    return null;
  }

  async function updateProgramStatus(isRestModel = true) {
    startLoading();
    const { error } = await fetchPostProgramUpdate({
      id: programModal.value.id,
      status: programModal.value.status
    });
    if (!error) {
      window.$message?.success('方案状态更新完成');
    }
    if (isRestModel) {
      restModel();
    }
    endLoading();
  }
  async function deleteProgram(id: string) {
    startLoading();
    const { error } = await fetchPostProgramDelete(id);
    if (!error) {
      window.$message?.success('方案已删除');
    }
    restModel();
    endLoading();
  }

  async function getPrograms(params: Api.Program.SearchParams) {
    startLoading();
    const { error, data } = await fetchPostProgramList(params);
    if (!error) {
      endLoading();
      return data;
    }
    endLoading();
    return null;
  }

  function loadProgramOptions() {
    const options = ref<SelectOption[]>([]);
    const load = async () => {
      const data = (await getPrograms({
        pageNo: -1,
        pageSize: 0,
        status: 1
      })) as unknown as Api.Program.ProgramItem[];
      if (data && !('records' in data)) {
        options.value = [];
        data.forEach(record => {
          options.value.push({
            label: record.name,
            value: record.id
          });
        });
      }
    };

    return { data: options, load, loading: actionLoading };
  }

  return {
    programModal,
    getPrograms,
    copyProgram,
    actionLoading,
    Status,
    statusOptions,
    statusLabel,
    getProgramDetail,
    restModel,
    updateProgram,
    updateProgramStatus,
    deleteProgram,
    addProgram,
    loadProgramOptions
  };
});
