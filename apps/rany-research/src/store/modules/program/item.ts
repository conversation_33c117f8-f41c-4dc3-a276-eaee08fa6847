import { defineStore } from 'pinia';
import { ref } from 'vue';
import { useLoading } from '~/packages/hooks/src';
import { fetchGetProgramItemDetail } from '@/service/api/program';
import { useSuiteStore } from '../database/suite';
import type { DataSourceStrategy } from '../DataSourceStrategy';
import { useProgramStore } from '.';
export const ItemType: Api.ProgramItem.ItemType = {
  labour: 1,
  examination: 2,
  medicine: 3,
  operation: 4,
  stack: 5
};

export const useItemStore = defineStore('program-item', () => {
  const comments = ref<string[]>([]);
  const programStore = useProgramStore();
  const suiteStore = useSuiteStore();

  const dataSource = ref<DataSourceStrategy>();

  const unitOptions = ref([
    { label: 'g', value: 'g' },
    { label: 'mg', value: 'mg' },
    { label: 'μg', value: 'μg' },
    { label: 'ml', value: 'ml' },
    { label: 'L', value: 'L' },
    { label: 'IU', value: 'IU' },
    { label: 'U', value: 'U' },
    { label: 'mg/㎡', value: 'mg/㎡' },
    { label: 'g/㎡', value: 'g/㎡' },
    { label: 'U/㎡', value: 'U/㎡' },
    { label: 'mg/㎡/day', value: 'mg/㎡/day' },
    { label: '次', value: '次' }
  ]);

  const Type = ref<Api.ProgramItem.ItemType>({
    labour: 1,
    examination: 2,
    medicine: 3,
    operation: 4,
    stack: 5
  });

  const ModuleType = ref<Api.ProgramItem.ModuleType>({
    plan: 0,
    program: 1,
    stack: 2
  });

  const itemModel = ref<Api.ProgramItem.ProgramItemRecord>({
    moduleId: '',
    moduleType: 0,
    name: '',
    refId: '',
    unit: '次',
    singleDose: 1,
    orderNumber: 0,
    type: Type.value.medicine
  });

  function $reset() {
    itemModel.value = {
      moduleId: '',
      moduleType: 0,
      unit: '次',
      name: '',
      refId: '',
      singleDose: 1,
      orderNumber: 0,
      type: Type.value.medicine
    };
  }

  function resetModel() {
    itemModel.value = {
      moduleId: '',
      moduleType: 0,
      name: '',
      orderNumber: 0,
      type: 3
    };
  }

  function getProgramDetail() {
    const model = ref<Api.Program.ItemResponseData>();
    const itemMap = ref<Record<string, Api.ProgramItem.ProgramItemRecord>>();
    const { startLoading, endLoading, loading } = useLoading();

    async function load(soluId: string, clinicPlanId?: string) {
      startLoading();
      if (dataSource.value) {
        const { data } = await dataSource.value.getProgramDetail(soluId, clinicPlanId);
        comments.value = data?.comments;
        if (data && dataSource.value.transformData) {
          itemMap.value = data.itemMap;
          const tableData = await dataSource.value.transformData(data);
          model.value = { ...data, tableData };
        }
      }

      endLoading();
      return model.value;
    }
    return { load, model, itemMap, loading };
  }

  async function initTypeOptions(setDefault?: boolean) {
    const res = await suiteStore.getItemType();
    if (res && setDefault && suiteStore.itemTypeList[0].value) {
      itemModel.value.type = suiteStore.itemTypeList[0].value as any;
    }
  }

  async function submitAddItemToProgram() {
    if (programStore.programModal?.id && dataSource.value && dataSource.value.addItem) {
      itemModel.value.moduleId = programStore.programModal.id;
      itemModel.value.moduleType = ModuleType.value.program;
      const res = await dataSource.value.addItem(itemModel.value);
      if (res?.data) {
        window.$message?.success('添加成功');
        resetModel();
        return true;
      }
      window.$message?.error('添加失败');
    }
    return false;
  }

  async function submitUpdateItemToProgram() {
    if (programStore.programModal?.id && dataSource.value && dataSource.value.updateItem) {
      const res = await dataSource.value.updateItem(itemModel.value);
      if (res?.data) {
        window.$message?.success('编辑成功');
        resetModel();
        return true;
      }
      window.$message?.error('编辑失败');
    }
    return false;
  }

  async function deleteItemToProgram(id: string) {
    if (dataSource.value?.deleteItem) {
      const { error } = await dataSource.value.deleteItem(id);
      if (!error) {
        window.$message?.success('删除成功');
        return true;
      }
    }
    window.$message?.error('删除失败');
    return false;
  }

  async function deleteRowItemToProgram(rowIndex: number, soluId: string) {
    if (dataSource.value?.deleteRowItem) {
      const { error } = await dataSource.value.deleteRowItem(rowIndex, soluId);
      if (!error) {
        window.$message?.success('删除成功');
        return true;
      }
      window.$message?.error('删除失败');
      return false;
    }
    window.$message?.error('方案不存在');
    return false;
  }

  async function getProgramItemDetail(id: string) {
    if (dataSource.value?.detailItem) {
      const { error, data } = await dataSource.value.detailItem(id);
      if (!error) {
        itemModel.value = data;
        itemModel.value.comment = '';
        if (itemModel.value.type !== ItemType.medicine) {
          itemModel.value.unit = '次';
        }
        if (itemModel.value.mediRoute && typeof itemModel.value.mediRoute === 'string') {
          // 后端返回的是逗号分隔的字符串，需要转换成字符串数组以匹配前端组件的数据类型
          itemModel.value.mediRoute = itemModel.value.mediRoute.split(',');
        }
        return itemModel.value;
      }
    }
    window.$message?.error('获取详情失败');
    return false;
  }
  async function getProgramCustomItemDetail(id: string) {
    if (dataSource.value?.detailCustomItem) {
      const { error, data } = await dataSource.value.detailCustomItem(id);
      if (!error) {
        itemModel.value = data;
        if (itemModel.value.type !== ItemType.medicine) {
          itemModel.value.unit = '次';
        }
        if (itemModel.value.mediRoute && typeof itemModel.value.mediRoute === 'string') {
          // 后端返回的是逗号分隔的字符串，需要转换成字符串数组以匹配前端组件的数据类型
          itemModel.value.mediRoute = itemModel.value.mediRoute.split(',');
        }
        return itemModel.value;
      }
    }
    window.$message?.error('获取详情失败');
    return false;
  }

  async function getProgramItemDetailOfAll(id: string) {
    if (id) {
      const res: any = await fetchGetProgramItemDetail(id);
      if (res.data) {
        itemModel.value = res.data;
        return itemModel.value;
      }
    }
    window.$message?.error('获取详情失败');
    return false;
  }

  async function addProgramCustomItem() {
    if (itemModel.value.mediRoute && Array.isArray(itemModel.value.mediRoute)) {
      itemModel.value.mediRoute = (itemModel.value.mediRoute as string[]).join(',');
    }
    if (dataSource.value?.addCustomItem) {
      const { error } = await dataSource.value.addCustomItem({
        itemId: itemModel.value.id as string,
        days: itemModel.value.mediPlan as string,
        frequency: itemModel.value.frequency,
        mediRoute: itemModel.value.mediRoute,
        comment: itemModel.value.comment,
        singleDose: itemModel.value.singleDose
      });
      if (!error) {
        return true;
      }
    }
    return false;
  }

  async function updateProgramCustomItem() {
    if (itemModel.value.mediRoute && Array.isArray(itemModel.value.mediRoute)) {
      itemModel.value.mediRoute = itemModel.value.mediRoute.join(',');
    }
    if (dataSource.value && dataSource.value.updateCustomItem) {
      const { error } = await dataSource.value.updateCustomItem({
        id: itemModel.value.id,
        itemId: itemModel.value.itemId as string,
        days: itemModel.value.mediPlan as string,
        frequency: itemModel.value.frequency,
        mediRoute: itemModel.value.mediRoute,
        comment: itemModel.value.comment,
        singleDose: itemModel.value.singleDose
      });
      if (!error) {
        return true;
      }
    }
    return false;
  }

  async function setItemDetailById(id: string, itemDetail: Api.Program.ItemResponseData, index: number) {
    const day = itemDetail.tableData[index].day;
    const itemCustomMap = itemDetail.tableData[index].itemCustomMap;
    const ob = itemCustomMap[id];

    if (ob && ob.id) {
      itemModel.value = ob;

      await getProgramCustomItemDetail(ob.id);
      // itemModel.value.mediPlan = day.toString();
      if (itemModel.value.itemId && itemDetail.itemMap) {
        const oriItem = itemDetail.itemMap[itemModel.value.itemId];
        itemModel.value = Object.assign(oriItem, itemModel.value);
      }
      itemModel.value.mediPlan = day.toString();
      if (itemModel.value.singleDose) {
        itemModel.value.singleDose = Number.parseInt(itemModel.value.singleDose as any, 10);
      }
    } else {
      await getProgramItemDetail(id);
      itemModel.value.mediPlan = day.toString();
    }
  }

  return {
    Type,
    unitOptions,
    comments,
    getProgramDetail,
    submitAddItemToProgram,
    deleteRowItemToProgram,
    submitUpdateItemToProgram,
    getProgramItemDetail,
    deleteItemToProgram,
    initTypeOptions,
    setItemDetailById,
    getProgramItemDetailOfAll,
    addProgramCustomItem,
    updateProgramCustomItem,
    itemModel,
    ModuleType,
    dataSource,
    $reset
  };
});
