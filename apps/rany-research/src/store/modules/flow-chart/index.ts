import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

// 定义节点类型
export type NodeType = 'default' | 'induction' | 'consolidation' | 'intensification1' | 'intensification2';

// 定义节点基础数据结构
export interface BaseNodeData {
  type?: NodeType;
  topLabel?: string; // 上方盒子显示的文本
}

// 普通节点数据
export interface TreatmentNodeData extends BaseNodeData {
  label: string;
  days?: string;
  hasGroup?: boolean;
  groupTitle?: string;
}

// 分组节点数据
export interface GroupNodeData extends BaseNodeData {
  title: string;
  nodes: Array<{ id: string; data: { label: string; days?: string } }>;
}

// 定义节点结构
export interface FlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: TreatmentNodeData | GroupNodeData;
}

// 定义树节点结构
export interface TreeNode {
  key: string;
  label: string;
  children?: TreeNode[];
  isLeaf?: boolean;
  type?: 'folder' | 'file';
  nodeId?: string; // 关联的流程图节点ID
  days?: string;
  [key: string]: any;
}

// 定义边结构
export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  animated?: boolean;
  type?: string;
  sourceHandle?: string;
  targetHandle?: string;
  style?: any;
}

// 类型检查函数
export function isGroupNodeData(data: any): data is GroupNodeData {
  return 'nodes' in data && Array.isArray(data.nodes);
}

export const useFlowChartStore = defineStore('flowChart', () => {
  // 状态
  const selectedNodeId = ref<string | null>(null);
  const selectedTreeKeys = ref<string[]>([]);
  const expandedTreeKeys = ref<string[]>(['outline-1', 'schemes-1', 'plans-1']);

  // 流程图节点数据
  const flowNodes = ref<FlowNode[]>([]);

  // 流程图边数据
  const flowEdges = ref<FlowEdge[]>([]);

  // 树形数据
  const treeData = ref<{
    outline: TreeNode[];
    schemes: TreeNode[];
    plans: TreeNode[];
  }>({
    outline: [],
    schemes: [],
    plans: []
  });

  // 计算属性
  const selectedNode = computed(() => {
    if (!selectedNodeId.value) return null;
    return flowNodes.value.find(node => node.id === selectedNodeId.value);
  });

  // 根据tab获取树形数据
  const getTreeDataByTab = (tab: string): TreeNode[] => {
    return treeData.value[tab as keyof typeof treeData.value] || treeData.value.outline;
  };

  // 过滤树形数据
  const getFilteredTreeData = (tab: string, searchValue: string): TreeNode[] => {
    const data = getTreeDataByTab(tab);
    if (!searchValue) return data;

    const filterNodes = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.reduce((acc: TreeNode[], node) => {
        const matchesSearch = node.label.toLowerCase().includes(searchValue.toLowerCase());
        const filteredChildren = node.children ? filterNodes(node.children) : [];

        if (matchesSearch || filteredChildren.length > 0) {
          acc.push({
            ...node,
            children: filteredChildren.length > 0 ? filteredChildren : node.children
          });
        }

        return acc;
      }, []);
    };

    return filterNodes(data);
  };

  // Actions
  const setSelectedNodeId = (nodeId: string | null) => {
    selectedNodeId.value = nodeId;
  };

  const setSelectedTreeKeys = (keys: string[]) => {
    selectedTreeKeys.value = keys;
  };

  const setExpandedTreeKeys = (keys: string[]) => {
    expandedTreeKeys.value = keys;
  };

  const updateNodePosition = (nodeId: string, position: { x: number; y: number }) => {
    const node = flowNodes.value.find(n => n.id === nodeId);
    if (node) {
      node.position = position;
    }
  };

  // 从流程图节点生成目录树数据
  const generateTreeDataFromNodes = () => {
    const treeNodes: TreeNode[] = [];

    // 遍历流程图节点
    flowNodes.value.forEach(node => {
      if (node.type === 'groupNode' || node.type === 'group') {
        // 确保是分组节点
        if (isGroupNodeData(node.data)) {
          const groupData = node.data as GroupNodeData;

          // 创建父节点（第一层）
          const parentNode: TreeNode = {
            key: `outline-${node.id}`,
            label: groupData.topLabel || groupData.title,
            type: 'folder',
            nodeId: node.id,
            children: []
          };

          // 添加子节点（第二层）
          if (groupData.nodes && Array.isArray(groupData.nodes)) {
            groupData.nodes.forEach((childNode, index) => {
              const childKey = `outline-${node.id}-${index}`;
              parentNode.children?.push({
                key: childKey,
                label: childNode.data.label,
                type: 'file',
                isLeaf: true,
                nodeId: `${node.id}-child-${index}`,
                days: childNode.data.days || ''
              });
            });
          }

          treeNodes.push(parentNode);
        }
      } else if (node.type === 'treatmentNode') {
        // 处理单个治疗节点
        const nodeData = node.data as TreatmentNodeData;

        // 创建父节点
        const parentNode: TreeNode = {
          key: `outline-${node.id}`,
          label: nodeData.topLabel || nodeData.groupTitle || nodeData.label,
          type: 'folder',
          nodeId: node.id,
          children: [
            {
              key: `outline-${node.id}-0`,
              label: nodeData.label,
              type: 'file',
              isLeaf: true,
              nodeId: node.id,
              days: nodeData.days || ''
            }
          ]
        };

        treeNodes.push(parentNode);
      }
    });

    return treeNodes;
  };

  // 同步流程图节点到目录树
  const syncNodesToTreeData = () => {
    const outlineData = generateTreeDataFromNodes();
    treeData.value.outline = outlineData;
  };

  // 添加节点时同步更新树
  const addNode = (node: FlowNode) => {
    flowNodes.value.push(node);
    syncNodesToTreeData();
  };

  // 删除节点时同步更新树
  const removeNode = (nodeId: string) => {
    const index = flowNodes.value.findIndex(n => n.id === nodeId);
    if (index > -1) {
      flowNodes.value.splice(index, 1);
      syncNodesToTreeData();
    }
  };

  // 更新节点数据时同步更新树
  const updateNodeData = (nodeId: string, data: Partial<TreatmentNodeData | GroupNodeData>) => {
    const node = flowNodes.value.find(n => n.id === nodeId);
    if (node) {
      node.data = { ...node.data, ...data };
      syncNodesToTreeData();
    }
  };

  const addEdge = (edge: FlowEdge) => {
    flowEdges.value.push(edge);
  };

  const removeEdge = (edgeId: string) => {
    const index = flowEdges.value.findIndex(e => e.id === edgeId);
    if (index > -1) {
      flowEdges.value.splice(index, 1);
    }
  };

  // 重置节点和边
  const resetNodesAndEdges = () => {
    flowNodes.value = [];
    flowEdges.value = [];
    // 重置选中状态
    selectedNodeId.value = null;
    selectedTreeKeys.value = [];
    // 同步更新目录树数据
    syncNodesToTreeData();
  };

  // 根据树节点选择高亮对应的流程图节点
  const highlightNodeFromTree = (treeKey: string) => {
    const findNodeId = (nodes: TreeNode[], key: string): string | null => {
      for (const node of nodes) {
        if (node.key === key && node.nodeId) {
          return node.nodeId;
        }
        if (node.children) {
          const found = findNodeId(node.children, key);
          if (found) return found;
        }
      }
      return null;
    };

    // 在所有树数据中查找
    for (const tabData of Object.values(treeData.value)) {
      const nodeId = findNodeId(tabData, treeKey);
      if (nodeId) {
        setSelectedNodeId(nodeId);
        return;
      }
    }
  };

  // 本地添加大纲数据项（用于拖拽添加后的本地更新）
  const addOutlineItem = (nodeId: string, nodeData: any) => {
    // 根据节点数据生成新的大纲项
    const newOutlineItem: TreeNode = {
      key: `outline-${nodeId}`,
      label: nodeData.topLabel || nodeData.groupTitle || nodeData.label,
      type: 'folder',
      nodeId: nodeId,
      children: nodeData.nodes ? nodeData.nodes.map((child: any, index: number) => ({
        key: `outline-${nodeId}-${index}`,
        label: child.label,
        type: 'file',
        isLeaf: true,
        nodeId: nodeId,
        days: child.days || ''
      })) : [{
        key: `outline-${nodeId}-0`,
        label: nodeData.label,
        type: 'file',
        isLeaf: true,
        nodeId: nodeId,
        days: nodeData.days || ''
      }]
    };

    // 添加到大纲数据中
    treeData.value.outline.push(newOutlineItem);
  };

  // 本地删除大纲数据项（用于删除节点后的本地更新）
  const removeOutlineItem = (nodeId: string) => {
    // 从大纲数据中删除对应的项
    const index = treeData.value.outline.findIndex(item => item.nodeId === nodeId);
    if (index > -1) {
      treeData.value.outline.splice(index, 1);
    }
  };

  return {
    // 状态
    selectedNodeId,
    selectedTreeKeys,
    expandedTreeKeys,
    flowNodes,
    flowEdges,
    treeData,

    // 计算属性
    selectedNode,

    // 方法
    getTreeDataByTab,
    getFilteredTreeData,
    setSelectedNodeId,
    setSelectedTreeKeys,
    setExpandedTreeKeys,
    updateNodePosition,
    updateNodeData,
    addNode,
    removeNode,
    addEdge,
    removeEdge,
    highlightNodeFromTree,
    syncNodesToTreeData,
    resetNodesAndEdges,
    addOutlineItem,
    removeOutlineItem
  };
});
