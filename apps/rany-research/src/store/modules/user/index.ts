import { defineStore } from 'pinia';

export enum RoleType {
  SYSTEM = '0', // 系统
  NORMAL = '1', // 普通用户
  SPECIALIST = '2', // 专家用户
  HOSPITAL = '3' // 医院用户
}

export enum ExpertType {
  SJEXPERT = 0,
  EXPERT = 1,
  HOSPITAL = '3' // 医院用户
}

interface State {
  token: string;
  userName: string;
  type?: RoleType;
  id: string; // user id]
  permissions?: string[];
  user?: any;
  roles?: Role[];
  settings: Settings;
}

interface Role {
  createTime: string;
  dataPermit: number;
  department: number;
  isSystem: number;
  modifyTime: string;
  remark: string;
  roleId: number;
  roleName: string;
}

interface Settings {
  createTime: string;
  faceStatus: string;
  fingerprintStatus: string;
  modifyTime: string;
  userId: string;
  wardId: number;
  wardName: string;
}

const defaultState: State = {
  user: {},
  roles: [],
  token: '',
  settings: {} as Settings,
  type: RoleType.NORMAL,
  userName: '',
  id: '',
  permissions: []
};

export const useUserStore = defineStore('user', {
  state: (): State => ({ ...defaultState }),
  persist: true,

  getters: {
    isLogin: (state): boolean => {
      return Boolean(state.token);
    }
  },

  actions: {
    setToken(token: string) {
      this.token = token;
    },

    setUserInfo(userInfo: Partial<State>) {
      Object.assign(this, userInfo);
    },

    resetState() {
      Object.assign(this, defaultState);
    }
  }
});
