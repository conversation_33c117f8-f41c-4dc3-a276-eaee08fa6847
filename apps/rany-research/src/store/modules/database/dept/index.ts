import { defineStore } from 'pinia';
import { ref } from 'vue';
import { SetupStoreId } from '@/enum';
import { fetchPostDepartmentAdd, fetchPostDepartmentList } from '@/service/api';

export const useDeptStore = defineStore(SetupStoreId.Dept, () => {
  /**
   * 获取科室列表
   *
   * @returns 科室列表
   */
  const departmentList = ref<Api.Dept.Department[]>();
  async function getDepartmentList(hospitalId: string | undefined) {
    const { error, data } = await fetchPostDepartmentList({ hospitalId, pageNo: 1, pageSize: 10 });
    if (!error) {
      departmentList.value = data.records;
      return true;
    }
    return false;
  }

  /**
   * 新增科室
   *
   * @param data 新增科室数据
   */
  async function addDepartment(val: Api.Dept.Department & Api.Common.BaseRequestParams) {
    const { error, data } = await fetchPostDepartmentAdd(val);
    return {
      error,
      data
    };
  }

  return {
    departmentList,
    getDepartmentList,
    addDepartment
  };
});
