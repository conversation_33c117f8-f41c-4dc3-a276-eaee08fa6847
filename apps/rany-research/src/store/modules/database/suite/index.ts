import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { SelectOption } from 'naive-ui';
import { SetupStoreId } from '@/enum';
import {
  fetchGetSuiteItemDel,
  fetchGetSuiteItemList,
  fetchGetSuiteItemType,
  fetchPostSuiteAdd,
  fetchPostSuiteItemAdd,
  fetchPostSuiteItemUpdate,
  fetchPostSuiteList,
  fetchPostSuiteUpdate
} from '@/service/api';
import { useLoading } from '~/packages/hooks/src';

export const useSuiteStore = defineStore(SetupStoreId.Suite, () => {
  /**
   * 新增项目
   *
   * @param suite - 项目信息
   * @returns 是否新增成功
   */
  async function addSuite(suite: Api.Suite.Suite & Api.Common.BaseRequestParams) {
    const { error, response } = await fetchPostSuiteAdd(suite);
    if (!error) {
      return response;
    }
    return false;
  }
  /**
   * 更新项目
   *
   * @param suite - 项目信息
   * @returns 是否更新成功
   */
  async function updateSuite(suite: Api.Suite.Suite & Api.Common.BaseRequestParams) {
    const { error } = await fetchPostSuiteUpdate(suite);
    if (!error) {
      return true;
    }
    return false;
  }
  /**
   * 获取项目类别
   *
   * @returns 项目类别
   */
  const itemTypeList = ref<SelectOption[]>([]);
  async function getItemType() {
    const { error, data } = await fetchGetSuiteItemType();
    if (!error) {
      itemTypeList.value = data.map(item => ({
        label: item.text,
        value: item.value
      }));
      return true;
    }
    return false;
  }
  /**
   * 添加组套详情-小项
   *
   * @param suiteItem - 小项信息
   * @returns 是否添加成功
   */
  async function addSuiteItem(suiteItem: Api.Suite.SuiteItem) {
    const { error } = await fetchPostSuiteItemAdd(suiteItem);
    if (!error) {
      return true;
    }
    return false;
  }

  /** 组套列表-小项 */
  const suiteItemList = ref<Api.Suite.SuiteItem[]>([]);
  /**
   * 获取组套列表-小项
   *
   * @param moduleId - 组套ID
   * @returns 组套列表-小项
   */
  async function getSuiteItemList(moduleId: string) {
    const { error, data } = await fetchGetSuiteItemList(moduleId);
    if (!error) {
      suiteItemList.value = data.filter((item: any) => item.delFlag === 0);
      return true;
    }
    return [];
  }
  /**
   * 更新组套详情-小项
   *
   * @param suiteItem - 小项信息
   * @returns 是否更新成功
   */
  async function updateSuiteItem(suiteItem: Api.Suite.SuiteItem) {
    const { error } = await fetchPostSuiteItemUpdate(suiteItem);
    if (!error) {
      return true;
    }
    return false;
  }

  /**
   * Creates a set of variables for loading options of a select/dropdown component. The function returns an object with
   * three properties:
   *
   * - `loading`: a boolean indicating whether the options are being loaded
   * - `options`: an array of options
   * - `load`: a function that loads the options
   *
   * @param fetchFunction a function that returns a promise of an object with two properties: `error` and `data`
   * @param mapFunction a function that maps an item of the `data` array to a select option
   * @returns an object with the three properties mentioned above
   */
  function createOptionsLoader(
    fetchFunction: () => Promise<{ error: any; data: any }>,
    mapFunction: (item: any) => SelectOption
  ) {
    const { loading, startLoading, endLoading } = useLoading();
    const options = ref<SelectOption[]>([]);
    const load = async () => {
      startLoading();
      if (options.value.length <= 0) {
        const { error, data } = await fetchFunction();
        if (error) {
          window.$dialog?.error({ content: error.message });
        }
        if (data) {
          options.value = data.records.map(mapFunction);
        }
      }
      endLoading();
    };

    return {
      loading,
      options,
      load
    };
  }

  function getSuitOptions() {
    return createOptionsLoader(
      () => fetchPostSuiteList({ pageNo: -1, pageSize: 100 }),
      (item: Api.Suite.Suite) => ({
        label: item.name ?? '',
        value: item.id
      })
    );
  }
  /**
   * 删除组套
   *
   * @param id - 组套ID
   * @returns 是否删除成功
   */
  async function deleteSuiteItem(id: string) {
    const { error } = await fetchGetSuiteItemDel(id);
    if (!error) {
      return true;
    }
    return false;
  }

  return {
    addSuite,
    updateSuite,
    itemTypeList,
    deleteSuiteItem,
    getSuitOptions,
    suiteItemList,
    updateSuiteItem,
    getSuiteItemList,
    getItemType,
    addSuiteItem
  };
});
