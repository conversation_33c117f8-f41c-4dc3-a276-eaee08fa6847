import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createPinia, setActivePinia } from 'pinia';
import { fetchPostSuiteAdd } from '@/service/api';
import { useSuiteStore } from './index';

// Mock the API call
vi.mock('@/service/api', () => ({
  fetchPostSuiteAdd: vi.fn()
}));

describe('useSuiteStore', () => {
  beforeEach(() => {
    // 创建一个新的 Pinia 实例并使其激活
    setActivePinia(createPinia());
  });

  describe('addSuite', () => {
    it('应该在API调用成功时返回true', async () => {
      // 准备
      const mockSuite = {
        id: '1',
        name: 'Test Suite',
        type: 'test',
        departmentId: '1',
        status: 1,
        userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
        userName: 'admin'
      } as Api.Suite.Suite & Api.Common.BaseRequestParams;
      const mockResponse: any = {
        data: {} as Api.Common.CommonResponseType,
        error: null
      };
      vi.mocked(fetchPostSuiteAdd).mockResolvedValue(mockResponse);

      // 执行
      const store = useSuiteStore();
      const result = await store.addSuite(mockSuite);

      // 断言
      expect(fetchPostSuiteAdd).toHaveBeenCalledWith(mockSuite);
      expect(result).toBe(true);
    });

    it('应该在API调用失败时返回false', async () => {
      // 准备
      const mockSuite = {
        id: '1',
        name: 'Test Suite',
        type: 'test',
        departmentId: '1',
        status: 1,
        userId: 'ed2bcf1d7a584a69bf99bd0df530475a',
        userName: 'admin'
      } as Api.Suite.Suite & Api.Common.BaseRequestParams;
      const mockResponse: any = {
        data: {} as Api.Common.CommonResponseType,
        error: null
      };
      vi.mocked(fetchPostSuiteAdd).mockResolvedValue(mockResponse);

      // 执行
      const store = useSuiteStore();
      const result = await store.addSuite(mockSuite);

      // 断言
      expect(fetchPostSuiteAdd).toHaveBeenCalledWith(mockSuite);
      expect(result).toBe(false);
    });
  });
});
