import { ref } from 'vue';
import { defineStore } from 'pinia';
import { SetupStoreId } from '@/enum';
import { fetchGetDiseaseList, fetchGetDiseaseTree } from '@/service/api';

export const useDiseaseStore = defineStore(SetupStoreId.Disease, () => {
  const diseaseList = ref<Api.Common.CommonOption[]>([]);

  async function getDiseaseList() {
    const { data, error } = await fetchGetDiseaseList({
      pageNo: 1,
      pageSize: 1000
    });
    if (!error) {
      diseaseList.value = data.records.map(item => ({
        label: item.shortName,
        value: item.id
      })) as Api.Common.CommonOption[];
    }
  }

  const diseaseTree = ref<Api.Common.CascaderOption[]>([]);
  const getDiseaseTree = async () => {
    const { data, error } = await fetchGetDiseaseTree();
    if (!error) {
      const formatDiseaseTree = (items: any) => {
        return items.map((item: any) => ({
          label: item.shortName,
          value: item.id,
          children: item.childList ? formatDiseaseTree(item.childList) : undefined,
          isLeaf: !item.childList?.length
        }));
      };
      diseaseTree.value = formatDiseaseTree(data);
    }
  };

  return {
    diseaseList,
    diseaseTree,
    getDiseaseList,
    getDiseaseTree
  };
});
