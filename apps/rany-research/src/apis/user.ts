import type { LoginForm, UserProfile } from '@/types/user';
import { request } from '@/utils/requestApi';
import type { PaginatedResponse } from '@/types/api';
import type { Params } from '@/types/hospital-user';
import type { HospitalUser } from '@/components/hospital-user/HospitalUserTable';

/**
 * 登录接口
 *
 * @param loginForm
 */
export const loginApi = (loginForm: LoginForm) => {
  return request<string>('/login', 'GET', loginForm);
};

/** 获取用户信息 用户名 */
export const getUserProfileApi = () => {
  return request<UserProfile>('/sys/profile', 'POST');
};

// 获取医院用户列表
export const getUserList = (params: Params) => {
  return request<Params, PaginatedResponse<HospitalUser>>(`/usermain/user/userlist`, 'POST', params);
};

// 添加用户
export const addUserApi = (params: any) => {
  return request<any>(`/regist`, 'POST', params);
};
// 修改用户信息
export const updateUserApi = (params: any) => {
  return request<any, any>(`/usermain/user/update/info`, 'POST', params);
};

// 启用用户
export const enableUserApi = (id: any) => {
  return request<any>(`/usermain/user/enable/${id}`, 'GET');
};

// 禁用用户
export const disableUserApi = (id: any) => {
  return request<any>(`/usermain/user/disable/${id}`, 'GET');
};

// 用户重置密码
export const resetPasswordUserApi = (username: any) => {
  return request<string, number>(`/usermain/user/pwd/reset/${username}`, 'POST');
};

// 忘记密码
export const forgotPassword = (data: any) => {
  return request<any>(`/forgot/pwd`, 'POST', data);
};

// 获取验证码
export const smsCode = (data: any) => {
  return request<any>(`/usermain/user/get/code`, 'POST', data);
};

// 获取/usermain/user/{userId} 用户信息
export const getUserInfo = (userId: string) => {
  return request(`/usermain/user/${userId}`, 'GET');
};

// 修改/usermain/user/update/info post
export const updateUserInfo = (data: any) => {
  return request('/usermain/user/update/info', 'POST', data);
};

export const verifyMobile = (body: { mobile?: string; code?: string }) => {
  return request('/usermain/user/mobile/verification', 'POST', body, {
    errorMessageShow: false
  });
};

export const wardList = (body: { pageNo?: number; pageSize?: number; status?: string; parentId: number }) => {
  const defaultParams = {
    pageNo: 1,
    pageSize: 999,
    status: '1', // 0 disabled, 1 valid,
    parentId: 0 // root level
  };
  Object.assign(defaultParams, body);
  return request('/inpatient/list', 'POST', defaultParams);
};

// 以下是从 data-platform 项目补充的 API 接口

// 获取用户角色信息
export const getUserRoles = (userId: string) => {
  return request(`/usermain/user/roles/${userId}`, 'GET');
};

// 设置用户角色
export const setUserRoles = (data: { userId: string; roleIds: string[] }) => {
  return request('/usermain/user/roles', 'POST', data);
};

// 批量删除用户
export const batchDeleteUsers = (userIds: string[]) => {
  return request('/usermain/user/batch/delete', 'POST', { userIds });
};

// 批量启用用户
export const batchEnableUsers = (userIds: string[]) => {
  return request('/usermain/user/batch/enable', 'POST', { userIds });
};

// 批量禁用用户
export const batchDisableUsers = (userIds: string[]) => {
  return request('/usermain/user/batch/disable', 'POST', { userIds });
};

// 导出用户列表
export const exportUserList = (params: any) => {
  return request('/usermain/user/export', 'POST', params, {
    timeout: 120000 // 2分钟超时
  });
};

// 导入用户
export const importUsers = (file: File) => {
  return request('/usermain/user/import', 'POST', file);
};

// 检查用户名是否存在
export const checkUserNameExists = (userName: string) => {
  return request(`/usermain/user/check/username/${userName}`, 'GET');
};

// 检查手机号是否存在
export const checkMobileExists = (mobile: string) => {
  return request(`/usermain/user/check/mobile/${mobile}`, 'GET');
};

// 检查邮箱是否存在
export const checkEmailExists = (email: string) => {
  return request(`/usermain/user/check/email/${email}`, 'GET');
};
