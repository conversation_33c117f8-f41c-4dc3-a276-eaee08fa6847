import { request } from '@/utils/requestApi';
import type { Role } from '@/types/role';

/** 获取所有角色 */
export const getRoleList = () => {
  return request('/usermain/role', 'GET');
};

/** 成员维护 */
export const setRoleUser = (data: any) => {
  return request('/usermain/role', 'PUT', data);
};

/** 获取所有角色带用户 */
export const getRoleListHasUser = (data: any) => {
  return request('/usermain/role/roleAndUserList', 'POST', data);
};

/** 新建角色 */
export const addRole = (data: Role) => {
  return request('/usermain/role', 'POST', data);
};

/** 更新角色 */
export const updateRole = (data: Role) => {
  return request('/usermain/role', 'PUT', data);
};

/** 删除角色 */
export const deleteRole = (id: string) => {
  return request(`/usermain/role/${id}`, 'DELETE');
};

/** 获取角色信息 */
export const getRoleInfo = (id: string) => {
  return request(`/usermain/role/info/${id}`, 'GET');
};

/** 获取子系统结构 */
export const getSubset = () => {
  return request('/usermain/menu/subset', 'GET');
};

/** 获取角色权限 */
export const getRolePermissions = (roleId: string) => {
  return request(`/usermain/role/permissions/${roleId}`, 'GET');
};

/** 设置角色权限 */
export const setRolePermissions = (data: { roleId: string; permissions: string[] }) => {
  return request('/usermain/role/permissions', 'POST', data);
};

/** 获取角色用户列表 */
export const getRoleUsers = (roleId: string) => {
  return request(`/usermain/role/users/${roleId}`, 'GET');
};

/** 批量删除角色 */
export const batchDeleteRoles = (roleIds: string[]) => {
  return request('/usermain/role/batch/delete', 'POST', { roleIds });
};

/** 启用角色 */
export const enableRole = (id: string) => {
  return request(`/usermain/role/enable/${id}`, 'GET');
};

/** 禁用角色 */
export const disableRole = (id: string) => {
  return request(`/usermain/role/disable/${id}`, 'GET');
};
