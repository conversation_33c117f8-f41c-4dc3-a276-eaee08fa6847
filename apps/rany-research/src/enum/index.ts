export enum SetupStoreId {
  App = 'app-store',
  Theme = 'theme-store',
  Auth = 'auth-store',
  Route = 'route-store',
  Tab = 'tab-store',
  Patient = 'patient-store',
  Disease = 'disease-store',
  Suite = 'suite-store',
  Risk = 'risk-store',
  Hospital = 'hospital-store',
  Dept = 'dept-store',
  Region = 'region-store',
  Experiment = 'experiment-store',
  Program = 'program-store',
  Platform = 'platform-store',
  Plan = 'clinic-plan-store',
  Stage = 'clinic-stage-store',
  Bag = 'bag-store',
  Actual = 'actual-store'
}
