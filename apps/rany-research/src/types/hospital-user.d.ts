declare module 'HospitalUser' {
  export interface Params {
    pageNo: number;
    pageSize: number;
    userName?: string;
    nickName?: string;
    mobile?: string;
    status?: string;
    type?: string;
    department?: number;
    wardId?: number;
  }

  export interface HospitalUserRecord {
    userId?: string;
    userName?: string;
    password?: string;
    email?: string;
    mobile?: string;
    status?: string;
    createTime?: string;
    modifyTime?: string;
    lastLoginTime?: string;
    description?: string;
    avatar?: string;
    nickName?: string;
    type?: string;
    department?: number[];
    smsSystem?: boolean;
    smsTodo?: boolean;
    emailSendMark?: boolean;
    wardId?: number | null;
    pwdStatus?: number;
    faceStatus?: string;
    fingerprintStatus?: string;
    sex?: number;
    deptName?: string;
    roleId?: string[];
    hospitalCollectionId?: string;
    institutionId?: number;
    title?: string;
  }
}

// 导出类型，用于组件和 API 中使用
export type HospitalUser = HospitalUser.HospitalUserRecord;
export type Params = HospitalUser.Params;
