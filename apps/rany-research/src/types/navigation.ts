// 导航类型
export type NavType = 'collapsed' | 'expanded';

// Tab 类型
export interface Tab {
  id: string;
  title: string;
  path: string;
  closable: boolean;
  cache?: boolean; // 是否需要缓存，默认为true
  meta?: {
    icon?: string;
    keepAlive?: boolean;
    [key: string]: any;
  };
}

// 菜单项类型
export interface MenuItem {
  id: string;
  title: string;
  path?: string;
  icon?: string;
  children?: MenuItem[];
  type?: string;
}

// 导航项类型
export interface NavItem {
  id: string;
  title: string;
  icon: string;
  path: string;
  type: 'link' | 'toggle';
}

// 工具导航项类型
export interface UtilityNavItem {
  id: string;
  title: string;
  icon: string;
  type: 'link' | 'action';
}
