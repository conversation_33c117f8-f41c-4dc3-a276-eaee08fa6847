// 登录参数
export type LoginForm = {
  username: string;
  password: string;
};

interface Role {
  roleId: number;
  roleName: string;
  remark: string;
  createTime: string;
  modifyTime: string;
  isSystem: number;
  dataPermit: number;
  department: number;
}

export interface UserRoles {
  roles: Role[];
}

export type UserProfile = {
  userId: string;
  userName: string;
  email: string;
  mobile: string;
  status: string;
  createTime: string;
  modifyTime: string;
  lastLoginTime: string;
  description: string;
  avatar: string;
  nickName: string;
  type: string;
  department: number;
  smsSystem: boolean;
  smsTodo: boolean;
  emailSendMark: boolean;
  wardId: number;
  pwdStatus: number;
  title: string;
  faceStatus: string;
  fingerprintStatus: string;
  institutionId?: number;
  sex?: number;
};

export type UserInfo = {
  staffPhoto: string;
  id: string;
  mobile: string;
  username: string;
  password: string;
  timeOfEntry: string;
  workNumber: string;
  correctionTime: string;
  departmentName: string;
  roleIds: string[];
  companyId: string;
  companyName: string;
};

export type PatientHosRecord = {
  id: string;
  hospId: string;
  admissionTime: string;
  dischargeTime: string;
  payMethod: string;
  contactName: string;
  contactPhone: string;
  remark: string;
  createBy: string;
  createTime: string;
  updateBy: string;
  updateTime: string;
  delFlag: number;
  paId: string;
  hospType: number;
  hosbarcode: string;
  admissionNumber: string;
  visitNumber: null | string;
  outpatientNumber: null | string;
  recordNumber: null | string;
  admiDayCount: number;
  bussTime: string;
  actions?: PatientHosRecord[];
  operName?: string;
  applyItem?: string;
  actionTime?: string;
  inpatientArea?: string;
  actionNumber?: number;

  // For web use
  actionForWeb: ActionForWeb[];
};

export type ActionForWeb = Pick<PatientHosRecord, 'bussTime' | 'hospType' | 'admiDayCount'> & {
  actionNumber: number;
};

export type PatientHosRecordDateMap = {
  [key: string]: PatientHosRecord[];
};

export type StatisticsPatient = {
  count: number;
  label: string;
  list: PatientHosRecord[];
};

declare module 'vue' {
  interface ComponentCustomProperties {
    $yearmonth: (key: string) => string;
  }
}
