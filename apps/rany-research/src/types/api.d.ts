declare module 'Api' {
  // 通用响应结构
  export interface BaseResponse<T = any> {
    code: number;
    message: string;
    data: T;
    success: boolean;
  }

  // 分页响应结构
  export interface PaginatedResponse<T = any> {
    list: T[];
    total: number;
    pageNo: number;
    pageSize: number;
    totalPages: number;
  }

  // 请求配置选项
  export interface RequestOptions {
    errorMessageShow?: boolean;
    fullscreenLoading?: boolean;
    timeout?: number;
  }

  // 通用列表查询参数
  export interface ListParams {
    pageNo?: number;
    pageSize?: number;
    keyword?: string;
    status?: string;
    startTime?: string;
    endTime?: string;
  }

  // 通用删除参数
  export interface DeleteParams {
    id: string | number;
    ids?: (string | number)[];
  }

  // 通用状态更新参数
  export interface StatusUpdateParams {
    id: string | number;
    status: string | number;
  }
}

// 导出常用类型
export type PaginatedResponse<T = any> = Api.PaginatedResponse<T>;
export type BaseResponse<T = any> = Api.BaseResponse<T>;
export type RequestOptions = Api.RequestOptions;
export type ListParams = Api.ListParams;
