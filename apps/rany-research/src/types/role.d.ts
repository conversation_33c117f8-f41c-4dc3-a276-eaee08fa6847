// 角色基础信息
export interface Role {
  roleId?: number;
  roleName: string;
  remark?: string;
  createTime?: string;
  modifyTime?: string;
  isSystem?: number;
  dataPermit?: number;
  department?: number;
  status?: string | number;
  permissions?: string[];
  userCount?: number;
}

// 角色用户关联
export interface RoleUser {
  roleId: number;
  userId: string;
  userName: string;
  nickName?: string;
  mobile?: string;
  email?: string;
  status?: string;
  createTime?: string;
}

// 角色权限
export interface RolePermission {
  roleId: number;
  permissionId: string;
  permissionName: string;
  permissionType: string;
  resourceId?: string;
  resourceName?: string;
}

// 角色列表查询参数
export interface RoleListParams {
  pageNo?: number;
  pageSize?: number;
  roleName?: string;
  status?: string;
  isSystem?: number;
  department?: number;
}

// 角色创建/更新参数
export interface RoleFormData {
  roleId?: number;
  roleName: string;
  remark?: string;
  isSystem?: number;
  dataPermit?: number;
  department?: number;
  permissions?: string[];
}

// 角色状态枚举
export enum RoleStatus {
  DISABLED = '0',
  ENABLED = '1'
}

// 角色类型枚举
export enum RoleType {
  SYSTEM = 0,
  CUSTOM = 1
}

// 数据权限枚举
export enum DataPermission {
  ALL = 1, // 全部数据权限
  CUSTOM = 2, // 自定数据权限
  DEPT = 3, // 部门数据权限
  DEPT_AND_SUB = 4, // 部门及以下数据权限
  SELF = 5 // 仅本人数据权限
}
