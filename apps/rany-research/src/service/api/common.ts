import { request } from '../request';

/**
 * 获取省市区的子集
 *
 * @param type - 行政区域类型:1-省(不传父区域ID),2-市（传省ID）,3-区县（传市ID）,4-乡镇（传区县ID）
 * @param parentId - 父级ID
 * @returns 省市区的子集
 *
 *   type:
 */
export function fetchGetProvinceSubset(type: number, parentId?: string) {
  return request<Api.Region.RegionList>({
    url: '/patient/provinces/subset',
    method: 'get',
    params: {
      type,
      parentId
    }
  });
}
/** 文件上传 */
export function fetchPostUploadFile(data: Api.Common.UploadFileParams) {
  return request({
    url: 'file/add',
    method: 'post',
    data
  });
}
/** 文件列表 */
export function fetchGetFileList(data: Api.Common.UploadFileSearchParams) {
  return request<Api.Common.UploadFileList>({
    url: '/file/list',
    method: 'post',
    data
  });
}
/** 文件删除 */
export function fetchDeleteFile(id: string) {
  return request({
    url: `/file/delete/${id}`,
    method: 'get'
  });
}
/** 获取人员list */
export function fetchGetMemberList(data: Api.Common.CommonSearchParams & { hospitalCollectionId: string }) {
  return request<Api.Common.PaginatingQueryRecord>({
    url: '/hospital/userlist',
    method: 'post',
    data
  });
}
