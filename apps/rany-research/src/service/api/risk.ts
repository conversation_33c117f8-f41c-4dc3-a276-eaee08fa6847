import { request } from '../request';

/** get risk list */
export function fetchGetRiskList(params?: Api.Risk.RiskSearchParams) {
  return request<Api.Risk.RiskList>({
    url: '/diseaseRisk/list',
    method: 'post',
    data: params
  });
}
/** get role list */
export function fetchGetAllProjectList() {
  return request<Api.Risk.AllProjectList[]>({
    url: '/risk/getProjectList',
    method: 'get'
  });
}
/** before del risk */
export function fetchGetRiskDel(id: string) {
  return request<Api.Common.CommonResponseType>({
    url: `/diseaseRisk/delete/${id}`,
    method: 'get'
  });
}
/** get risk tree */
export function fetchGetRiskTree() {
  return request<Api.Risk.RiskListTree[]>({
    url: '/risk/listTree',
    method: 'get'
  });
}
/** 添加接口 */
export function fetchPostRiskAdd(params?: Api.Risk.Risk & Api.Common.BaseRequestParams) {
  return request<Api.Risk.RiskListTree[]>({
    url: '/diseaseRisk/add',
    method: 'post',
    data: params
  });
}
/** 编辑接口 */
export function fetchPostRiskEdit(params?: Api.Risk.Risk & Api.Common.BaseRequestParams) {
  return request<Api.Risk.RiskListTree[]>({
    url: '/diseaseRisk/update',
    method: 'put',
    data: params
  });
}
