import { request } from '../request';

/** get suite list */
export function fetchPostSuiteList(data: Api.Suite.SuiteSearchParams) {
  return request<Api.Suite.SuiteList>({
    url: '/open/stack/pageList',
    method: 'post',
    data
  });
}
/** get role list */
// export function fetchGetAllParentList() {
//   return request<Api.Disease.AllParentList[]>({
//     url: '/disease/getParentList',
//     method: 'get'
//   });
// }
/** del suite item */
export function fetchGetSuiteItemDel(id: string) {
  return request<Api.Common.CommonResponseType>({
    url: `/open/item/delete/${id}`,
    method: 'get'
  });
}
/** del suite */
export function fetchGetSuiteDel(id: string) {
  return request<Api.Common.CommonResponseType>({
    url: `/open/stack/delete/${id}`,
    method: 'get'
  });
}
/** get suite tree */
export function fetchGetSuiteTree() {
  return request<Api.Suite.SuiteListTree[]>({
    url: '/getAllSuiteList',
    method: 'get'
  });
}

/** 新增项目 */
export function fetchPostSuiteAdd(data: Api.Suite.Suite & Api.Common.BaseRequestParams) {
  return request<Api.Common.CommonResponseType>({
    url: '/open/stack/add',
    method: 'post',
    data
  });
}

/** 更新项目 */
export function fetchPostSuiteUpdate(data: Api.Suite.Suite & Api.Common.BaseRequestParams) {
  return request<Api.Common.CommonResponseType>({
    url: '/open/stack/update',
    method: 'post',
    data
  });
}

/** 获取项目类别 */
export function fetchGetSuiteItemType() {
  return request<Api.Suite.ItemType[]>({
    url: '/open/item/typelist',
    method: 'get'
  });
}

/** 添加组套详情-小项 */
export function fetchPostSuiteItemAdd(data: Api.Suite.SuiteItem) {
  return request<Api.Suite.SuiteItemResponseType>({
    url: '/open/item/add',
    method: 'post',
    data
  });
}

/** 获取组套列表-小项 */
export function fetchGetSuiteItemList(moduleId: string) {
  return request<Api.Suite.SuiteItem[]>({
    url: `/open/item/listByMId/${moduleId}`,
    method: 'get'
  });
}
/** 更新组套详情-小项 */
export function fetchPostSuiteItemUpdate(data: Api.Suite.SuiteItem) {
  return request<Api.Common.CommonResponseType>({
    url: '/open/item/update',
    method: 'post',
    data
  });
}
