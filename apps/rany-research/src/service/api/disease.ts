import { request } from '../request';

/** get disease list */
export function fetchGetDiseaseList(params?: Api.Disease.DiseaseSearchParams) {
  return request<Api.Disease.DiseaseList>({
    url: '/disease/list',
    method: 'POST',
    data: params
  });
}

/** get disease list */
export function fetchGetParentDiseaseList() {
  return request<Api.Disease.DiseaseList>({
    url: '/disease/getList',
    method: 'GET'
  });
}
/** 删除疾病 */
export function fetchDeleteDisease(id: string) {
  return request<Api.Common.CommonResponseType>({
    url: `/disease/delete/${id}`,
    method: 'GET'
  });
}

/** get role list */
export function fetchGetAllParentList() {
  return request<Api.Disease.AllParentList[]>({
    url: '/risk/listTree',
    method: 'get'
  });
}
/** before del disease */
export function fetchGetDiseaseBeforeDel(params?: Api.Disease.DiseaseBeforeDelParams) {
  return request<Api.Common.CommonResponseType>({
    url: '/del/before',
    method: 'get',
    params
  });
}
/** 添加疾病 */
export function fetchPostDiseaseAdd(data: Api.Disease.Disease & Api.Common.BaseRequestParams) {
  return request({
    url: '/disease/add',
    method: 'post',
    data
  });
}
/** 编辑疾病 */
export function fetchPutDiseaseEdit(data: Api.Disease.Disease & Api.Common.BaseRequestParams) {
  return request({
    url: '/disease/update',
    method: 'put',
    data
  });
}
/** 获取疾病树 */
export function fetchGetDiseaseTree() {
  return request<Api.Common.CommonResponseType>({
    url: '/disease/listTree',
    method: 'get'
  });
}
