import { request } from '../request';

export function fetchPostProgramAdd(data: Api.Program.ProgramItem) {
  return request<string | null>({ url: '/open/solu/add', method: 'post', data });
}
export function fetchPostProgramUpdate(data: Api.Program.ProgramItem) {
  return request<number>({ url: '/open/solu/update', method: 'post', data });
}
export function fetchPostProgramDelete(id: string) {
  return request<number>({ url: `/open/solu/delete/${id}`, method: 'get' });
}
export function fetchPostProgramList(data: Api.Program.SearchParams) {
  return request<Api.Program.ProgramList>({
    url: '/open/solu/pageList',
    method: 'post',
    data
  });
}
export function fetchGetProgramDetail(id: string) {
  return request<Api.Program.ProgramItem>({ url: `/open/solu/detail/${id}`, method: 'get' });
}
export function fetchGetProgramItemsDetail(programId: string) {
  return request<Api.Program.ItemResponseData>({ url: `/open/solu/items/${programId}`, method: 'get' });
}

/** 方案小项 */
/**
 * 添加方案小项
 *
 * @param data 方案小项数据
 * @returns 小项id
 */
export function fetchPostProgramItemAdd(data: Api.ProgramItem.ProgramItemRecord) {
  return request<string | null>({ url: '/open/item/add', method: 'post', data });
}
/**
 * 更新方案小项
 *
 * @param data 方案小项数据
 * @returns 小项id
 */
export function fetchPostProgramItemUpdate(data: Api.ProgramItem.ProgramItemRecord) {
  return request<string | null>({ url: '/open/item/update', method: 'post', data });
}
/**
 * 删除方案小项
 *
 * @param id 小项id
 * @returns 是否删除成功
 */
export function fetchGetProgramItemDelete(id: string) {
  return request<string | null>({ url: `/open/item/delete/${id}`, method: 'get' });
}
export function fetchPostProgramRowItemDelete(rowNum: number, solutionId: string) {
  return request<string | null>({ url: `/open/solu/del/row`, method: 'post', data: { rowNum: [rowNum], solutionId } });
}
/**
 * 获取方案小项详情
 *
 * @param id 小项id
 * @returns 小项数据
 */
export function fetchGetProgramItemDetail(id: string) {
  return request<Api.ProgramItem.ProgramItemRecord>({ url: `/open/item/detail/${id}`, method: 'get' });
}

/**
 * 获取诊疗计划中的方案详情
 *
 * @param soluId - 方案ID
 * @param clinicPlanId - 诊疗计划ID
 * @returns 返回包含方案详细信息的Promise，包括表头、表格数据、备注等
 */
export function fetchGetProgramDetailByClinic(soluId: string, clinicPlanId: string) {
  return request<Api.Program.ItemResponseData>({
    url: `/open/solu/detailAll/${soluId}/${clinicPlanId}`,
    method: 'get'
  });
}

/**
 * 添加方案行项目
 *
 * @param data 方案行项目数据
 * @returns 行项目id
 */
export function fetchPostProgramCustomItemAdd(data: Api.ProgramItem.AddCustomItemVO) {
  return request<string | null>({
    url: `/open/itemCustom/add`,
    method: 'post',
    data
  });
}
/**
 * 获取方案行项目
 *
 * @param data 方案行项目数据
 * @returns 行项目id
 */
export function fetchPostProgramCustomItemDetail(id: string) {
  return request<Api.ProgramItem.ProgramItemRecord>({
    url: `/open/itemCustom/detail/${id}`,
    method: 'get'
  });
}

/**
 * 更新方案行项目
 *
 * @param data 方案行项目数据
 * @returns 行项目id
 */
export function fetchPostProgramCustomItemUpdate(data: Api.ProgramItem.AddCustomItemVO) {
  return request<string | null>({
    url: `/open/itemCustom/update`,
    method: 'post',
    data
  });
}
/**
 * 删除定制方案小项
 *
 * @returns 行项目id
 */
export function fetchGetProgramCustomItemDelete(id: string) {
  return request<string | null>({
    url: `/open/itemCustom/delete/${id}`,
    method: 'get'
  });
}

export function fetchPostProgramCopy(data: Api.Program.CopyProgramParams) {
  return request<string | null>({ url: '/open/solu/copy', method: 'post', data });
}
