import { dataPlatformRequest as request } from '../request';

export function fetchGetMedicineDictFrequency() {
  return request<DataPlatformApi.Medicine.DictionaryItem[]>({ url: '/open/dict/il/dict5', method: 'get' });
}
export function fetchGetMedicineDictRoute(id: string = 'dict3') {
  return request<DataPlatformApi.Medicine.DictionaryItem[]>({ url: `/open/dict/lbp/${id}`, method: 'get' });
}

export function fetchGetMedicineDictList() {
  return request<DataPlatformApi.Medicine.ItemRecord[]>({ url: '/open/medi/gl/', method: 'get' });
}

export function fetchGetCheckCategoryById(id: string) {
  return request<DataPlatformApi.Category.ItemRecord[]>({ url: `/open/category/l/${id}`, method: 'get' });
}
/**
 * @param categoryId 分类ID
 * @returns 分类下的检验项列表
 */
export function fetchGetCheckRecords(categoryId: string) {
  return request<DataPlatformApi.CheckItem.ItemRecord[]>({ url: `/open/laboratory/lc/${categoryId}`, method: 'get' });
}
/** @returns 手术记录列表 */
export function fetchGetOperationRecords(parentId: string) {
  return request<DataPlatformApi.OperationItem.OperationRecord[]>({ url: `/open/oc/list/${parentId}`, method: 'get' });
}
