import { request } from '../request';

/** 获取科室列表 */
/** get department list */
export function fetchPostDepartmentList(data: Api.Dept.DepartmentSearchParams) {
  return request<Api.Dept.DepartmentList>({
    url: '/dept/list',
    method: 'post',
    data
  });
}

/** 添加科室 */
export function fetchPostDepartmentAdd(data: Api.Dept.Department & Api.Common.BaseRequestParams) {
  return request({
    url: '/dept/add',
    method: 'post',
    data
  });
}

/** 编辑科室 */
export function fetchPostDepartmentEdit(data: Api.Dept.Department & Api.Common.BaseRequestParams) {
  return request({
    url: '/dept',
    method: 'put',
    data
  });
}

/** 删除科室 */
export function fetchPostDepartmentDelete(id: string | undefined) {
  return request({
    url: `/dept/delete/${id}`,
    method: 'post'
  });
}
