import type { RoleType } from "@/stores/user";

export type LoginResponse = {
  token: string;
  permissions: string[];
  roles: string[];
  user: UserInfo;
};
declare global {
  type MyReponse<T> = {
    code?: number;
    data: T;
    message?: string;
  };
  type EmptyObject = Record<string, unknown>;
  type PaginationResponse<T> = Pagination<T>;
  type ResUserInfo = Partial<UserInfo>;
  type ResLoginResponse = LoginResponse;
  type ResMenuItem = MenuItem;
  type ResRoleItem = RoleItem;
  type ResTemplateItem = TemplateItem;
  type ResReport = Pagination<ReportItem>;
  type StatusType1 = StatusType;
  type ResReportPreview = {
    userinfo?: ReportItem;
    approve?: Array<ApproveItem>;
  };

  type ListQueryParam = {
    pageSize: number;
    pageNo: number;
    sortIndex?: number;
    status?: StatusType;
    name?: string;
  };
}

export enum StatusType {
  DISABLE = "0",
  ENABLED = "1",
}

export enum AuthType {
  MENU = "0",
  BUTTON = "1",
  AUTH = "2",
}

export type RoleItem = {
  remark?: string;
  roleId?: number;
  dataPermit?: string;
  roleName?: string;
  createTime?: string;
  isSystem?: number;
};

type MenuItem = {
  menuId?: string;
  attr?: string;
  menuName?: string;
  parentId?: string;
  orderNum?: number;
  path?: string;
  icon?: string;
  perms?: string | null;
  type?: AuthType; // 类型 0菜单 1按钮 2路由
  children?: { [index: string]: ResMenuItem };
};

export declare type UserInfo = {
  [inde: string]: string | boolean | null | number | number[] | Role;
  avatar: string;
  department: number; // For update info passing the lowest level department id; Detail Info return number[]
  departmentlist: number[];
  createTime: string;
  description: string;
  email: string;
  emailSendMark: boolean;
  id: string;
  lastLoginTime: string;
  logoffRemarks: null;
  roleId: number;
  mobile: string | null;
  modifyTime: string | null;
  nickName: string | null;
  pwdStatus: number;
  password: string;
  oldpassword: string;
  newpassword: string;
  smsSystem: boolean;
  refId: string;
  smsTodo: boolean;
  status: string;
  type: RoleType;
  title: string;
  userId: string | null;
  userName: string;
  bindDevice: string;
  faceId: string;
  fingerprintId: string;
  faceStatus: string;
  fingerprintStatus: number;
  wardId: number;
  roles: Role;
};

export type Role = {
  createTime: string;
  dataPermit: number;
  isSystem: number;
  modifyTime: number;
  remark: number;
  roleId: number;
  roleName: string;
};

export type Department = {
  id: number;
  parentId: number;
  deptName: string;
  deptCode: string;
  remark: string;
  status: number;
  creator: string;
  createTime: string;
  modifyTime: string;
  modified: string;
  deptTypeId: string;
  scot: string;
  isDelete: number;
  userList: string;
  nodeId: string;
  hospitalIds: string;
  parentName: string;
  parentIds: string;
};

export type Nation = {
  id: string;
  dictId: string;
  itemText: string;
  itemValue: string;
  itemSort: number;
  remark: string;
  status: number;
  createTime: string;
  updateTime: string;
  parentId: string;
};

