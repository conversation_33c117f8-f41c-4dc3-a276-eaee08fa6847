import { request } from '../request';

export function fetchPostClinicList(search: Api.ClinicPlan.SearchParams) {
  return request<Api.ClinicPlan.ClinicPlanList>({ url: '/open/clinicPlan/pageList', method: 'post', data: search });
}
export function fetchPostClinicAdd(data: Api.ClinicPlan.AddVo) {
  return request<number>({ url: '/open/clinicPlan/add', method: 'post', data });
}
export function fetchGetClinicDelete(id: string) {
  return request<number>({ url: `/open/clinicPlan/delete/${id}`, method: 'get' });
}
export function fetchGetClinicDetail(id: string) {
  return request<Api.ClinicPlan.ClinicPlanItem>({ url: `/open/clinicPlan/detail/${id}`, method: 'get' });
}
export function fetchPostClinicUpdate(data: Partial<Api.ClinicPlan.Record>) {
  return request<number>({ url: `/open/clinicPlan/update`, method: 'post', data });
}

/**
 * Sends a POST request to add a new clinic stage.
 *
 * @example
 *   const stageData = { /* your stage data here *\/ };
 *   fetchPostClinicStageAdd(stageData).then(response => {
 *   console.log('Stage added with ID:', response);
 *   });
 *
 * @param data - The data for the clinic stage to be added, which is a partial object of type
 *   {@link Api.ClinicStage.StageVO}.
 * @returns A promise that resolves to the response of the request, which is expected to be a number.
 */
export function fetchPostClinicStageAdd(data: Partial<Api.ClinicStage.StageVO>) {
  return request<number>({ url: `/open/clinicStage/add`, method: 'post', data });
}
export function fetchPostClinicStageUpdate(data: Partial<Api.ClinicStage.StageVO>) {
  return request<number>({ url: `/open/clinicStage/update`, method: 'post', data });
}
/**
 * Sends a POST request to add or update a program for a clinic stage.
 *
 * @param data - The data for the clinic stage program, which is a partial object of type
 *   `Api.ClinicStage.StageProgramVO`. This can include any subset of properties defined in the `StageProgramVO`
 *   interface.
 * @returns A promise that resolves to a number, which typically represents the status or ID of the operation performed.
 * @throws {Error} Throws an error if the request fails.
 */
export function fetchPostClinicStageAddProgramOrRest(data: Partial<Api.ClinicStage.StageProgramVO>) {
  return request<number>({ url: `/open/clinicStage/bind/solu`, method: 'post', data });
}

export function fetchPostClinicLinkProject(data: Api.ClinicPlan.LinkProjectVo) {
  return request<string>({ url: `/open/clinicPlan/projectLinkPlan`, method: 'post', params: data });
}

export function fetchGetClinicStageDeleteProgramOrRest(id: string) {
  return request<number>({ url: `/open/clinicStage/unbind/solu/${id}`, method: 'get' });
}

/**
 * Sends a GET request to delete a clinic stage by its ID.
 *
 * @example
 *   const stageId = '12345';
 *   fetchGetClinicStageDelete(stageId)
 *     .then(response => {
 *       console.log('Stage deleted, response:', response);
 *     })
 *     .catch(error => {
 *       console.error('Error deleting stage:', error);
 *     });
 *
 * @param id - The unique identifier of the clinic stage to be deleted.
 * @returns A promise that resolves to a number, which typically indicates the success of the deletion.
 */
export function fetchGetClinicStageDelete(id: string) {
  return request<number>({ url: `/open/clinicStage/delete/${id}`, method: 'get' });
}

export function fetchGetClinicStageCopy(stageId:string,userId:string,userName:string){
  return request<number>({url:`/open/clinicStage/copy/${stageId}`,method:'get',params:{userId,userName}});
}

/**
 * Fetches the preview details of a clinic plan by its ID.
 *
 * @example
 *   const clinicPlanId = '12345';
 *   fetchPostClinicPreview(clinicPlanId)
 *     .then(details => {
 *       console.log(details);
 *     })
 *     .catch(error => {
 *       console.error('Error fetching clinic plan preview:', error);
 *     });
 *
 * @param id - The unique identifier of the clinic plan to fetch.
 * @returns A promise that resolves to the clinic plan details.
 */
export function fetchGetClinicPreview(id: string) {
  return request<Api.ClinicPlan.ClinicPlanDetail>({ url: `/open/clinicPlan/preview/${id}`, method: 'get' });
}

/**
 * Fetches the specific details of a clinic plan by its ID.
 *
 * @example
 *   const clinicPlanDetail = await fetchGetClinicSpecificDetail('12345');
 *
 * @param id - The unique identifier of the clinic plan to retrieve.
 * @returns A promise that resolves to the details of the clinic plan.
 */
export function fetchGetClinicSpecificDetail(id: string) {
  return request<Api.ClinicPlan.ClinicPlanDetail>({ url: `/open/clinicPlan/detailAll/${id}`, method: 'get' });
}

export function fetchGetClinicPlanByProject(search: Api.ClinicPlan.SearchParams) {
  const params = new URLSearchParams(search as any).toString();
  return request<Api.ClinicPlan.ClinicPlanList>({
    url: `/open/clinicPlan/listByProject?${params}`,
    method: 'get'
  });
}

/**
 * Sends a POST request to copy a clinic plan by its ID.
 *
 * @param data - The data for the clinic plan to be copied, which is an object of type
 *   {@link Api.ClinicPlan.CopyClinicPlanParams}.
 * @returns A promise that resolves to a number, which typically indicates the success of the copy operation.
 */
export function fetchPostClinicPlanCopy(data: Api.ClinicPlan.CopyClinicPlanParams) {
  return request<number>({ url: `/open/clinicPlan/copy`, method: 'post', data });
}
