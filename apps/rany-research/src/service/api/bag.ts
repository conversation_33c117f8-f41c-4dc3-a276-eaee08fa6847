import { request } from '../request';

/**
 * Sends a request to add a new bag (专科袋).
 *
 * @param data - The parameters required to generate a new bag.
 * @returns A promise that resolves to the ID of the newly created bag.
 */
export function fetchPostBagAdd(data: Api.Bag.GenerateBagParams | Api.Bag.RandomizeParams) {
  return request<number>({
    url: '/open/sbag/add',
    method: 'post',
    data
  });
}

/**
 * Fetches the details of a patient's bag by their ID.
 *
 * @param id - The unique identifier of the patient whose bag details are to be fetched.
 * @returns A promise that resolves to the result of the API call, containing the patient's bag details.
 */
export function fetchGetPatientBag(id: string) {
  return request<Api.Bag.PatientBagResult>({
    url: `/open/sbag/detailAll/${id}`,
    method: 'get'
  });
}

/** 方案小项 */
/**
 * 添加方案小项
 *
 * @param data 方案小项数据
 * @returns 小项id
 */
export function fetchPostProgramItemAdd(data: Api.ProgramItem.ProgramItemRecord) {
  return request<string | null>({ url: '/open/sbag/item/add', method: 'post', data });
}
/**
 * 更新方案小项
 *
 * @param data 方案小项数据
 * @returns 小项id
 */
export function fetchPostProgramItemUpdate(data: Api.ProgramItem.ProgramItemRecord) {
  return request<string | null>({ url: '/open/sbag/item/update', method: 'post', data });
}
/**
 * 删除方案小项
 *
 * @param id 小项id
 * @returns 是否删除成功
 */
export function fetchGetProgramItemDelete(id: string) {
  return request<string | null>({ url: `/open/sbag/item/delete/${id}`, method: 'get' });
}

export function fetchPostProgramRowItemDelete(rowNum: number, solutionId: string) {
  return request<string | null>({
    url: `/open/sbag/solu/del/row`,
    method: 'post',
    data: { rowNum: [rowNum], solutionId }
  });
}

/**
 * 获取方案小项详情
 *
 * @param id 小项id
 * @returns 小项数据
 */
export function fetchGetProgramItemDetail(id: string) {
  return request<Api.ProgramItem.ProgramItemRecord>({ url: `/open/sbag/item/detail/${id}`, method: 'get' });
}

/**
 * 获取方案包中的方案详情
 *
 * @param soluId - 方案ID
 * @param sbagId - 方案包ID
 * @returns 返回包含方案详细信息的Promise，包括表头、表格数据、备注等
 */
export function fetchGetProgramDetailBySBag(soluId: string, sbagId: string) {
  return request<Api.Program.ItemResponseData>({
    url: `/open/sbag/solu/detailAll/${soluId}/${sbagId}`,
    method: 'get'
  });
}

/**
 * 添加方案行项目
 *
 * @param data 方案行项目数据
 * @returns 行项目id
 */
export function fetchPostProgramCustomItemAdd(data: Api.ProgramItem.AddCustomItemVO) {
  return request<string | null>({
    url: `/open/sbag/itemCustom/add`,
    method: 'post',
    data
  });
}

/**
 * 更新方案行项目
 *
 * @param data 方案行项目数据
 * @returns 行项目id
 */
export function fetchPostProgramCustomItemUpdate(data: Api.ProgramItem.AddCustomItemVO) {
  return request<string | null>({
    url: `/open/sbag/itemCustom/update`,
    method: 'post',
    data
  });
}

/**
 * 删除定制方案小项
 *
 * @returns 行项目id
 */
export function fetchGetProgramCustomItemDelete(id: string) {
  return request<string | null>({
    url: `/open/sbag/itemCustom/delete/${id}`,
    method: 'get'
  });
}

/** 新增阶段，方案，休疗 */
/**
 * 发送请求以添加一个新的专科袋（sbag）阶段。
 *
 * @param data - 要添加的专科袋阶段的数据。
 * @returns 一个Promise，解析为新创建的专科袋阶段的ID。
 */
export function fetchPostSbagStageAdd(data: Partial<Api.ClinicStage.StageVO>) {
  // 发送POST请求到指定的API端点，创建一个新的专科袋阶段
  return request<number>({ url: `/open/sbagStage/add`, method: 'post', data });
}

/**
 * 获取指定专科袋（sbag）的阶段列表。
 *
 * @param sbagId - 专科袋的唯一标识符。
 * @returns 一个Promise，解析为包含专科袋阶段信息的数组。
 */
export function fetchGetStageListBySbagId(sbagId: string) {
  // 发送GET请求到指定的API端点，获取专科袋的阶段列表
  return request<Api.ClinicStage.StageVO[]>({ url: `/open/sbagStage/listBySbag/${sbagId}`, method: 'get' });
}

/**
 * 获取指定阶段的方案列表。
 *
 * @param stageId - 阶段的唯一标识符。
 * @returns 一个Promise，解析为包含方案信息的数组。
 */
export function fetchGetMethodListByStageId(stageId: string) {
  return request<Api.ClinicStage.StageVO[]>({ url: `/open/sbagStage/listByStage/${stageId}`, method: 'get' });
}
/**
 * 绑定方案/休疗到阶段
 *
 * @param data - 绑定方案/休疗到阶段的数据。
 * @returns 一个Promise，解析为包含方案信息的数组。
 */
export function fetchPostBindSolutionToStage(data: Partial<Api.Bag.BindSolutionToStageParams>) {
  return request<Api.ClinicStage.StageVO[]>({ url: `/open/sbagStage/bind/solu`, method: 'post', data });
}

/** 小项实际值模块 */

/**
 * Adds an actual value to a bag.
 *
 * @param data - The data for adding an actual value to a bag, which is a partial object of type
 *   {@link Api.Bag.ActualValueParams}.
 * @returns A promise that resolves to the new bag stage with the added actual value.
 */
export function fetchPostActualValueToBagAdd(data: Partial<Api.Bag.ActualValueParams>) {
  return request<number>({ url: `/open/sbag/actual/add`, method: 'post', data });
}
export function fetchPostActualValueToBagUpdate(data: Partial<Api.Bag.ActualValueParams>) {
  return request<number>({ url: `/open/sbag/actual/update`, method: 'post', data });
}
export function fetchGetActualValueToBagDelete(id: string) {
  return request<number>({ url: `/open/sbag/actual/delete/${id}`, method: 'get' });
}
export function fetchGetActualValueToBagDetail(id: string) {
  return request<Api.Bag.ActualValueItem>({ url: `/open/sbag/actual/detail/${id}`, method: 'get' });
}
