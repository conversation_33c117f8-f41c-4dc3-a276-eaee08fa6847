import { request } from '../request';

/** get hospital list */
export function fetchPostHospitalList(data?: Api.Hospital.HospitalSearchParams) {
  return request<Api.Hospital.HospitalList>({
    url: '/hospital/list',
    method: 'post',
    data
  });
}

/** add hospital */
export function fetchPostHospitalAdd(data: Api.Hospital.Hospital) {
  return request({
    url: '/hospital/add',
    method: 'post',
    data
  });
}
/** update hospital */
export function fetchPutHospitalUpdate(data: Api.Hospital.Hospital & Api.Common.BaseRequestParams) {
  return request({
    url: '/hospital/update',
    method: 'put',
    data
  });
}

// 项目关联下的医院-添加
export function fetchPostProjectHospitalAdd(data: Api.Hospital.ProjectHospitalAddParams) {
  return request({
    url: '/projectHospital/add',
    method: 'post',
    data
  });
}

// 项目关联下的医院-移除
export function fetchDeleteProjectHospitalRemove(id: string) {
  return request({
    url: '/projectHospital/removeOne',
    method: 'get',
    params: {
      id
    }
  });
}

// 项目关联下的医院-查询列表
export function fetchPostProjectHospitalList(data: Api.Hospital.HosOfProjectSearchParams) {
  return request<Api.Hospital.HospitalOfProjectList>({
    url: '/projectHospital/list',
    method: 'post',
    data
  });
}
// 项目关联下的医院-修改医院信息
export function fetchPutProjectHospitalUpdate(data: Api.Hospital.ProjectHospitalAddParams) {
  return request({
    url: '/projectHospital/update',
    method: 'put',
    data
  });
}
