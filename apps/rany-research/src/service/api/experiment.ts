import { request } from '../request';

/** 获取试验列表 */
/** get experiment list */
export function fetchPostExperimentList(data: Api.Experiment.ExperimentSearchParams) {
  return request<Api.Experiment.ExperimentList>({
    url: '/project/list',
    method: 'post',
    data
  });
}

/** 添加试验 */
export function fetchPostExperimentAdd(data: Api.Experiment.Experiment & Api.Common.BaseRequestParams) {
  return request({
    url: '/project/add',
    method: 'post',
    data
  });
}

/** 编辑试验 */
export function fetchPostExperimentEdit(data: Api.Experiment.Experiment & Api.Common.BaseRequestParams) {
  return request({
    url: '/project/update',
    method: 'post',
    data
  });
}

/** 删除试验 */
export function fetchPostExperimentDelete(data: Api.Experiment.ExperimentDeleteParams & Api.Common.BaseRequestParams) {
  return request({
    url: `/project/delete`,
    method: 'post',
    data
  });
}

/** 启动项目 */
export function fetchPostExperimentStart(data: Api.Experiment.ExperimentDeleteParams & Api.Common.BaseRequestParams) {
  return request({
    url: '/project/enable',
    method: 'post',
    data
  });
}

/** 终止项目 */
export function fetchPostExperimentStop(data: Api.Experiment.ExperimentDeleteParams & Api.Common.BaseRequestParams) {
  return request({
    url: '/project/disable',
    method: 'post',
    data
  });
}

/** 获取相关字典 */
export function fetchGetExperimentDict(type: string) {
  return request<Api.Experiment.ExperimentDict[]>({
    url: `/project/get/dict/${type}`,
    method: 'get'
  });
}

/** 获取试验详情 */
export function fetchGetExperimentDetail(id: string | undefined) {
  return request<Api.Experiment.Experiment>({
    url: `/project/info/${id}`,
    method: 'get'
  });
}

// 根据医院id获取试验列表
export function fetchPostExperimentListByHospitalId(data: Api.Experiment.ExperimentSearchParams) {
  return request<Api.Experiment.ExperimentList>({
    url: `/project/hospital/list`,
    method: 'post',
    data
  });
}

export function fetchPostExperimentExitHospital(data: Api.Experiment.ExperimentExitHospitalParams) {
  return request({
    url: `/project/hospital/out`,
    method: 'post',
    data
  });
}

// 项目医院成员关联-添加
export function fetchPostExperimentHosMemberAdd(data: Api.Experiment.ExperimentHospitalMemberAddParams[]) {
  return request({
    url: `/projectHospitalUser/add`,
    method: 'post',
    data
  });
}

// 项目医院成员关联-移除
export function fetchPostExperimentHosMemberRemove(id: string, projectId: string) {
  return request({
    url: `/projectHospitalUser/removeOne`,
    method: 'get',
    params: {
      id,
      projectId
    }
  });
}

// 项目医院成员关联-列表
export function fetchPostExperimentHosMemberList(data: Api.Experiment.ExperimentHosMemberSearchParams) {
  return request<Api.Experiment.ExperimentHosMemberList>({
    url: `/projectHospitalUser/list`,
    method: 'post',
    data
  });
}

// 项目关联危险度-添加
export function fetchPostExperimentDangerAdd(data: Api.Experiment.ExperimentAddProjectRiskData) {
  return request({
    url: `/projectDiseRisk/add`,
    method: 'post',
    data
  });
}

// 项目关联危险度-删除
export function fetchPostExperimentDangerRemove(id: string) {
  return request({
    url: `/projectDiseRisk/delete/${id}`,
    method: 'delete'
  });
}

// 项目关联危险度-列表
export function fetchGetExperimentDangerList(data: Api.Risk.RiskSearchParamsOfProject) {
  return request<Api.Risk.Risk>({
    url: `/projectDiseRisk/list/${data.projectId}`,
    method: 'get'
  });
}
