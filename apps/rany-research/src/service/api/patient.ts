import { request } from '../request';

/**
 * Adding patient
 *
 * @param patient patient info
 */
export function fetchPostPatientAdd(patient: Api.Patient.PatientItem & Api.Common.BaseRequestParams) {
  return request<boolean>({ url: '/patient/add', method: 'post', data: patient });
}
/**
 * list of patient
 *
 * @param patient patient info
 */
export function fetchPostPatientList(params?: Api.Patient.CommonSearchParams & { hospitalId?: string | null }) {
  return request<Api.Patient.PatientList>({
    url: '/patient/list',
    method: 'post',
    data: params
  });
}
/**
 * list of nation
 *
 * @param patient patient info
 */
export function fetchGetPatientNations() {
  return request<Api.Patient.PatientNotion[]>({
    url: '/patient/get/nation',
    method: 'get'
  });
}
/**
 * info of patient
 *
 * @param id patient'id
 */
export function fetchGetPatientInfo(id: string) {
  return request<Api.Patient.PatientItem>({
    url: `/patient/getInfo/${id}`,
    method: 'get'
  });
}
/**
 * Update patient info
 *
 * @param patient patientinfo to be update
 */
export function fetchPutPatientUpdate(patient: Api.Patient.PatientItem) {
  return request<boolean>({
    url: `/patient/update`,
    method: 'put',
    data: patient
  });
}

/**
 * 获取患者历史危险度信息
 *
 * @param params 查询参数
 */
export function fetchPostPatientRiskList(params?: Api.Patient.PatientHisSearchParams) {
  return request<Api.Patient.PatientHis>({
    url: '/patientRisk/list',
    method: 'post',
    data: params
  });
}

/**
 * 获取就诊信息列表
 *
 * @param params 查询参数
 */
export function fetchPostTreatmentList(params?: Api.Patient.PatientHisSearchParams) {
  return request<Api.Patient.TreatmentList>({
    url: '/treatment/list',
    method: 'post',
    data: params
  });
}
