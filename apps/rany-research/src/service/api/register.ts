import { request } from '../request';

/** get register list */
export function fetchPostRegisterList(data?: Api.Register.RegisterSearchParams) {
  return request<Api.Register.RegisterList>({
    url: '/registpage/list',
    method: 'post',
    data
  });
}
/** add register */
export function fetchPostRegisterPageAdd(data: Api.Register.Register & Api.Common.BaseRequestParams) {
  return request({
    url: '/registpage/add',
    method: 'post',
    data
  });
}
/** update register */
export function fetchPostRegisterPageUpdate(data: Api.Register.Register & Api.Common.BaseRequestParams) {
  return request({
    url: '/registpage/update',
    method: 'post',
    data
  });
}
/** delete register */
export function fetchPostRegisterPageDelete(id: string) {
  return request({
    url: '/registpage/delete',
    method: 'post',
    data: { id }
  });
}
/** detail register */
export function fetchGetRegisterPageDetail(id: string | undefined) {
  return request<Api.Register.Register>({
    url: `/registpage/info/${id}`,
    method: 'get'
  });
}
