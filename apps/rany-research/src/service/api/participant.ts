import { request } from '../request';

// 获取受试者列表
export function fetchPostParticipantList(params?: Api.Participant.ParticipantSearchParams) {
  return request<Api.Participant.ParticipantList>({
    url: '/human/subject/list',
    method: 'post',
    data: params
  });
}
// 根据患者id和状态获取出入组项目列表
export function fetchParticipantProjectList(params?: Api.Participant.ParticipantProjectSearchParams) {
  return request<Api.Participant.ParticipantProjectList>({
    url: '/human/subject/patient/list/project',
    method: 'post',
    data: params
  });
}

// 添加受试者
export function addParticipant(params: Api.Participant.ParticipantAddReqVO) {
  return request<Api.Common.CommonResponseType>({
    url: '/human/subject/add',
    method: 'post',
    data: params
  });
}

// 更新受试者
export function updateParticipant(params: Api.Participant.Participant) {
  return request<Api.Common.CommonResponseType>({
    url: '/human/subject/update',
    method: 'post',
    data: params
  });
}

// 获取受试者详情
export function getParticipantDetail(id: string) {
  return request<Api.Participant.Participant>({
    url: `/human/subject/info/${id}`,
    method: 'get'
  });
}

// 出组
export function outGroupParticipant(
  params: Pick<Api.Participant.Participant, 'id' | 'userName' | 'userId' | 'patientId' | 'projectId'>
) {
  return request<Api.Common.CommonResponseType>({
    url: '/human/subject/out/group',
    method: 'post',
    data: params
  });
}

// 入组
export function inGroupParticipant(
  params: Pick<
    Api.Participant.Participant,
    'id' | 'userName' | 'userId' | 'patientId' | 'projectId' | 'number' | 'status'
  >
) {
  return request<Api.Common.CommonResponseType>({
    url: '/human/subject/in/group',
    method: 'post',
    data: params
  });
}
