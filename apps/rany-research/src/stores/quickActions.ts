export interface QuickAction {
  title: string;
  index: string;
  icon: string;
  status: boolean;
}

export const useQuickActionsStore = defineStore('quickActions', () => {
  // 快捷操作数据
  const quickActions = ref<QuickAction[]>([
    { title: '用户', index: 'user', icon: IconRyMenuUser, status: false },
    {
      title: '管理端',
      index: 'manage',
      icon: IconRyNavMonitor,
      status: false
    },
    {
      title: '工作台',
      index: 'workspace',
      icon: IconRyNavBell,
      status: false
    },
    {
      title: '消息',
      index: 'message',
      icon: IconRyNavMessage,
      status: false
    },
    { title: '医生', index: 'doctor', icon: IconRyMenuU, status: false },
    { title: '目录', index: 'list', icon: IconRyMenuList, status: true },
    { title: '首页', index: 'home', icon: IconRyMenuHome, status: false },
    {
      title: '平台',
      index: 'platform',
      icon: IconRyMenuPlatform,
      status: false
    }
  ]);

  // 当前激活的按钮索引
  const activeActionIndex = ref<string | null>(null);

  // 设置按钮状态
  const setActionStatus = (index: string, status: boolean): void => {
    const action = quickActions.value.find(item => item.index === index);
    if (action) {
      action.status = status;
      activeActionIndex.value = status ? index : null;
    }
  };

  // 重置所有按钮状态
  const resetAllStatus = (): void => {
    quickActions.value.forEach(action => {
      action.status = false;
    });
    activeActionIndex.value = null;
  };

  // 切换按钮状态
  const toggleActionStatus = (index: string): void => {
    const action = quickActions.value.find(item => item.index === index);
    if (action) {
      action.status = !action.status;
      activeActionIndex.value = action.status ? index : null;
    }
  };

  return {
    quickActions,
    activeActionIndex,
    setActionStatus,
    resetAllStatus,
    toggleActionStatus
  };
});
