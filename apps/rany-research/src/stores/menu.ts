import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

interface MenuItem {
  id: string;
  icon: string;
  title: string;
  type: 'item' | 'group';
  children?: MenuItem[];
}

export const useMenuStore = defineStore('menu', () => {
  // 菜单配置
  const menuItems = ref<MenuItem[]>([
    {
      id: 'dashboard',
      icon: 'Odometer',
      title: '仪表盘',
      type: 'item'
    },
    {
      id: 'data',
      icon: 'DataLine',
      title: '数据中心',
      type: 'group',
      children: [
        {
          id: 'data-analysis',
          icon: 'TrendCharts',
          title: '数据分析',
          type: 'item'
        },
        {
          id: 'data-monitor',
          icon: 'Monitor',
          title: '数据监控',
          type: 'item'
        }
      ]
    },
    {
      id: 'system',
      icon: 'Setting',
      title: '系统管理',
      type: 'group',
      children: [
        {
          id: 'user',
          icon: 'User',
          title: '用户管理',
          type: 'item'
        },
        {
          id: 'role',
          icon: 'Lock',
          title: '角色权限',
          type: 'item'
        }
      ]
    }
  ]);

  // 获取所有菜单项（包括子菜单）
  const getAllMenuItems = computed(() => {
    const items: MenuItem[] = [];
    const traverse = (menuItem: MenuItem) => {
      items.push(menuItem);
      if (menuItem.children) {
        menuItem.children.forEach(traverse);
      }
    };
    menuItems.value.forEach(traverse);
    return items;
  });

  // 根据ID获取菜单项
  const getMenuItem = (id: string) => {
    return getAllMenuItems.value.find(item => item.id === id);
  };

  return {
    menuItems,
    getAllMenuItems,
    getMenuItem
  };
});
