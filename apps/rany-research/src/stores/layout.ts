import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { NavType } from '@/types/navigation';

export const useLayoutStore = defineStore('layout', () => {
  // 状态
  const navType = ref<NavType>('collapsed');
  const showRightPanel = ref(false);
  const activeMenu = ref('');
  const activeTab = ref('');

  // 方法
  const toggleNavType = (val?: NavType) => {
    if (val) {
      navType.value = val;
    } else {
      navType.value = navType.value === 'collapsed' ? 'expanded' : 'collapsed';
    }
  };

  const toggleRightPanel = () => {
    showRightPanel.value = !showRightPanel.value;
  };

  const setActiveMenu = (menu: string) => {
    activeMenu.value = menu;
  };

  return {
    navType,
    showRightPanel,
    activeMenu,
    activeTab,
    toggleNavType,
    toggleRightPanel,
    setActiveMenu
  };
});
