import { defineStore } from 'pinia';
import type { RouteRecordRaw } from 'vue-router';
import type { MenuItem, NavItem, Tab, UtilityNavItem } from '@/types/navigation';
import { router } from '@/router';

// 标签页配置
const TAB_CONFIG = {
  MAX_TABS: 10, // 最大打开标签数
  STORAGE_KEY: 'tabs_state' // 持久化存储的key
};

interface NavigationState {
  mainNavItems: NavItem[];
  utilityNavItems: UtilityNavItem[];
  tabs: Tab[];
  activeTab: string;
  menuItems: MenuItem[];
  cachedTabs: Set<string>; // 需要缓存的组件
  expandedMenuNodes: Set<string>; // 展开的菜单节点
  recentVisited: string[]; // 最近访问的路径
  menuHistory: string[]; // 菜单访问历史
  aiStatus: 'running' | 'unrunning'; // AI状态
}

export const useNavigationStore = defineStore('navigation', {
  state: (): NavigationState => {
    // 尝试从localStorage恢复标签页状态
    let savedTabs: Tab[] = [];
    let savedActiveTab = '';
    let savedExpandedNodes: string[] = [];
    let savedRecentVisited: string[] = [];
    let savedMenuHistory: string[] = [];
    // AI状态
    const aiStatus: 'running' | 'unrunning' = 'unrunning';

    try {
      const saved = localStorage.getItem(TAB_CONFIG.STORAGE_KEY);
      if (saved) {
        const { tabs, activeTab, expandedNodes, recentVisited, menuHistory } = JSON.parse(saved);
        if (Array.isArray(tabs) && tabs.length > 0) {
          savedTabs = tabs;
          savedActiveTab = activeTab;
        }
        if (Array.isArray(expandedNodes)) {
          savedExpandedNodes = expandedNodes;
        }
        if (Array.isArray(recentVisited)) {
          savedRecentVisited = recentVisited;
        }
        if (Array.isArray(menuHistory)) {
          savedMenuHistory = menuHistory;
        }
      }
    } catch (e) {
      console.warn('恢复标签页状态失败:', e);
    }

    return {
      mainNavItems: [
        {
          id: 'menu-list',
          title: '目录',
          icon: IconRyMenuList,
          path: '/platform/dashboard',
          type: 'toggle'
        },
        {
          id: 'menu-home',
          title: '主页',
          icon: IconRyMenuHome,
          path: '/aiChat/home',
          type: 'link'
        },
        {
          id: 'menu-platform',
          title: '平台',
          icon: IconRyMenuPlatform,
          path: '#',
          type: 'link'
        }
      ],
      utilityNavItems: [
        {
          id: 'manage',
          title: '管理端',
          icon: IconRyNavMonitor,
          type: 'link'
        },
        {
          id: 'message',
          title: '消息',
          icon: IconRyNavMessage,
          type: 'action'
        },
        {
          id: 'workspace',
          title: '工作台',
          icon: IconRyNavBell,
          type: 'link'
        },
        {
          id: 'user',
          title: '用户',
          icon: IconRyNavUser,
          type: 'action'
        }
      ],
      tabs: savedTabs,
      activeTab: savedActiveTab,
      menuItems: [],
      cachedTabs: new Set(savedTabs.filter(tab => tab.cache !== false).map(tab => tab.id)),
      expandedMenuNodes: new Set(savedExpandedNodes),
      recentVisited: savedRecentVisited,
      menuHistory: savedMenuHistory,
      aiStatus
    };
  },

  getters: {
    // 获取当前激活的标签页
    activeTabInfo: (state): Tab | undefined => {
      return state.tabs.find(tab => tab.id === state.activeTab);
    },
    // 获取AI状态
    getAiStatus: state => state.aiStatus,

    // 获取需要缓存的组件名称列表
    cachedViews: state => Array.from(state.cachedTabs),

    getMainNavItems: state => state.mainNavItems,
    getUtilityNavItems: state => state.utilityNavItems,
    getNavItemById: state => (id: string) => {
      return state.mainNavItems.find(item => item.id === id) || state.utilityNavItems.find(item => item.id === id);
    },
    getMenuItems: state => state.menuItems.filter(item => item.title && item.path),

    // 获取展开的菜单节点
    expandedNodes: state => Array.from(state.expandedMenuNodes),

    // 获取最近访问的路径
    recentVisitedPaths: state => state.recentVisited,

    // 获取菜单访问历史
    menuNavigationHistory: state => state.menuHistory,

    // 获取当前路径的面包屑
    currentBreadcrumb: state => {
      const breadcrumb: MenuItem[] = [];
      const findPath = (items: MenuItem[], targetPath: string): boolean => {
        for (const item of items) {
          if (item.path === targetPath) {
            breadcrumb.push(item);
            return true;
          }
          if (item.children && findPath(item.children, targetPath)) {
            breadcrumb.unshift(item);
            return true;
          }
        }
        return false;
      };

      if (state.activeTab) {
        const activeTabInfo = state.tabs.find(tab => tab.id === state.activeTab);
        if (activeTabInfo) {
          findPath(state.menuItems, activeTabInfo.path);
        }
      }

      return breadcrumb;
    }
  },

  actions: {
    // 更改AI状态
    changeAiStatus(status: 'running' | 'unrunning') {
      this.aiStatus = status;
    },
    // 保存标签页状态到localStorage
    saveTabsState() {
      try {
        localStorage.setItem(
          TAB_CONFIG.STORAGE_KEY,
          JSON.stringify({
            tabs: this.tabs,
            activeTab: this.activeTab,
            expandedNodes: Array.from(this.expandedMenuNodes),
            recentVisited: this.recentVisited,
            menuHistory: this.menuHistory
          })
        );
      } catch (e) {
        console.warn('保存标签页状态失败:', e);
      }
    },

    // 手动添加组件到缓存
    addComponentToCache(componentName: string) {
      if (componentName) {
        this.cachedTabs.add(componentName);
      }
    },

    // 手动从缓存中移除组件
    removeComponentFromCache(componentName: string) {
      this.cachedTabs.delete(componentName);
    },

    // 添加标签页
    addTab(tab: Tab) {
      // 检查是否已存在
      const existingTab = this.tabs.find(t => t.id === tab.id);
      if (existingTab) {
        this.activeTab = tab.id;
        // 更新已存在标签页的信息
        if (tab.meta) {
          this.updateTab(tab.id, {
            meta: { ...existingTab.meta, ...tab.meta }
          });
        }
      } else {
        // 检查是否超过最大数量
        if (this.tabs.length >= TAB_CONFIG.MAX_TABS) {
          // 移除最早打开的可关闭标签
          const removeIndex = this.tabs.findIndex(t => t.closable);
          if (removeIndex > -1) {
            this.tabs.splice(removeIndex, 1);
            this.cachedTabs.delete(this.tabs[removeIndex].id);
          } else {
            return;
          }
        }

        // 添加新标签
        this.tabs.push(tab);
        this.activeTab = tab.id;

        // 添加到缓存
        if (tab.meta?.keepAlive !== false) {
          this.cachedTabs.add(tab.id);
        }
      }

      // 添加到最近访问
      this.addToRecentVisited(tab.path);

      // 添加到菜单历史
      this.addToMenuHistory(tab.path);

      // 保存状态
      this.saveTabsState();
    },

    // 关闭标签页
    closeTab(id: string) {
      const index = this.tabs.findIndex(tab => tab.id === id);
      if (index === -1) return;

      // 如果关闭的是当前激活的标签页
      if (this.activeTab === id) {
        // 优先激活右侧标签，其次激活左侧标签
        const nextTab = this.tabs[index + 1] || this.tabs[index - 1];
        if (nextTab) {
          this.activeTab = nextTab.id;
          router.push(nextTab.path);
        } else {
          // 如果没有其他标签，跳转到home-new
          router.push({ path: '/home-new' } as any);
        }
      }

      // 从数组和缓存中移除
      this.tabs.splice(index, 1);
      this.cachedTabs.delete(id);

      // 保存状态
      this.saveTabsState();
    },

    // 关闭其他标签页
    closeOtherTabs(id: string) {
      const currentTab = this.tabs.find(tab => tab.id === id);
      if (!currentTab) return;

      // 保留不可关闭的标签和当前标签
      this.tabs = this.tabs.filter(tab => !tab.closable || tab.id === id);

      // 更新缓存
      const newCachedTabs = new Set<string>();
      this.tabs.forEach(tab => {
        if (tab.cache !== false) {
          newCachedTabs.add(tab.id);
        }
      });
      this.cachedTabs = newCachedTabs;

      // 激活保留的标签
      this.activeTab = id;
      router.push(currentTab.path);

      // 保存状态
      this.saveTabsState();
    },

    // 关闭所有可关闭的标签页
    closeAllTabs() {
      // 保留不可关闭的标签
      const remainingTabs = this.tabs.filter(tab => !tab.closable);
      this.tabs = remainingTabs;

      // 更新缓存
      const newCachedTabs = new Set<string>();
      remainingTabs.forEach(tab => {
        if (tab.cache !== false) {
          newCachedTabs.add(tab.id);
        }
      });
      this.cachedTabs = newCachedTabs;

      // 如果没有剩余标签，跳转到home-new
      if (remainingTabs.length > 0) {
        this.activeTab = remainingTabs[0].id;
        router.push(remainingTabs[0].path);
      } else {
        router.push({ path: '/home-new' } as any);
      }

      // 保存状态
      this.saveTabsState();
    },

    // 刷新标签页
    refreshTab(id: string) {
      // 从缓存中移除
      this.cachedTabs.delete(id);

      // 下一个tick重新添加到缓存
      setTimeout(() => {
        const tab = this.tabs.find(t => t.id === id);
        if (tab?.cache !== false) {
          this.cachedTabs.add(id);
        }
      }, 0);
    },

    // 更新标签页信息
    updateTab(id: string, updates: Partial<Tab>) {
      const index = this.tabs.findIndex(tab => tab.id === id);
      if (index !== -1) {
        this.tabs[index] = { ...this.tabs[index], ...updates };
        // 保存状态
        this.saveTabsState();
      }
    },

    // 移动标签页位置
    moveTab(fromIndex: number, toIndex: number) {
      if (fromIndex >= 0 && fromIndex < this.tabs.length && toIndex >= 0 && toIndex < this.tabs.length) {
        const tab = this.tabs[fromIndex];
        this.tabs.splice(fromIndex, 1);
        this.tabs.splice(toIndex, 0, tab);
        // 保存状态
        this.saveTabsState();
      }
    },

    // 初始化标签页状态（用于页面刷新后恢复）
    initTabs(tabs: Tab[], activeId: string) {
      this.tabs = tabs;
      this.activeTab = activeId;

      // 初始化缓存
      const newCachedTabs = new Set<string>();
      tabs.forEach(tab => {
        if (tab.cache !== false) {
          newCachedTabs.add(tab.id);
        }
      });
      this.cachedTabs = newCachedTabs;

      // 保存状态
      this.saveTabsState();
    },

    // 获取平台管理相关的路由结构
    getPlatManageRoutes() {
      try {
        const routes = router.options.routes as RouteRecordRaw[];
        const findPlatManageRoute = (routes: RouteRecordRaw[]): RouteRecordRaw | null => {
          for (const route of routes) {
            if (route.name === 'platManage') {
              return route;
            }
            if (route.children) {
              const found = findPlatManageRoute(route.children);
              if (found) return found;
            }
          }
          return null;
        };

        const platManageRoute = findPlatManageRoute(routes);
        if (platManageRoute) {
          // 初始化菜单项
          this.initializeMenuItems([platManageRoute]);

          // 注意：移除了自动添加医院管理标签页的逻辑
          // 仅在需要时展开默认节点
          if (this.menuItems.length > 0) {
            const firstMenuItem = this.menuItems[0];
            if (firstMenuItem) {
              this.expandedMenuNodes.add(firstMenuItem.id);
            }
          }
        }
      } catch (error) {
        console.error('初始化菜单项失败:', error);
        // 确保即使出错，UI也能正常显示
      }
    },

    // 查找默认路由（优先使用 redirect，没有则使用第一个子路由）
    findDefaultRoute(route: RouteRecordRaw): RouteRecordRaw {
      if (route.redirect) {
        return {
          name: route.name,
          path: typeof route.redirect === 'string' ? route.redirect : route.path,
          meta: route.meta,
          component: route.component || (() => {})
        } as RouteRecordRaw;
      }

      if (route.children && route.children.length > 0) {
        const firstVisibleChild = route.children.find(child => !child.meta?.hidden);
        if (firstVisibleChild) {
          return firstVisibleChild;
        }
      }

      return route;
    },

    // 关闭左侧标签页
    closeLeftTabs(id: string) {
      const index = this.tabs.findIndex(tab => tab.id === id);
      if (index > 0) {
        const leftTabs = this.tabs.slice(0, index).filter(tab => tab.closable);
        leftTabs.forEach(tab => {
          this.closeTab(tab.id);
        });
      }
    },

    // 关闭右侧标签页
    closeRightTabs(id: string) {
      const index = this.tabs.findIndex(tab => tab.id === id);
      if (index !== -1 && index < this.tabs.length - 1) {
        const rightTabs = this.tabs.slice(index + 1).filter(tab => tab.closable);
        rightTabs.forEach(tab => {
          this.closeTab(tab.id);
        });
      }
    },

    // 固定标签页
    pinTab(id: string) {
      const tab = this.tabs.find(tab => tab.id === id);
      if (tab) {
        this.updateTab(id, { closable: false });
      }
    },

    // 取消固定标签页
    unpinTab(id: string) {
      const tab = this.tabs.find(tab => tab.id === id);
      if (tab) {
        this.updateTab(id, { closable: true });
      }
    },

    // 如果需要动态修改导航项，可以添加相应的 action
    updateNavItem(id: string, updates: Partial<NavItem | UtilityNavItem>) {
      const mainNavIndex = this.mainNavItems.findIndex(item => item.id === id);
      if (mainNavIndex !== -1) {
        this.mainNavItems[mainNavIndex] = {
          ...this.mainNavItems[mainNavIndex],
          ...updates
        } as NavItem;
        return;
      }

      const utilityNavIndex = this.utilityNavItems.findIndex(item => item.id === id);
      if (utilityNavIndex !== -1) {
        this.utilityNavItems[utilityNavIndex] = {
          ...this.utilityNavItems[utilityNavIndex],
          ...updates
        } as UtilityNavItem;
      }
    },

    // 设置当前激活的标签页
    setActiveTab(id: string) {
      const tab = this.tabs.find(t => t.id === id);
      if (tab) {
        this.activeTab = id;
        router.push(tab.path);
        this.saveTabsState();
      }
    },

    addNavItem(item: NavItem | UtilityNavItem, type: 'main' | 'utility') {
      if (type === 'main') {
        this.mainNavItems.push(item as NavItem);
      } else {
        this.utilityNavItems.push(item as UtilityNavItem);
      }
    },

    removeNavItem(id: string) {
      this.mainNavItems = this.mainNavItems.filter(item => item.id !== id);
      this.utilityNavItems = this.utilityNavItems.filter(item => item.id !== id);
    },

    routeToMenuItem(route: any, parentPath: string = ''): MenuItem {
      const menuItem: MenuItem = {
        id: route.name || route.path,
        title: route.meta?.title || route.name,
        path: route.path.startsWith('/') ? route.path : `${parentPath}/${route.path}`,
        icon: route.meta?.icon,
        type: route.meta?.type || 'rany-research'
      };

      if (route.children) {
        const children = route.children
          .filter((child: any) => !child.meta?.hidden)
          .map((child: any) => {
            // 如果子路由有 redirect，使用 redirect 的路径
            const childPath = child.redirect || child.path;
            // 根据子路由路径是否以/开头决定是否需要拼接父路径
            const fullPath = childPath.startsWith('/')
              ? childPath
              : `${menuItem.path}/${childPath}`.replace(/\/+/g, '/');

            return this.routeToMenuItem(
              {
                ...child,
                path: fullPath
              },
              menuItem.path
            );
          })
          .filter((item: MenuItem) => item.title && item.path);

        if (children.length > 0) {
          menuItem.children = children;
        }
      }

      return menuItem;
    },

    initializeMenuItems(routes: any[]) {
      this.menuItems = routes
        .filter(route => !route.meta?.hidden)
        .map(route => {
          // 如果路由有 redirect，使用 redirect 的路径
          const routePath = route.redirect || route.path;
          return this.routeToMenuItem(
            {
              ...route,
              path: routePath
            },
            ''
          );
        })
        .filter(item => item.title && item.path);
    },

    findMenuItemByPath(path: string): MenuItem | null {
      const find = (items: MenuItem[]): MenuItem | null => {
        for (const item of items) {
          if (item.path === path) {
            return item;
          }
          if (item.children) {
            const found = find(item.children);
            if (found) {
              return found;
            }
          }
        }
        return null;
      };
      return find(this.menuItems);
    },

    // 更新展开的菜单节点
    updateExpandedMenuNodes(nodeId: string, expanded: boolean) {
      if (expanded) {
        this.expandedMenuNodes.add(nodeId);
      } else {
        this.expandedMenuNodes.delete(nodeId);
      }
      this.saveTabsState();
    },

    // 添加到最近访问
    addToRecentVisited(path: string) {
      const index = this.recentVisited.indexOf(path);
      if (index > -1) {
        this.recentVisited.splice(index, 1);
      }
      this.recentVisited.unshift(path);

      // 限制最大数量
      if (this.recentVisited.length > 10) {
        this.recentVisited.pop();
      }

      this.saveTabsState();
    },

    // 添加到菜单历史
    addToMenuHistory(path: string) {
      this.menuHistory.push(path);

      // 限制最大数量
      if (this.menuHistory.length > 50) {
        this.menuHistory.shift();
      }

      this.saveTabsState();
    }
  }
});
