/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Region {
    type RegionList = {
      id: string;
      name: string;
    }[];
  }
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page number */
      pageNo: number;
      /** page size */
      size: number;
      /** page size */
      pageSize: number;
      /** total count */
      total: number;
      /** orders */
      orders?: [];
      /** optimize count sql */
      optimizeCountSql?: boolean;
      /** search count */
      searchCount?: boolean;
      /** max limit */
      maxLimit?: null;
      /** count id */
      countId?: null;
      /** pages */
      pages?: number;
    }

    interface BaseRequestParams {
      userName: string;
      userId: string;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'> & Partial<BaseRequestParams>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "0": disabled
     */
    type EnableStatus = 1 | 0;

    /** common record */
    type CommonRecord<T = any> = T & {
      /** record id */
      id?: string;
      /** record creator */
      createBy?: string;
      /** record create time */
      createTime?: string;
      /** record updater */
      updateBy?: string;
      /** record update time */
      updateTime?: string;
      /** record status 每个类型都有多种状态，使用number限定类型，不需要限定具体值 */
      status?: number;
    };

    /** common type */
    interface CommonResponseType<T = any> {
      code: number;
      message: string;
      data: T;
      key?: null;
      status?: string;
    }
    type CommonOption = {
      label: string;
      value: string;
    };
    type CascaderOption = CommonOption & {
      children?: CascaderOption[];
    };
    type UploadFileParams = {
      /** 文件创建人 */
      createBy?: string;
      /** 文件创建时间 */
      createTime?: string;
      /** 文件地址 */
      fileAddress?: string;
      /** 文件大小 */
      fileSize?: number;
      /** 文件ID */
      id?: string;
      /** 文件名称 */
      name?: string;
      /** 关联ID */
      relevanceId?: string;
      /** 关联类型 */
      relevanceType?: string;
      /** 来源ID */
      sourceId?: string;
      /** 来源名称 */
      sourceName?: string;
    } & Common.BaseRequestParams;
    type UploadFileSearchParams = CommonType.RecordNullable<
      Pick<Api.Common.UploadFileParams, 'relevanceId' | 'relevanceType' | 'name'> & CommonSearchParams
    >;
    type UploadFileList = Common.PaginatingQueryRecord<UploadFileParams>;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      userId: string;
      userName: string;
      roles: string[];
      buttons: string[];
    }
  }
  namespace ProgramItem {
    type ItemType = {
      /** 类别：1-检验；2-检查；3-用药；4-手术；5-组套 */
      labour: 1;
      examination: 2;
      medicine: 3;
      operation: 4;
      stack: 5;
    };
    type ModuleType = {
      /** 所属模块类型：0-诊疗计划；1-方案；2-组套 */
      plan: 0;
      program: 1;
      stack: 2;
    };
    interface ProgramItemRecord {
      id?: string;
      /** 别名(组套信息) */
      alias?: string;
      /** 定制小项关联小项ID */
      content?: string;
      itemId?: string;
      /** 注释，注意事项 */
      comment?: string;
      /** 减停 */
      cutOut?: number;
      /** 英文名(组套信息) */
      enName?: string;
      /** 频次 */
      frequency?: string;
      /** 科室ID(组套信息) */
      hospDeptId?: string;
      /** 用药计划：1-4(1到4天)，3（第3天），6（第6天） */
      mediPlan?: string;
      /** 给药途径 */
      mediRoute?: string[] | string;
      /** 所属模块ID，根据模块类型存储不同的ID */
      moduleId: string;
      /** 所属模块类型：0-诊疗计划；1-方案；2-组套 */
      moduleType: number;
      /** 名称 */
      name: string;
      /** 排序 */
      orderNumber: number;
      /** 关联项ID，关联数据平台用药，检查，检验，手术ID，组套ID（stack.id） */
      refId?: string;
      /** 缩写(组套信息) */
      shortName?: string;
      /** 单次用量 */
      singleDose?: number;
      /** 类别：1-检验；2-检查；3-用药；4-手术；5-组套 */
      type: ItemType[keyof ItemType];

      /** 单位 */
      unit?: string;
      stackItems?: ProgramItemRecord[];
    }

    interface AddCustomItemVO {
      id?: string;
      /** 定制注释，注意事项 */
      comment?: string;
      /** 定制计划，改变一天（例：3）或几天（例：3,4,3-6）的值 定制计划 */
      days: string;
      /** 定制频次 */
      frequency?: string;
      /** 小项ID，关联item.id 小项ID */
      itemId: string;
      /** 定制给药途径 */
      mediRoute?: string;
      /** 定制单次用量 */
      singleDose?: number;
    }
  }

  namespace Program {
    /** 页码,-1表示全部 */
    type Status = {
      deleted: -1;
      drafted: 0;
      published: 1;
      disabled: 2;
    };
    type SearchParams = Partial<{
      name: string;
      /** 危险度分型 */
      riskId?: string[];
      /** 状态:0-草稿；1-发布；2-停用 */
      status?: number | null;
      restriction: number;
      clinicPlanId?: string;
      stageId?: string;
    }> &
      Common.CommonSearchParams;
    interface ProgramRecord {
      comment: string;
      createBy: string;
      createTime: string;
      dayCount: number;
      delFlag: string;
      diseaseId: string;
      diseaseName: string;
      itemNames: string[];
      name: string;
      riskId: string;
      riskNames: string[];
      status: number;
      // :0-私有;1-公开
      restriction: number;
      stageId: string;
      clinicPlanId: string;
    }
    type ProgramItem = Common.CommonRecord<Partial<ProgramRecord>>;
    type ProgramList = Common.PaginatingQueryRecord<ProgramItem>;
    type CopyProgramParams = {
      solutionId: string;
    };

    // Items
    type ItemResponseData = {
      comments: string[];
      headers: Header[];
      itemMap: Record<string, ProgramItem.ProgramItemRecord>;
      stacks: Record<string, ProgramItem.ProgramItemRecord>;
      tableData: TableDatum[];
    };
    interface TableDatum {
      [property: string]: any;
      day: number;
      itemCustomMap: Record<string, ProgramItem.ProgramItemRecord>;
      itemActualMap: Record<string, Bag.ActualValueItem>;
    }
    interface Header {
      children?: Header[];
      key?: string;
      title: string;
      unit?: string;
      comment?: string;
    }
  }

  namespace ClinicPlan {
    type SearchParams = Partial<{
      name?: string;
      clinicPlanName?: string;
      diseaseId: string;
      riskId: string;
      status: string | number;
      projectId: string;
      restriction: number;
    }> &
      Common.CommonSearchParams;

    type PanelType = {
      name: string;
      temporaryName: string;
      isEditing: boolean;
      id?: string;
      orderNumber?: number;
      solutions?: Api.ClinicPlan.ClinicSolutionItem[];
    };
    type CopyClinicPlanParams = {
      clinicPlanId: string;
    };

    /** ClinicPlanAddVO */
    export interface AddVo {
      /** 疾病ID */
      diseaseId?: string;
      /** 名称 */
      name: string;
      /** 试验项目ID */
      projectId?: string;
      /** 受限状态：0-私有；1-公开 受限状态，0-私有；1-公开。如果0，则必须传projectId；如果1，则不必须传projectId */
      restriction: number;
      /** 危险度ID,多个英文逗号分隔 */
      riskId?: string;
      /** 组别 */
      tranches?: string;
    }
    export interface LinkProjectVo {
      clinicPlanId?: string;
      /** 试验项目ID */
      projectId?: string;
    }

    type RestrictionStatus = {
      /** 0-私有；1-公开。如果0，则必须传projectId；如果1，则不必须传projectId */
      privateStatus: 0;
      publicStatus: 1;
    };
    interface Record extends AddVo {
      diseaseName?: string;
      id?: string;
      riskName?: string[];
      projectName?: string;
      status?: number;
      weekCount?: number;
      intervention?: string;
      createdTime?: string;
      updateTime?: string;
    }
    type ClinicPlanItem = Common.CommonRecord<Record>;
    type ClinicPlanList = Common.PaginatingQueryRecord<ClinicPlanItem>;

    type ClinicPlanDetail = {
      cpItems: any[];
      clinicPlan: Api.ClinicPlan.ClinicPlanItem & { stages: Api.ClinicStage.StageItem[] };
    };

    type ClinicSolutionItem = {
      solutionId: string;
      weekCount: number;
      soluName: string;
      dayCount: number;
      soluSummary: string[];
      restDayCount: number | null;
      soluItems: any | null; // Specify a more precise type if known
      comment: string;
      soluType: number;
      stageSoluId: string;
    };
  }

  namespace ClinicStage {
    type StageType = {
      /** 类型：0-阶段；1-方案；2-休疗 */
      stage: 0;
      solution: 1;
      rest: 2;
    };
    /** ClinicStageAddVO */
    interface StageVO {
      /** 诊疗计划ID，关联clinic_plan.id */
      clinicPlanId: string;
      /** 顺序号 */
      orderNumber: number;
      /** 相关信息，根据类型确定本字段内容：0-阶段名称；1-方案ID，关联solution.id；2-休疗的天数 */
      /** 类型：0-阶段；1-方案；2-休疗 */
      relation: string;
      type: number;
      id?: string;
    }
    interface StageProgramVO {
      /** 诊疗阶段.id */
      stageId: string;
      /** 顺序号 */
      orderNumber: number;
      /** 相关信息，根据类型确定本字段内容1-方案ID，关联solution.id；2-休疗的天数 */
      relation: string;
      /** 类型1-方案；2-休疗 */
      type: number;
    }

    interface StageItem {
      id: string;
      clinicPlanId: string;
      type: number;
      relation: string;
      sbagId?: string;
      orderNumber: number;
      createTime: string; // Consider using Date type if you want to handle dates
      createBy: string; // Adjust type if you want to parse the JSON array
      updateTime: string; // Consider using Date type if you want to handle dates
      updateBy: string; // Adjust type if you want to parse the JSON array
      name: string;
      solutions: Api.ClinicPlan.ClinicSolutionItem[]; // Specify a more precise type if known
      solution: any; // Specify a more precise type if known
      restDayCount: number | null; // Adjust based on expected type
    }
  }

  /** 专科袋 */
  namespace Bag {
    type StageType = {
      /** 类型：1-方案；2-休疗 */
      solution: 1;
      rest: 2;
    };
    type RandomizeParams = {
      clinicPlanId: string;
      sbagSoluId: string;
      tranches: string;
      patientId: string;
    };

    interface BindSolutionToStageParams {
      /** 诊疗阶段ID */
      stageId: string;
      /** 插入点（前一个节点），方案/休疗ID，如为空，则加到末尾 */
      insertPoint?: string;
      /** 修改，删除依据 */
      pursuant?: string;
      /** 相关信息:1-方案ID，关联solution.id；2-休疗的天数 */
      relation: string | number;
      /** 类型:1-方案；2-休疗 */
      type: StageType[keyof StageType];
    }
    interface GenerateBagParams {
      /** 诊疗计划ID，按顺序英文逗号分隔 诊疗计划ID，为空则说明创建空专科袋 */
      clinicPlanId?: string;
      /** 受试者ID */
      hsuId?: string;
      /** 患者ID */
      patientId: string;
      /** 登记首页，来源是regist_page.template，可自由编辑 登记首页 */
      registPage: string;
      /** 专科袋生成插入位置（追加此方案之后） */
      sbagSoluId?: string;
      /** 专科袋开始时间 专科袋开始时间,格式：yyyy-MM-dd HH:mm:ss */
      startTime?: string;
      /** 组别 */
      tranches?: string;
    }
    interface ActualValueParams {
      /** 实际诊疗日期 */
      actualDay: string;
      /** 天数，哪一天 天数 */
      dayNum: number;
      /** 实际频次 */
      frequency: string;
      /** 实际给药途径 */
      mediRoute: string | string[];
      /** 专科袋小项ID，关联sbag_item.id 专科袋小项ID */
      sbagItemId: string;
      /** 实际单次用量(检验类是json格式) 实际单次用量 */
      singleDose: string | any;
    }
    interface ActualValueItem {
      actualDay: string;
      createBy: string;
      createTime: string;
      unit: string;
      dayNum: number;
      delFlag: number;
      frequency: string;
      id: string;
      mediRoute: string | string[];
      sbagItemId: string;
      singleDose: string;
      updateBy: string;
      updateTime: string;
    }

    interface PatientBagResult {
      cpItems: [];
      sbag: {
        clinicPlanId: string;
        createBy: string;
        createTime: string;
        delFlag: number;
        diseaseId: string;
        diseaseName: string;
        id: string;
        patientId: string;
        registPage: string;
        riskId: string;
        riskName: string;
        stages: Api.ClinicStage.StageItem[];
        startTime: string;
        updateBy: string;
        updateTime: string;
        validTime: string;
      };
    }
  }
  /**
   * namespace Patient
   *
   * backend api module: "Patient"
   */
  namespace Patient {
    /** 定义患者信息的接口，用于标准化患者的相关数据 */
    /** PatientAddReqVO */
    interface PatientBase {
      sbagFlag?: number;
      sbagId?: string;
      /** 出生日期 */
      birthday?: string;
      /** 出生地，省 */
      birthplace?: string;
      /** 证件号 */
      credNumber?: string;
      /** 证件类型，默认身份证，传字典id */
      credType?: string;
      /** 最终诊断,传id */
      diagnosisFinal?: string;
      /** 初步诊断 */
      diagnosisInit?: string;
      /** 诊断日期 */
      diagnosisTime?: string;
      /** 现住址 */
      familyAddress?: string;
      /** 性别：男，女，不明,，传字典id */
      gender?: string;
      /** 就诊医院 */
      hospitalId?: string;
      /** 试验项目名称 */
      projectName?: string;
      /** 婚姻状况 */
      maritalStatus?: string;
      /** 患者姓名 */
      name?: string;
      /** 民族，传字典id */
      nation?: string;
      /** 民族名称 */
      nationName?: string;
      /** 籍贯 */
      nativePlace?: string;
      /** 门诊号 */
      outpatientNumber?: string;
      /** 户口地址 */
      permanentAddress?: string;
      // 初步诊断
      preliminaryDiagnosis?: string;
      /** 联系电话 */
      phoneNumber?: string;
      /** 亲属姓名 */
      relativeName?: string;
      /** 临床危险度ID */
      riskId?: string;
      id?: string;
      /** 身高 */
      height?: number;
      /** 体重 */
      weight?: number;
      hospitalName?: string;
      riskName?: string;
      treatmentId?: string;
      /** 是否有出组按钮：1有，其他没有 */
      groupStatus?: number;
      /** 是否有推荐出入组按钮：0 都没有，1有推荐入组，2有推荐出组，3 两个都有 */
      aiGroupStatus?: number;
      diseaseId?: string;
      treatment: {
        diagnosisFinal: string;
        diagnosisInit: string;
        id: string;
        hospitalId: string;
        outpatientNumber: string;
        currFlag: string;
        createTime: string;
        createBy: string;
        delFlag: number;
        riskId: string;
        patientId: string;
        diagnosisTime: string;
        diagnosisFinals: string[];
      };
      diagnosisFinals: string[];
    }

    interface Treatment {
      createBy: string;
      createTime: string;
      currFlag: string;
      delFlag: number;
      diagnosisFinal: string;
      diagnosisInit: string;
      diagnosisTime: string;
      hospitalId: string;
      id: string;
      hospitalName: string;
      riskName: string;
      treatmentId?: string;
      treatment: {
        diagnosisFinal: string;
        diagnosisInit: string;
        id: string;
        hospitalId: string;
        outpatientNumber: string;
        currFlag: string;
        createTime: string;
        createBy: string;
        delFlag: number;
        riskId: string;
        patientId: string;
        diagnosisTime: string;
      };
    }

    interface Nation {
      id: string;
      dictId: string;
      itemText: string;
      itemValue: string;
      itemSort: number;
      remark: string;
      parentId: string;
      parentName: null;
      childList: null;
    }

    type PatientAddReqVO = Common.BaseRequestParams & PatientBase;

    type PatientItem = Common.CommonRecord<PatientBase>;
    type PatientNotion = Common.CommonRecord<Nation>;
    type PatientList = Common.PaginatingQueryRecord<PatientItem>;
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'> & {
      name?: string;
      hospitalName?: string;
      projectId?: string;
    };
    type PatientHisSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'> & {
      patientId: string;
      treatmentId?: string;
    };
    type PatientHis = Common.CommonRecord<{
      id: string;
      patientId: string;
      treatmentId: string;
      riskId: string;
      riskName: string;
      createTime: string;
      delFlag: number;
    }>;
    type PatientHisList = Common.PaginatingQueryRecord<PatientHis>;
    type TreatmentSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'> & {
      patientId: string;
      treatmentId: string;
    };
    type TreatmentBase = Common.CommonRecord<{
      id: string;
      patientId: string;
      hospitalId: string;
      outpatientNumber: string;
      diagnosisInit: string;
      riskId: string;
      currFlag: string;
      createTime: string;
      delFlag: number;
      hospital: {
        id: string;
        name: string;
        logo: string;
        iphoneNumber: string;
        email: string;
        address: string;
        province: string;
        coordinate: string;
        createTime: string;
        updateTime: string;
        updateBy: string;
        delFlag: number;
        status: number;
      };
      risk: {
        id: string;
        name: string;
        parentId: string;
      };
    }>;

    type TreatmentList = Common.PaginatingQueryRecord<TreatmentBase>;
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }
  /**
   * namespace Risk
   *
   * backend api module: "risk"
   */
  namespace Risk {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'>;
    /** risk */
    type Risk = Common.CommonRecord<{
      name?: string; // 危险度名称
      risk?: string; // 父级危险度 自关联ID，顶级为-1
      shortName?: null | string; // 疾病简称
      groupCondition?: string; // 分组条件
      projectName?: string; // 试验项目id
      riskId?: string | string[];
      diseaseId?: string; // 疾病id
      projectId?: string;
      riskName?: string;
      projectIds?: string[];
      diseaseRiskId?: string;
    }>;
    /** parent */
    type Parent = Common.CommonRecord<{
      parentName: string;
      parentCode: string;
    }>;
    /** risksearch params */
    type RiskSearchParams = CommonType.RecordNullable<
      Pick<Api.Risk.Risk, 'shortName' | 'name' | 'riskId' | 'projectId'> & CommonSearchParams
    >;
    type RiskSearchParamsOfProject = CommonType.RecordNullable<Pick<Api.Risk.Risk, 'projectId'>>;
    /** risklist */
    type RiskList = Common.PaginatingQueryRecord<Risk>;
    /** riskParent-list */
    type AllProjectList = {
      id: string;
      name: string;
    };
    /** riskbefore del params */
    type RiskBeforeDelParams = CommonType.RecordNullable<Pick<Api.Risk.Risk, 'id'>>;
    /** risk-list-tree */
    type RiskListTree = {
      id: string;
      name: string;
      childList?: RiskListTree[];
    };
  }
  /**
   * namespace Disease
   *
   * backend api module: "disease"
   */
  namespace Disease {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'>;
    /** disease */
    type Disease = Common.CommonRecord<{
      abbrName?: string;
      alias?: string;
      enName?: string;
      idcCode?: string | null;
      name?: string;
      parentId?: string | null;
      shortName?: null | string;
      createBy?: string;
      createTime?: string;
      updateBy?: string;
      updateTime?: string;
      delFlag?: number;
      division?: string;
      hospitalId?: string;
      id?: string;
      index?: number;
    }>;
    /** parent */
    type Parent = Common.CommonRecord<{
      parentName: string;
      parentCode: string;
    }>;
    /** disease search params */
    type DiseaseSearchParams = CommonType.RecordNullable<
      Pick<Api.Disease.Disease, 'shortName' | 'name' | 'parentId'> & CommonSearchParams
    >;
    /** disease list */
    type DiseaseList = Common.PaginatingQueryRecord<Disease>;
    /** disease Parent-list */
    type AllParentList = Pick<Parent, 'id' | 'parentName' | 'parentCode'>;
    /** disease before del params */
    type DiseaseBeforeDelParams = CommonType.RecordNullable<Pick<Api.Disease.Disease, 'id'>>;
  }
  /**
   * namespace Hospital
   *
   * backend api module: "hospital"
   */
  namespace Hospital {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'>;
    /** hospital */
    type Hospital = Common.CommonRecord<{
      name: string; // 医院名称
      codeName?: string | null; // 医院代码
      province?: string; // 省
      address?: string; // 地址
      coordinate?: string; // 坐标
      iphoneNumber?: string; // 联系电话
      email?: string; // 邮箱
      projectNumber?: number; // 关联研究数
      userNumber?: number; // 关联用户数
      logo?: string | null; // logo
      doctorCount?: number | null; // 医生数量
      status: number; // 状态
      delFlag?: number; // 删除标志,
      longitude?: string; // 经度
      latitude?: string; // 纬度
    }>;
    /** hospital search params */
    type HospitalSearchParams = CommonType.RecordNullable<
      Pick<Api.Hospital.Hospital, 'status' | 'name'> & CommonSearchParams
    >;
    /** hospital list */
    type HospitalList = Common.PaginatingQueryRecord<Hospital>;
    /** 项目关联下的医院-添加 */
    type ProjectHospitalAddParams = Common.CommonRecord<{
      attachment?: string; // 附件路径多个英文逗号隔开
      estimatedHuman?: number; // 预计受试者人数
      hospitalId?: string; // 医院ID
      projectId?: string; // 项目ID
      codeName?: string; // 医院代号
      name?: string; // 医院名称
      users?: Array<{
        hospitalId: string; // 医院ID
        id: string; // ID
        projectId: string; // 项目ID
        userId: string; // 用户ID
      }>;
      existingNumber?: number; // 已有受试者人数
      humanNumber?: number; // 已有成员
    }>;
    /** 项目关联下的医院-查询列表 */
    type HosOfProjectSearchParams = CommonType.RecordNullable<
      Pick<Api.Hospital.ProjectHospitalAddParams, 'projectId'>
    > &
      CommonSearchParams;
    type HospitalOfProjectList = Common.PaginatingQueryRecord<ProjectHospitalAddParams>;
  }
  /**
   * namespace Suite
   *
   * backend api module: "suite"
   */
  namespace Suite {
    /** 定义套项目信息的接口，用于标准化套项目相关数据 */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'>;
    /** suite */
    type Suite = Common.CommonRecord<{
      name?: string | null; // 项目名称
      shortName?: null | string; // 缩写
      alias?: string; // 别名
      diseaseId?: string | null; // 疾病ID
      diseaseName?: string; // 疾病分型
      enName?: string; // 英文名称
      description?: string; // 项目描述
      subItems?: string[]; // 项目详情,
      dieaseId?: string;
    }>;
    /** suiteItem */
    type SuiteItem = {
      /** 别名(组套信息) */
      alias?: string;
      /** 注释，注意事项 */
      comment?: string;
      /** 减停 */
      cutOut?: string;
      /** 英文名(组套信息) */
      enName?: string;
      /** 频次 */
      frequency?: string;
      /** 科室ID(组套信息) */
      hospDeptId?: string;
      /** 用药计划（至少包含一个数字，并且可以选择性地包含逗号和横杠）如：1-4(1到4天)，3（第3天），6（第6天） */
      mediPlan?: string;
      /** 给药途径 */
      mediRoute?: string;
      /** 所属模块ID，根据模块类型存储不同的ID */
      moduleId: string;
      /** 所属模块类型：0-诊疗计划；1-方案；2-组套 */
      moduleType: number;
      /** 名称 */
      name: string;
      /** 排序 */
      orderNumber: number;
      /** 关联项ID，关联数据平台用药，检查，检验，手术ID，组套ID（stack.id） */
      refId?: string;
      /** 缩写(组套信息) */
      shortName?: string;
      /** 单次用量 */
      singleDose?: number;
      /** 类别：1-检验；2-检查；3-用药；4-手术；5-组套 */
      type: number;
      /** 单位 */
      unit?: string;
      id?: string;
    };

    /** suiteItem-ResponseType */
    type SuiteItemResponseType = Common.CommonResponseType<SuiteItem>;

    /** parent */
    // type Parent = Common.CommonRecord<{
    //   parentName: string;
    //   parentCode: string;
    // }>;
    /** suitesearch params */
    type SuiteSearchParams = CommonType.RecordNullable<Pick<Api.Suite.Suite, 'name' | 'dieaseId'> & CommonSearchParams>;
    /** suitelist */
    type SuiteList = Common.PaginatingQueryRecord<Suite>;
    /** suiteParent-list */
    // type AllParentList = Pick<Parent, 'id' | 'parentName' | 'parentCode'>;
    /** suitebefore del params */
    type SuiteBeforeDelParams = CommonType.RecordNullable<Pick<Api.Suite.Suite, 'id'>>;
    /** suite-list-tree */
    type SuiteListTree = {
      id: string;
      name: string;
      children: SuiteListTree[];
    };
    /** item type */
    type ItemType = {
      value: number;
      textEN: string;
      text: string;
    };
  }
  /**
   * namespace Dept
   *
   * backend api module: "dept"
   */
  namespace Dept {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'>;
    /** department */
    type Department = Common.CommonRecord<{
      name: string; // 科室名称
      division: string | string[]; // 科别
      parentId: string; // 上级组织
      hospitalId?: string; // 医院ID
    }>;
    /** department search params */
    type DepartmentSearchParams = CommonType.RecordNullable<
      Pick<Api.Dept.Department, 'hospitalId'> & CommonSearchParams
    >;

    /** department list */
    type DepartmentList = Common.PaginatingQueryRecord<Department>;
  }
  /**
   * namespace Register
   *
   * backend api module: "register"
   */
  namespace Register {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'>;
    /** register */
    type Register = Common.CommonRecord<{
      name?: string; // 登记首页名称
      projectName?: string; // 试验项目
      filePath?: string; // 文件
      template?: object; // 模板
    }>;
    /** register search params */
    type RegisterSearchParams = CommonType.RecordNullable<
      Pick<Api.Register.Register, 'name' | 'status'> & CommonSearchParams
    >;
    /** register list */
    type RegisterList = Common.PaginatingQueryRecord<Register>;
  }
  /**
   * namespace Experiment
   *
   * backend api module: "experiment"
   */
  namespace Experiment {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'>;
    /** experiment */
    type Experiment = Common.CommonRecord<{
      actualEndTime?: string; // 项目结束时间
      actualStartTime?: string; // 项目开始时间
      blindMethod?: string; // 盲法
      delFlag?: number; // 删除标志：0-正常；1-删除
      endTime?: string; // 项目实施结束时间
      estimatedHosp?: number; // 预计医院数
      estimatedHuman?: number; // 预计受试者（样本）量
      exclCondition?: string; // 排除条件
      inCondition?: string; // 入组条件
      outCondition?: string; // 出组条件
      purpose?: string; // 研究目的
      registNumber?: string; // 注册号
      rpaId?: string; // 登记首页ID
      rpaTemplate?: Record<string, any>; // 模板
      stage?: string; // 研究阶段
      startTime?: string; // 项目实施开始时间
      hospitalList?: string[]; // 医院
      name?: string; // 试验名称
      type?: string; // 研究类型
      shortName?: string; // 试验简称
      implementTime?: string; // 实施时间,
      disease?: string; // 疾病,
      projectDiseList?: string[]; // 疾病风险列表
      diseaseList?: {
        diseasedId: string;
      }[];
      design?: string; // 设计
      diseaseName?: string;
      typeName?: string;
      stageName?: string;
      projectHospitalList?: {
        hospitalId: string;
        estimatedHuman: number;
      }[];
      hospitalId?: string;
    }>;
    type ExperimentDetail = Experiment & {
      useHospitalCount?: string;
      useHospitalPercentage?: string;
      useSampleCount?: string;
      userSamplePercentage?: string;
    };
    /** experiment dict */
    type ExperimentDict = {
      dictId: string;
      itemText: string;
      itemValue: string;
      itemSort?: number;
      parentId?: string;
      status?: number;
      id?: string;
    };
    /** experiment search params */
    type ExperimentSearchParams = CommonType.RecordNullable<
      Pick<Api.Experiment.Experiment, 'hospitalId' | 'status' | 'stage' | 'name' | 'type'> & CommonSearchParams
    >;
    /** experiment list */
    type ExperimentList = Common.PaginatingQueryRecord<Experiment>;
    /** stage */

    type DiseRiskOption = {
      label: string;
      value: {
        diseasedId: string;
        projectId?: string;
      };
    };
    type ExperimentDeleteParams = Common.BaseRequestParams & {
      id: string;
    };
    type ExperimentExitHospitalParams = ExperimentDeleteParams & {
      hospitalId: string;
    };
    type ExperimentStatus = Common.EnableStatus | 2;

    type ExperimentHospitalMemberAddParams = {
      hospitalId?: string;
      projectId: string;
      userId: string;
      name?: string;
    };

    type ExperimentHosMemberSearchParams = CommonType.RecordNullable<
      Pick<Api.Experiment.ExperimentHospitalMemberAddParams, 'name' | 'projectId' | 'hospitalId'> & CommonSearchParams
    >;
    type ExperimentHosMember = Common.CommonRecord<{
      id?: string;
      projectId?: string;
      hospitalId?: string;
      userId: string;
      userName: string;
      password?: string;
      email?: string;
      mobile?: string;
      status: string;
      personTypes?: string[];
      deptName?: string;
      createTime?: string;
      modifyTime: string;
      lastLoginTime: string;
      description?: string;
      avatar: string;
      nickName: string;
      type: string;
      department: number;
      smsSystem: boolean;
      smsTodo: boolean;
      emailSendMark: boolean;
      wardId: number;
      pwdStatus: number;
      faceStatus: string;
      fingerprintStatus: string;
      sex: number;
      roleId: string;
      hospitalName?: string;
    }>;
    type ExperimentHosMemberList = Common.PaginatingQueryRecord<ExperimentHosMember>;

    type ExperimentAddProjectRiskData = {
      diseaseRiskId: string;
      projectId: string;
    };
  }

  /**
   * namespace Participant
   *
   * backend api module: "participant"
   */
  namespace Participant {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'pageNo' | 'pageSize'>;
    type Participant = Common.CommonRecord<{
      age?: string;
      sbagId?: string;
      /** 出生日期 */
      birthday?: string | null;
      /** 出生地，省 */
      birthplace?: string | null;
      /** 证件号 */
      credNumber?: string | null;
      /** 证件类型，默认身份证，传字典id */
      credType?: string | null;
      /** 最终诊断,传id */
      diagnosisFinal?: string | null;
      /** 初步诊断 */
      diagnosisInit?: string | null;
      /** 诊断日期 */
      diagnosisTime?: string | null;
      /** 现住址 */
      familyAddress?: string | null;
      /** 性别：男，女，不明,，传字典id */
      gender?: string | null;
      /** 就诊医院 */
      hospitalId?: string | null;
      /** 婚姻状况 */
      maritalStatus?: string | null;
      /** 民族，传字典id */
      nation?: string | null;
      /** 籍贯 */
      nativePlace?: string | null;
      /** 门诊号 */
      outpatientNumber?: string | null;
      /** 户口地址 */
      permanentAddress?: string | null;
      /** 联系电话 */
      phoneNumber?: string | null;
      /** 亲属姓名 */
      relativeName?: string | null;
      name?: string;
      attachment?: string;
      clinicPlanId?: string;
      delFlag?: number;
      number?: string;
      patientId?: string | null;
      projectHospitalId?: string | null;
      patientHospitalId?: string | null;
      projectId?: string;
      projectName?: string;
      rpaId?: string;
      rpaTemplate?: Record<string, any>;
      sbagGenFalg?: number;
      userId?: string;
      userName?: string;
      patientName?: string;
      sex?: string;
      riskId?: string;
      tranches?: string;
      specialBag?: string;
      attachments?: string[];
      height?: string;
      weight?: string;
      patient: {
        id?: string;
        BSA?: string;
        birthday?: string | null;
        birthplace?: string;
        credNumber?: string;
        credType?: string;
        bsa?: string;
        diagnosisFinal?: string;
        diagnosisInit?: string;
        diagnosisTime?: string | null;
        familyAddress?: string;
        gender?: string;
        hospitalId?: string;
        maritalStatus?: string;
        name?: string;
        nation?: string;
        nationName?: string;
        nativePlace?: string;
        outpatientNumber?: string;
        permanentAddress?: string;
        phoneNumber?: string;
        maritalStatusName?: string;
        relativeName?: string;
        riskId?: string;
        userId?: string;
        userName?: string;
        height?: string;
        weight?: string;
        sbagId?: string;
        hospital?: {
          name: string;
        };
        diseaseName?: string;
        riskName?: string;
      };
      treatment: {
        createBy?: string;
        createTime?: string;
        currFlag?: string;
        delFlag?: number;
        diagnosisFinal?: string;
        diagnosisInit?: string;
        diagnosisTime?: string;
        hospitalId?: string;
        id?: string;
        outpatientNumber?: string;
        patientId?: string;
        riskId?: string;
      };
      risk: {
        id: string;
        name: string;
        parentId: string;
      };
      statusName?: string;
      sexName?: string;
      hospitalName?: string;
      createUser?: string;
      diseaseName?: string;
      diseaseRiskName?: {
        name: string;
      };
    }>;
    type ParticipantProject = Common.CommonRecord<{
      id: string;
      name: string;
      patientId?: string;
      projectHospitalId?: string;
      status?: number; // 0-出组 1-入组
    }>;
    type ParticipantProjectSearchParams = CommonType.RecordNullable<
      Pick<Api.Participant.ParticipantProject, 'patientId' | 'projectHospitalId' | 'status'> & CommonSearchParams
    >;
    type ParticipantProjectList = Common.PaginatingQueryRecord<ParticipantProject>;
    type ParticipantAddReqVO = Common.BaseRequestParams & Participant;
    type ParticipantList = Common.PaginatingQueryRecord<Participant>;
    type ParticipantSearchParams = CommonType.RecordNullable<
      Pick<
        Api.Participant.Participant,
        'number' | 'projectHospitalId' | 'status' | 'projectId' | 'patientId' | 'patientHospitalId'
      > &
        CommonSearchParams
    >;
  }
}

declare namespace DataPlatformApi {
  namespace Medicine {
    /**
     * ItemRecord
     *
     * 药品信息
     */
    type ItemRecord = {
      /**
       * id
       *
       * 药品id
       */
      id: string;
      /**
       * groupName
       *
       * 药品名称
       */
      groupName: string;
      /**
       * affiliation
       *
       * affiliation
       */
      affiliation: number;
    };
    /**
     * DictionaryItem
     *
     * 字典项
     */
    type DictionaryItem = Api.Common.CommonRecord<{
      /**
       * dictId
       *
       * 字典id
       */
      dictId: string;
      /**
       * itemSort
       *
       * 排序
       */
      itemSort: number;
      /**
       * itemText
       *
       * 字典项名称
       */
      itemText: string;
      /**
       * itemValue
       *
       * 字典项值
       */
      itemValue: string;
      /**
       * parentId
       *
       * 父id
       */
      parentId: string;
      /**
       * parentName
       *
       * 父名称
       */
      parentName: null;
      /**
       * remark
       *
       * 备注
       */
      remark: string;

      hasChild?: null | string;
    }>;
  }

  namespace Category {
    type ItemRecord = {
      id: string;
      catName: string;
      parentId: string;
      hasChild: number;
      hasItem: number;
    };
  }

  namespace CheckItem {
    type ItemRecord = {
      affiliation?: number;
      createBy?: null;
      createTime?: string;
      id?: string;
      itemAlias?: string;
      itemCode?: string;
      itemName?: string;
      itemShort?: null;
      value?: string;
      itemType?: string;
      updateBy?: string;
      updateTime?: string;
    };
  }
  namespace InspectionItem {
    type InspectionRecord = {
      id: string;
      examineItem: string;
      examineAlias: string;
      partName: string;
    };
  }
  namespace OperationItem {
    type OperationRecord = {
      id: number;
      operationId: string;
      name: string;
      parentId: string;
      hasChild: number;
      hasItem: number;
    };
  }
}
