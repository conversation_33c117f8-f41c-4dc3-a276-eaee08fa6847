import 'vue-router';
import type { RouteRecordRaw } from 'vue-router';

// 扩展Vue Router的RouteMeta接口
declare module 'vue-router' {
  interface RouteMeta {
    /** 菜单标题 */
    title?: string;
    /** 菜单图标 */
    icon?: string;
    /** 是否固定 */
    affix?: boolean;
    /** 是否在菜单中隐藏 */
    hideInMenu?: boolean;
    /** 路由是否固定，不受权限控制 */
    constant?: boolean;
    /** 菜单排序号 */
    order?: number;
    /** 当前路由需要选中的菜单项 */
    activeMenu?: string | null;
    /** 是否隐藏该路由在面包屑上面的显示 */
    hideBreadcrumb?: boolean;
    /** 是否需要缓存 */
    keepAlive?: boolean;
    /** 是否显示在菜单中 */
    menu?: boolean;
    /** 类型 */
    type?: string;
    /** 是否完全隐藏 */
    hidden?: boolean;
    /** 国际化键名 */
    i18nKey?: string;
  }
}

// 应用命名空间
declare namespace App {
  // 国际化命名空间
  namespace I18n {
    type I18nKey = string;
  }

  // 路由命名空间
  namespace Route {
    // 路由名称
    type RouteKey = string;

    // 路由路径映射
    interface RouteMap {
      [key: string]: string;
    }

    // 路由路径
    type RoutePath = string;

    // 最后一级路由
    type LastLevelRouteKey = string;

    // 自定义路由类型
    interface CustomRoute {
      name: string;
      path: string;
      component?: any;
      redirect?: string | Record<string, any> | ((to: any) => any);
      meta?: RouteRecordRaw['meta'];
      children?: CustomRoute[];
      props?: boolean | Record<string, any> | ((to: any) => Record<string, any>);
      alias?: string | string[];
    }
  }
}

// 兼容原有的类型导入
declare module '@elegant-router/types' {
  export type RouteKey = App.Route.RouteKey;
  export type RouteMap = App.Route.RouteMap;
  export type RoutePath = App.Route.RoutePath;
  export type LastLevelRouteKey = App.Route.LastLevelRouteKey;
  export type CustomRoute = App.Route.CustomRoute;
  export type ElegantConstRoute = App.Route.CustomRoute;
}
