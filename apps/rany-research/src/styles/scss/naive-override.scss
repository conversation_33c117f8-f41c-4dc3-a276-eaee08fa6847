/* 覆盖naive-ui组件的active状态阴影 */

/* 全局覆盖 */
:root {
  --n-box-shadow-active: none !important;
}

/* 针对具体组件的覆盖，确保优先级 */
.n-button:active,
.n-button--active,
.n-input:active,
.n-input--active,
.n-select:active,
.n-select--active,
.n-date-picker:active,
.n-date-picker--active,
.n-time-picker:active,
.n-time-picker--active,
.n-input-number:active,
.n-input-number--active {
  box-shadow: none !important;
}

/* 通用类覆盖 */
.n-base-selection:active,
.n-base-selection--active,
.n-base-input:active,
.n-base-input--active {
  box-shadow: none !important;
}
