# N-Data-Table 样式验证指南

## 如何验证样式是否生效

### 1. 访问测试页面

在浏览器中访问：`http://localhost:9527/test-data-table`

如果路由未配置，您可以：

1. 临时修改现有页面来测试样式
2. 或者直接在现有的表格页面中添加样式类进行测试

### 2. 检查样式效果

#### data-table-compact 类
- **预期效果**：表格单元格的 padding 应该变小（4px）
- **检查方法**：对比带有和不带有此类的表格，单元格内容应该更紧密

#### data-table-sm 类
- **预期效果**：紧凑的 padding（4px）
- **检查方法**：与普通表格对比，行高应该更小

#### data-table-xs 类
- **预期效果**：小字体（12px）+ 紧凑 padding
- **检查方法**：字体明显变小，行高也变小

#### data-table-striped 类
- **预期效果**：奇偶行有不同的背景色
- **检查方法**：表格行应该呈现斑马纹效果

#### data-table-bordered 类
- **预期效果**：单元格右侧有边框
- **检查方法**：每个单元格右侧应该有竖直分割线

### 3. 浏览器开发者工具检查

打开浏览器开发者工具，检查以下内容：

1. **样式文件是否加载**：
   - 在 Network 面板中查看是否有 `global.scss` 相关的请求
   - 确认没有 404 或编译错误

2. **CSS 规则是否应用**：
   - 选择表格元素
   - 在 Styles 面板中查看是否有我们的自定义样式
   - 确认 CSS 变量是否正确设置

3. **优先级检查**：
   - 确认我们的样式没有被其他样式覆盖
   - 查看是否有 `!important` 规则生效

### 4. 常见问题排查

#### 样式未生效的可能原因：

1. **样式文件未正确导入**
   - 检查 `src/plugins/assets.ts` 是否包含 SCSS 导入
   - 确认 `src/styles/scss/global.scss` 包含了 n-data-table 导入

2. **选择器优先级不足**
   - 我们的样式使用了 `.n-data-table.data-table-xxx` 的高优先级选择器
   - 如果仍未生效，可能需要增加 `!important`

3. **Naive UI 版本兼容性**
   - 检查 DOM 结构是否与我们的选择器匹配
   - 可能需要根据实际的 DOM 结构调整选择器

4. **缓存问题**
   - 清除浏览器缓存
   - 重启开发服务器

### 5. 快速测试方法

在任意现有的表格页面中，临时添加样式类来测试：

```vue
<!-- 原来的表格 -->
<NDataTable :columns="columns" :data="data" size="small" />

<!-- 添加样式类测试 -->
<NDataTable
  class="data-table-sm"
  :columns="columns"
  :data="data"
  size="small"
/>
```

刷新页面，观察两个表格的差异。

### 6. 验证成功的标志

✅ **样式生效的标志**：
- `data-table-compact/sm` 类：行高明显变小
- `data-table-xs` 类：字体变小且行高变小
- `data-table-striped` 类：奇偶行颜色不同
- `data-table-bordered` 类：列之间有竖直分割线
- `table-action-buttons` 类：操作按钮排列整齐，间距一致

❌ **样式未生效的标志**：
- 表格外观没有任何变化
- 开发者工具中看不到我们的 CSS 规则
- Console 中有 SCSS 编译错误

### 7. 调试建议

如果样式仍未生效，请按以下步骤调试：

1. 打开开发者工具 Console，查看是否有错误信息
2. 在 Network 面板中确认样式文件是否成功加载
3. 手动在 Console 中执行：
   ```javascript
   document.querySelector('.n-data-table').classList.add('data-table-sm');
   ```
   如果手动添加类有效果，说明样式本身没问题，可能是导入问题。

4. 检查 Vite 配置和 package.json 中的 sass 依赖版本
