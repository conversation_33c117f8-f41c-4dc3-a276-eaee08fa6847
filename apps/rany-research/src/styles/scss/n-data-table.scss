/* N-Data-Table 统一样式管理 */
.data-common-table {
  /* 移除表格外边框 */
  .n-data-table.n-data-table--bordered .n-data-table-wrapper {
    border: none !important;
  }

  .n-data-table .n-data-table-td {
    border-bottom: 2px solid #fff !important;
    background: rgba(240, 248, 255, 0.15);
  }
  .n-data-table .n-data-table-th {
    border-bottom: 2px solid #fff !important;
    background: #f0f0f0;
    //第一个的border和最后一个的border四个角的radius都是6px
    &:first-child {
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }
    &:last-child {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }
  }
  .n-data-table .n-data-table-tr {
    margin: 4px 0 !important;
  }

  /* 基础表格样式类 */
  .n-data-table.data-table-small {
    font-size: 12px !important;
  }

  .n-data-table.data-table-compact {
    .n-data-table-th,
    .n-data-table-td {
      --n-th-padding: 4px !important;
      --n-td-padding: 4px !important;
    }
  }

  .n-data-table.data-table-striped {
    .n-data-table-tr:nth-child(even) .n-data-table-td {
      background-color: rgba(0, 0, 0, 0.02);
    }

    html.dark & {
      .n-data-table-tr:nth-child(even) .n-data-table-td {
        background-color: rgba(255, 255, 255, 0.02);
      }
    }
  }

  .n-data-table.data-table-bordered {
    .n-data-table-th,
    .n-data-table-td {
      border-right: 1px solid var(--n-border-color);
    }
  }

  .n-data-table.data-table-borderless {
    .n-data-table-th,
    .n-data-table-td {
      border: none !important;
    }
  }

  /* 快捷组合类 */
  .n-data-table.data-table-xs {
    font-size: 12px !important;

    .n-data-table-th,
    .n-data-table-td {
      --n-th-padding: 4px !important;
      --n-td-padding: 4px !important;
    }
  }

  .n-data-table.data-table-sm {
    .n-data-table-th,
    .n-data-table-td {
      --n-th-padding: 4px !important;
      --n-td-padding: 4px !important;
    }
  }

  .n-data-table.data-table-lg {
    .n-data-table-th,
    .n-data-table-td {
      --n-th-padding: 12px 16px !important;
      --n-td-padding: 12px 16px !important;
    }
  }

  /* 表格工具栏样式 */
  .data-table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 4px;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  /* 表格操作按钮样式 */
  .table-action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
    align-items: center;

    .n-button {
      padding: 0 8px !important;
      min-width: auto !important;
    }
  }

  /* 表格状态指示器 */
  .table-status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;

    &.status-active {
      background-color: #52c41a;
    }

    &.status-inactive {
      background-color: #ff4d4f;
    }

    &.status-pending {
      background-color: #faad14;
    }
  }

  /* 响应式样式 */
  @media (max-width: 768px) {
    .n-data-table {
      font-size: 14px !important;

      .n-data-table-th,
      .n-data-table-td {
        --n-th-padding: 8px 4px !important;
        --n-td-padding: 8px 4px !important;
      }
    }

    .data-table-toolbar {
      flex-direction: column;
      gap: 8px;
      align-items: stretch;

      .toolbar-left,
      .toolbar-right {
        justify-content: center;
      }
    }
  }

  /* 深色主题样式 */
  html.dark {
    .n-data-table {
      .n-data-table-th {
        background-color: var(--n-color-modal);
      }

      .n-data-table-tr:hover .n-data-table-td {
        background-color: rgba(255, 255, 255, 0.05);
      }
    }
  }

  // 分页
  .n-data-table .n-data-table__pagination {
    justify-content: center;
  }
  .n-pagination .n-pagination-item {
    --n-item-size: 20px;
  }
  .n-pagination .n-pagination-item.n-pagination-item--disabled.n-pagination-item--button,
  .n-pagination .n-pagination-item.n-pagination-item--button {
    border: none;
    background-color: transparent;
  }
  .n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--active {
    border-radius: 3px;
    background: #4198ff;
    color: #fff;
    border: none;
  }
  .n-pagination .n-pagination-item:not(.n-pagination-item-disabled).n-pagination-item--active:hover {
    background-color: #4198ff;
    color: #fff;
  }
  .n-pagination .n-pagination-item:not(.n-pagination-item--disabled):hover.n-pagination-item--button {
    border: none;
    color: #4198ff;
  }
  .n-pagination .n-base-selection {
    min-height: 22px;
    .n-base-selection-label {
      --n-height: 22px;
      .n-base-selection-input {
        line-height: 22px;
      }
    }
  }
}
