# N-Data-Table 样式使用指南

## 概述

本项目为 `n-data-table` 组件提供了统一的样式管理，避免在各个组件中重复定义相同的样式代码。

## 可用的样式类

### 基础样式类

- `data-table-small`: 小字体表格 (12px)
- `data-table-compact`: 紧凑模式表格 (减小 padding)
- `data-table-striped`: 斑马纹表格
- `data-table-bordered`: 带边框表格
- `data-table-borderless`: 无边框表格

### 快捷组合类

- `data-table-xs`: 超小表格 (小字体 + 紧凑模式)
- `data-table-sm`: 小表格 (紧凑模式)
- `data-table-lg`: 大表格 (增大 padding)

### 工具类

- `data-table-toolbar`: 表格工具栏容器
- `table-action-buttons`: 表格操作按钮容器
- `table-status-indicator`: 状态指示器

## 使用示例

### 基础用法

```vue
<template>
  <!-- 基础表格 -->
  <NDataTable
    :columns="columns"
    :data="data"
    size="small"
  />

  <!-- 紧凑模式表格 -->
  <NDataTable
    class="data-table-compact"
    :columns="columns"
    :data="data"
    size="small"
  />

  <!-- 小字体紧凑表格 -->
  <NDataTable
    class="data-table-sm"
    :columns="columns"
    :data="data"
    size="small"
  />
</template>
```

### 带工具栏的表格

```vue
<template>
  <div>
    <!-- 表格工具栏 -->
    <div class="data-table-toolbar">
      <div class="toolbar-left">
        <NButton type="primary">新增</NButton>
        <NButton>批量删除</NButton>
      </div>
      <div class="toolbar-right">
        <NButton circle>
          <template #icon>
            <RefreshIcon />
          </template>
        </NButton>
      </div>
    </div>

    <!-- 表格 -->
    <NDataTable
      class="data-table-striped"
      :columns="columns"
      :data="data"
      size="small"
    />
  </div>
</template>
```

### 操作按钮列

```vue
<script>
const columns = [
  // ... 其他列
  {
    title: '操作',
    key: 'actions',
    render: (row) => (
      <div class="table-action-buttons">
        <NButton size="small" type="primary">编辑</NButton>
        <NButton size="small" type="error">删除</NButton>
      </div>
    )
  }
]
</script>
```

### 状态指示器

```vue
<script>
const columns = [
  {
    title: '状态',
    key: 'status',
    render: (row) => (
      <span>
        <span class={`table-status-indicator status-${row.status}`}></span>
        {row.statusText}
      </span>
    )
  }
]
</script>
```

## 自定义主题变量

如果需要自定义表格样式，可以通过 CSS 变量进行调整：

```css
:root {
  --n-th-color: #fafafa;
  --n-th-color-hover: #f0f0f0;
  --n-th-color-dark: #2d2d2d;
  --n-th-color-hover-dark: #3d3d3d;
  --n-border-color: #d9d9d9;
  --n-table-color-striped: #f9f9f9;
  --n-text-color-disabled: #c0c4cc;
  --n-color-target: #e6f7ff;
}
```

## 响应式支持

样式文件包含了响应式设计，在移动设备上会自动调整：

- 字体大小增大到 14px
- Padding 调整为 8px 4px
- 工具栏在小屏幕上变为垂直布局

## 迁移指南

### 从自定义样式迁移

如果你之前使用了自定义的表格样式：

```vue
<!-- 之前的写法 -->
<NDataTable class="custom-table" />

<style>
.custom-table {
  font-size: 12px !important;
}
.custom-table :deep(.n-data-table-th) {
  --n-th-padding: 4px;
  --n-td-padding: 4px;
}
</style>
```

改为：

```vue
<!-- 新的写法 -->
<NDataTable class="data-table-sm" />

<!-- 移除自定义样式，或者只保留组件特有的样式 -->
<style>
/* 只保留组件特有的样式 */
</style>
```

## 注意事项

1. 样式类可以组合使用，如：`class="data-table-compact data-table-striped"`
2. 避免在组件中重复定义已有的表格样式
3. 如有特殊需求，建议先检查是否可以通过现有样式类组合实现
4. 新增通用样式时，请更新此文档

## 样式文件位置

- 主样式文件：`src/styles/scss/n-data-table.scss`
- 导入位置：`src/styles/scss/global.scss`
- 文档位置：`src/styles/scss/README.md`
