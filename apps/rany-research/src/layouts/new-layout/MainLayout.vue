<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useRoute, useRouter } from 'vue-router';
import { ElLoading } from 'element-plus';
import type { LoadingInstance } from 'element-plus/es/components/loading/src/loading';
import { EventNames, Layout, TabsBar, useLayoutStore, useNavigationStore, useTabsStore } from 'component-library';
import { AiChatSideView } from '@rany/component-ai';
import emitter from '@/utils/mitt';

const router = useRouter();
const layoutStore = useLayoutStore();
const navigationStore = useNavigationStore();
const route = useRoute();

const showExpandIcon = computed(() => {
  return router.currentRoute.value.path === '/home/<USER>';
});

const handleMenu = () => {
  if (router.currentRoute.value.path === '/home/<USER>/platform') return;
  // include /aiChat/QA
  if (router.currentRoute.value.path === '/home/<USER>' || router.currentRoute.value.path.includes('/aiChat/QA')) {
    // 打开抽屉
    emitter.emit('open-ai-drawer');
  } else {
    layoutStore.toggleNavType();
  }
};

const handleMenuRouteChange = (data: string) => {
  router.push(data);
};
const layout = ref();
const handleTabClose = (nav: any, isEmpty: boolean) => {
  if (isEmpty) {
    router.push('/ai/home');
  } else {
    router.push(nav);
  }
};

const handleTabCloseAll = () => {
  router.push('/home/<USER>');
};

const tabsBar = ref<InstanceType<typeof TabsBar>>();
emitter.on(EventNames.TAB_ADD, (tab: any) => {
  // 确保添加的tab有type属性
  if (!tab.type) {
    // 如果没有type，默认设置为platform类型
    tab.type = 'rany-research';

    // 也可以尝试从路由meta获取
    if (tab.path) {
      const matchedRoute = router
        .getRoutes()
        .find(
          routeItem =>
            routeItem.path === tab.path || (tab.path.startsWith('/') && routeItem.path === tab.path.substring(1))
        );
      if (matchedRoute && matchedRoute.meta && matchedRoute.meta.type) {
        tab.type = matchedRoute.meta.type;
      }
    }
  }

  tabsBar.value?.addTab(tab);
});

const handleNavChange = (nav: any) => {
  if (nav === '/platform') {
    // 页面跳转到平台首页
    router.push('/home/<USER>/platform');
  } else if (nav === '/message') {
    // 给tabsBar添加一个tab

    tabsBar.value?.addTab({
      id: 'message',
      title: '消息',
      label: '消息',
      path: '/message',
      closable: true,
      cache: true,
      key: 'message',
      type: 'rany-research'
    });
    router.push('/message');
  } else if (nav === '/doctor') {
    if (
      !route.path.includes('/aiChat/QA') &&
      route.path !== '/home/<USER>' &&
      route.path !== '/home/<USER>/platform' &&
      route.path !== '/ai/home'
    ) {
      layout.value.toggleRightPanel();
    }
  } else {
    router.push(nav);
  }
};

const handleAction = () => {
  if (
    !route.path.includes('/aiChat/QA') &&
    route.path !== '/home/<USER>' &&
    route.path !== '/home/<USER>/platform' &&
    route.path !== '/ai/home'
  ) {
    layoutStore.toggleRightPanel();
  }
};

const { showRightPanel, navType } = storeToRefs(layoutStore);

// 内容区域引用
const contentAreaRef = ref<HTMLElement | null>(null);
// 加载实例
let loadingInstance: LoadingInstance | null = null;

// 计算属性：判断是否为AI聊天页面
const isAiChatQA = computed(() => route.path.includes('/aiChat'));

// 显示加载遮罩
const showLoading = () => {
  if (contentAreaRef.value) {
    // 仅当内容区域存在且当前没有活跃的加载实例时创建新实例
    if (!loadingInstance) {
      loadingInstance = ElLoading.service({
        target: contentAreaRef.value,
        lock: true,
        text: '加载中...',
        background: 'rgba(255, 255, 255, 0.7)'
      });
    }
  }
};

// 隐藏加载遮罩
const hideLoading = () => {
  if (loadingInstance) {
    loadingInstance.close();
    loadingInstance = null;
  }
};

// 添加路由全局钩子
router.beforeEach((to, from, next) => {
  // 只在非首次加载、非相同路径、需要显示loading的页面上显示加载
  if (from.name && to.path !== from.path && to.path !== '/home-new' && !to.meta.noLoading && to.name !== from.name) {
    showLoading();
  }
  next();
});

router.afterEach(to => {
  // 路由加载完成后隐藏加载指示器
  // 使用动态延迟，基于页面复杂度
  const hideDelay = to.meta.heavyPage ? 500 : 250;

  if (to.meta.heavyPage) {
    setTimeout(() => {
      // 如果是重型页面，先将加载文本更改为"正在渲染页面..."
      if (loadingInstance) {
        loadingInstance.setText('正在渲染页面...');
      }
    }, 200);
  }

  setTimeout(() => {
    hideLoading();
  }, hideDelay);
});

// 监听全局加载事件
onMounted(() => {
  // 监听开始加载事件
  if (emitter) {
    emitter.on('content-loading:start', (payload: any) => {
      // 允许自定义加载文本
      if (contentAreaRef.value) {
        if (loadingInstance) hideLoading();
        loadingInstance = ElLoading.service({
          target: contentAreaRef.value,
          lock: true,
          text: payload?.text || '加载中...',
          background: 'rgba(255, 255, 255, 0.7)'
        });
      }
    });

    // 监听结束加载事件
    emitter.on('content-loading:end', () => {
      hideLoading();
    });
  }
});

// 确保组件卸载时清理资源和事件监听
onBeforeUnmount(() => {
  hideLoading();
  if (emitter) {
    emitter.off('content-loading:start');
    emitter.off('content-loading:end');
  }
});

// 监听路由变化，同步标签页
watch(
  () => route.path,
  newPath => {
    try {
      // 如果newPath是/home-new/ai-home/QA/:id?，则右侧收起来
      if (
        route.path.includes('/aiChat/QA') ||
        route.path === '/home/<USER>' ||
        route.path === '/home/<USER>/platform' ||
        route.path === '/ai/home'
      ) {
        if (route.path === '/home/<USER>') {
          layoutStore.setActiveMenu('menu-home');
        } else if (route.path === '/home/<USER>/platform') {
          layoutStore.setActiveMenu('menu-platform');
        }
        layoutStore.toggleRightPanel(false);
      }
      // 如果是AI聊天页面或/home-new页面，不需要添加标签
      if (isAiChatQA.value || newPath === '/home-new') return;

      const menuItem = navigationStore.findMenuItemByPath(newPath);
      if (menuItem && menuItem.path) {
        // 检查是否已经存在相同路径的标签页，避免重复添加
        const existingTab = navigationStore.tabs?.find(tab => tab.path === menuItem.path);
        if (existingTab) {
          navigationStore.setActiveTab(existingTab.id);
          return;
        }
        useTabsStore().addTab({
          id: menuItem.id,
          title: menuItem.title,
          label: menuItem.title,
          path: menuItem.path,
          closable: true,
          cache: true,
          key: menuItem.id,
          type: 'rany-research'
        });
      }
    } catch (error) {
      console.error('路由变化处理错误:', error);
      // 允许用户继续使用应用，即使出现错误
    }
  },
  { immediate: true }
);
</script>

<template>
  <Layout
    ref="layout"
    :current-path="route.fullPath"
    home-path="/home/<USER>"
    :show-expand-icon="showExpandIcon"
    platform="RanyResearch"
    @nav-change="handleNavChange"
    @menu-route-change="handleMenuRouteChange"
    @handle-menu="handleMenu"
    @handle-action="handleAction"
  >
    <TabsBar
      v-if="route.name !== 'AiChatHome' && route.name !== 'ai-home' && route.name !== 'AccountSettingIndex'"
      ref="tabsBar"
      :active="route.fullPath"
      type="rany-research"
      @tab-click="handleNavChange"
      @tab-close="handleTabClose"
      @tab-close-all="handleTabCloseAll"
    />
    <template #rightPanel>
      <AiChatSideView @close="layout.toggleRightPanel()" />
    </template>

    <RouterView :key="route.fullPath" v-slot="{ Component, route }">
      <KeepAlive :include="route.meta.keepAlive ? Component.name : ''">
        <component :is="Component" />
      </KeepAlive>
    </RouterView>
  </Layout>
</template>

<style scoped>
.main-layout {
  --sidebar-collapsed-width: 13.75rem;
  --right-panel-width: 20rem;
  --transition-timing: cubic-bezier(0.645, 0.045, 0.355, 1);
  --content-timing: cubic-bezier(0.645, 0.045, 0.355, 1);
}

.navigation-container {
  @apply relative h-full;
  min-width: var(--sidebar-collapsed-width);
  transition: min-width 0.4s var(--transition-timing);
}

.layout-content {
  @apply flex-1 flex flex-col min-w-0 py-[0.5rem];
  transition:
    margin-left 0.4s var(--content-timing),
    padding 0.4s var(--content-timing);
}

/* 导航展开状态 */
.nav-expanded-enter-active ~ .layout-content {
  margin-left: var(--sidebar-width);
}

/* 导航收起状态 */
.nav-expanded-leave-active ~ .layout-content {
  margin-left: var(--sidebar-collapsed-width);
}

/* 内容区域容器 */
.content-container {
  @apply flex-1 bg-white rounded-[1vh] overflow-hidden flex flex-col;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  transition:
    transform 0.4s var(--content-timing),
    box-shadow 0.4s var(--content-timing);
  transform-origin: left center;
  will-change: transform;
}

/* 导航展开时的内容区域动画 */
.nav-expanded-enter-active ~ .layout-content .content-container {
  transform: translateX(2px);
}

/* 导航收起时的内容区域动画 */
.nav-expanded-leave-active ~ .layout-content .content-container {
  transform: translateX(0);
}

/* 导航组件动画 */
.nav-expanded-enter-active,
.nav-expanded-leave-active,
.nav-collapsed-enter-active,
.nav-collapsed-leave-active {
  transition: all 0.4s var(--transition-timing);
  transform-origin: left;
  will-change: transform, opacity;
}

.nav-expanded-enter-from,
.nav-expanded-leave-to {
  opacity: 0;
  transform: translateX(-4px);
}

.nav-collapsed-enter-from,
.nav-collapsed-leave-to {
  opacity: 0;
  transform: translateX(-4px);
}

.bg-gradient {
  background: radial-gradient(127.33% 251.81% at 0% 7.43%, #739ff2 0%, #f3f5fa 100%);
}

.layout-right-panel {
  @apply my-[1vh] flex-shrink-0 overflow-hidden h-[calc(100vh-2vh)] fixed right-[1vh];
  width: var(--right-panel-width);
  transition: transform 0.3s var(--transition-timing);
  will-change: transform;
  background: white;
  border-radius: 0.5rem;
}

.platform-panel {
  @apply h-[calc(100vh-2vh)] my-[0.5rem] mx-[0.5rem] py-[0.25rem] px-[0.25rem] w-[12.25rem];
  background-color: #f6f6f6;
  border: 1px solid rgba(229, 231, 235, 0.5);
  transition: all 0.3s var(--transition-timing);
}

.nav-hover-effect {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.nav-hover-effect:hover {
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.ai-chat-btn {
  @apply flex items-center rounded-[0.25rem] px-[0.75rem] py-[0.4rem] text-[0.875rem];
  transition: all 0.3s var(--transition-timing);
}

.ai-chat-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ai-chat-btn:active {
  transform: translateY(0);
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(8px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-8px);
}

.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.slide-right-enter-from,
.slide-right-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

.right-panel-container {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s var(--transition-timing);
}

.right-panel-container:hover {
  box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
}
</style>
