import axios, { type AxiosRequestConfig, type Method } from 'axios';
import { router } from '@/router';
import { useUserStore } from '@/stores/user';
import type { RequestOptions } from '@/types/api';
import { getServiceBaseURL } from '@/utils/service';

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';

const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

const service = axios.create({
  baseURL: otherBaseURL.portal_api,
  timeout: 60000
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 如果请求的不是登录接口，则添加 token
    if (config.url !== '/login') {
      const user = useUserStore();
      console.log(user)
      if (user.token) {
        config.headers.Authentication = `${user.token}`;
      }else{
        const loginUrl = import.meta.env.VITE_AUTH_LOGIN_URL ;
        // const currentPath = encodeURIComponent(window.location.pathname + window.location.search);

        // 如果当前已经在登录页，则不需要重定向
        if (window.location.href.includes('/login')) {
          return;
        }

        setTimeout(() => {
          window.location.href = `${loginUrl}?redirect=/ly/web/`;
        }, 400);
        return;
      }
    }
    return config;
  },
  err => Promise.reject(err)
);

// 响应拦截器
service.interceptors.response.use(
  res => {
    // 响应统一处理
    res.data.success = res.data.code === 1;

    // 将分页数据转换为数字类型
    if (res.data && res.data.data) {
      if (res.data.data.pageNo) {
        res.data.data.pageNo = Number(res.data.data.pageNo);
      }
      if (res.data.data.pageSize) {
        res.data.data.pageSize = Number(res.data.data.pageSize);
      }
      if (res.data.data.total) {
        res.data.data.total = Number(res.data.data.total);
      }
    }

    return res.data;
  },
  err => {
    // 设置一个变量
    let tokenAbnormal = false;
    const user = useUserStore();
    if (err.response) {
      switch (err.response.status) {
        case 401:
          if (!tokenAbnormal) {
            tokenAbnormal = true;
            setTimeout(() => {
              tokenAbnormal = false;
            }, 30000);
          }
          // 退出登录
          user.$reset();
          router.push('/login');
          break;
        case 403:
          // 处理权限不足的情况
          console.error('权限不足');
          break;
        case 404:
          // 处理资源不存在的情况
          console.error('资源不存在');
          break;
        case 500:
          // 处理服务器内部错误的情况
          console.error('服务器内部错误');
          break;
        default:
          // 处理其他错误状态
          console.error(`请求失败，状态码：${err.response.status}`);
      }
    } else if (err.request) {
      // 请求已发送但没有收到响应
      console.error('请求已发送但没有收到响应');
    } else {
      // 其他错误
      console.error('请求配置错误');
    }
    return err.response?.data || Promise.reject(err);
  }
);

export const request = <RequestT = any, ResponseT = any>(
  url: string,
  method: Method = 'GET',
  submitData?: RequestT,
  options?: RequestOptions
) => {
  const config: AxiosRequestConfig = {
    url,
    method,
    timeout: options?.timeout
  };

  if (method.toUpperCase() === 'GET') {
    config.params = submitData as object;
  } else if (submitData instanceof File || submitData instanceof Blob) {
    const formData = new FormData();
    formData.append('file', submitData as File | Blob);
    config.data = formData;
    config.headers = {
      ...config.headers,
      'Content-Type': 'multipart/form-data'
    };
  } else {
    const user = useUserStore();
    config.data = {
      ...(submitData as object),
      userId: user.id,
      userName: user.userName
    };
  }

  return service.request<RequestT, ResponseT>(config);
};

export default service;
