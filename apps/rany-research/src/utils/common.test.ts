import { describe } from 'node:test';
import { expect, test } from 'vitest';
import { extractBirthDateFromIDCard } from './common';
describe('ExtractBirthDateFromIDCard', () => {
  test('extractBirthDateFromIDCard should return null when input is an empty string', () => {
    const idCard = '';
    const result = extractBirthDateFromIDCard(idCard);
    expect(result).toBeNull();
  });

  test('extractBirthDateFromIDCard should return valid birth date for 18-digit ID card', () => {
    const idCard = '123456199001010011';
    const result = extractBirthDateFromIDCard(idCard);
    expect(result).toBe('1990-01-01');
  });

  test('extractBirthDateFromIDCard should return valid birth date for 15-digit ID card', () => {
    const idCard = '123456900101001';
    const result = extractBirthDateFromIDCard(idCard);
    expect(result).toBe('1990-01-01');
  });

  test('extractBirthDateFromIDCard should return null for invalid ID card', () => {
    const idCard = '123456199001320011'; // Invalid date
    const result = extractBirthDateFromIDCard(idCard);
    expect(result).toBeNull();
  });
});
