<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useDiseaseStore } from '@/store/modules/database/disease';

const value = defineModel<string | number | undefined | null>('value', { required: true });
const props = defineProps<{
  multiple?: boolean;
}>();

const diseaseStore = useDiseaseStore();
const diseaseOptions = ref<Api.Common.CascaderOption[]>([]);
const loading = ref(false);

async function getDiseaseTree() {
  if (diseaseOptions.value.length > 0) return;

  try {
    loading.value = true;
    await diseaseStore.getDiseaseTree();
    diseaseOptions.value = diseaseStore.diseaseTree;
  } catch (error) {
    console.error('获取疾病树失败:', error);
  } finally {
    loading.value = false;
  }
}

// 组件挂载时预加载数据
onMounted(() => {
  getDiseaseTree();
});
</script>

<template>
  <NCascader
    v-model:value="value"
    :multiple="props.multiple"
    :options="diseaseOptions"
    :loading="loading"
    size="small"
    check-strategy="child"
    filterable
    clearable
    placeholder="请选择疾病"
  />
</template>

<style scoped></style>
