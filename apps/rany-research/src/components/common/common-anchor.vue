<script setup lang="ts">
import { NAnchor } from 'naive-ui';

interface AnchorItem {
  title: string;
  href: string;
  children?: AnchorItem[];
}

const props = defineProps<{
  anchorItems: AnchorItem[];
}>();
</script>

<template>
  <div class="relative w-200px container">
    <NAnchor ignore-gap class="fixed z-1 w-200px overflow-clip" :bound="100">
      <NAnchorLink v-for="item in props.anchorItems" :key="item.href" :title="item.title" :href="item.href">
        <NAnchorLink v-for="child in item.children" :key="child.href" :title="child.title" :href="child.href" />
      </NAnchorLink>
    </NAnchor>
  </div>
</template>
