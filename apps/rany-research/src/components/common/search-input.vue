<script setup lang="ts">
import { ref, watch } from 'vue';

defineProps({
  placeholder: {
    type: String,
    default: '请输入搜索内容'
  }
});

const emit = defineEmits(['search']);

const searchValue = ref('');

// 使用VueUse的防抖处理搜索
const debouncedSearch = useDebounceFn((value: string) => {
  emit('search', value);
}, 300);

// 监听搜索值变化，自动触发搜索
watch(searchValue, newValue => {
  debouncedSearch(newValue);
});

const handleSearch = () => {
  emit('search', searchValue.value);
};
</script>

<template>
  <NInput v-model:value="searchValue" size="small" :placeholder="placeholder" clearable @keyup.enter="handleSearch">
    <template #suffix>
      <NIcon class="cursor-pointer" :size="16" @click="handleSearch">
        <icon-ic-round-search />
      </NIcon>
    </template>
  </NInput>
</template>

<style scoped></style>
