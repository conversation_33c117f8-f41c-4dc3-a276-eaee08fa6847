<script setup lang="tsx">
import { ref, watch } from 'vue';
import { NImage } from 'naive-ui';
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
import VueOfficePptx from '@vue-office/pptx';
import { VuePdf } from 'vue3-pdfjs';
import '@vue-office/docx/lib/index.css';
import '@vue-office/excel/lib/index.css';

const props = defineProps({
  filePath: {
    type: String,
    required: true
  },
  fileExt: {
    type: String,
    required: true
  }
});

const show = defineModel('show', {
  type: Boolean,
  default: false
});

const dialogTitle = ref('');
const dialogStyle = ref({});
const loading = ref(true);

function getDialogConfig() {
  // 图片文件
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
  if (imageExts.includes(props.fileExt)) {
    dialogTitle.value = '查看图片';
    dialogStyle.value = { width: '800px' };
    return;
  }

  // PDF文件
  if (props.fileExt === 'pdf') {
    dialogTitle.value = '查看PDF';
    dialogStyle.value = { width: '90vw', height: '90vh' };
    return;
  }

  // Word文档
  if (['doc', 'docx'].includes(props.fileExt)) {
    dialogTitle.value = '查看Word文档';
    dialogStyle.value = { width: '90vw', height: '90vh' };
    return;
  }

  // Excel文档
  if (['xls', 'xlsx'].includes(props.fileExt)) {
    dialogTitle.value = '查看Excel文档';
    dialogStyle.value = { width: '90vw', height: '90vh' };
    return;
  }

  // PPT文档
  if (['ppt', 'pptx'].includes(props.fileExt)) {
    dialogTitle.value = '查看PPT文档';
    dialogStyle.value = { width: '90vw', height: '90vh' };
    return;
  }

  // 其他文件直接下载
  const a = document.createElement('a');
  a.href = props.filePath;
  a.target = '_blank';
  a.rel = 'noopener noreferrer';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

watch(show, newVal => {
  if (newVal) {
    loading.value = true;
    getDialogConfig();
  }
});
</script>

<template>
  <NModal
    v-model:show="show"
    :title="dialogTitle"
    :style="dialogStyle"
    preset="dialog"
    @update:show="val => $emit('update:show', val)"
  >
    <template #default>
      <div class="max-h-[calc(90vh-120px)] overflow-auto">
        <!-- 图片文件 -->
        <NImage v-if="['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExt)" :src="filePath" crossorigin="anonymous" />

        <!-- PDF文件 -->
        <div v-else-if="fileExt === 'pdf'" class="pdf-container">
          <div class="pdf-content">
            <VuePdf
              :src="filePath"
              :page="1"
              :scale="1"
              :enable-text-selection="true"
              :enable-annotations="false"
              @loaded="loading = false"
            />
          </div>
        </div>

        <!-- Word文档 -->
        <div v-else-if="['doc', 'docx'].includes(fileExt)" class="docx-container">
          <VueOfficeDocx :src="filePath" :height="800" :width="1000" :show-loading="true" />
        </div>

        <!-- Excel文档 -->
        <div v-else-if="['xls', 'xlsx'].includes(fileExt)" class="excel-container">
          <VueOfficeExcel :src="filePath" style="height: 100vh" />
        </div>

        <!-- PPT文档 -->
        <div v-else-if="['ppt', 'pptx'].includes(fileExt)" class="ppt-container">
          <VueOfficePptx :src="filePath" style="height: 100vh" />
        </div>

        <!-- 其他文件 -->
        <div v-else class="other-container"></div>
      </div>
    </template>
  </NModal>
</template>

<style lang="scss" scoped>
.pdf-container {
  width: 100%;
  height: calc(90vh - 120px);
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  gap: 16px;
}

.pdf-toolbar {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 8px 16px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.pdf-zoom {
  display: flex;
  align-items: center;
}

.pdf-content {
  flex: 1;
  overflow: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;

  :deep(.vue-pdf) {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    background-color: #fff;
  }
}

.pdf-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #666;

  .loading-text {
    text-align: center;
  }
}

.pdf-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #f56c6c;
  text-align: center;
}

.pdf-page {
  background: white;
  padding: 10px;
  border-radius: 4px;
}

.docx-container,
.excel-container,
.ppt-container {
  width: 100%;
  height: calc(90vh - 120px);
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background-color: #fff;
  padding: 20px;
}

:deep(.vue-office-excel),
:deep(.vue-office-pptx),
:deep(.vue-office-docx) {
  width: 100%;
  height: 100%;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
</style>
