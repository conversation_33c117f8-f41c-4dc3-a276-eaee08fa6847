<script setup lang="ts">
import { onMounted } from 'vue';
import { usePlanStore } from '@/store/modules/plan';

const value = defineModel<string | number | undefined | null>('value', { required: true });

const store = usePlanStore();
const { data, load, loading } = store.getPlanOptions();

// 组件挂载时预加载数据
onMounted(() => load());
</script>

<template>
  <NSelect
    v-model:value="value"
    :options="data"
    :loading="loading"
    filterable
    clearable
    placeholder="输入计划名称搜索"
  />
</template>

<style scoped></style>
