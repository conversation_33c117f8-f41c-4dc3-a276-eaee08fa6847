<script setup lang="ts">
import { ref } from 'vue';
import { fetchPostRegisterList } from '@/service/api';

const model = defineModel<string>();
const options = ref<Api.Common.CommonOption[]>([]);
// 获取选项列表
async function getOptions() {
  try {
    const { data } = await fetchPostRegisterList({
      pageNo: 1,
      pageSize: 1000
    });

    options.value =
      data?.records.map(item => ({
        label: item.name || '',
        value: item.id || ''
      })) || [];
  } catch {
    window.$message?.error('获取登记表列表失败');
  }
}
</script>

<template>
  <NSelect v-model:value="model" placeholder="请选择登记首页" :options="options" @focus="getOptions" />
</template>
