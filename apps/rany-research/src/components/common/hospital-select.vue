<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { fetchPostHospitalList } from '@/service/api';

const value = defineModel<string | number | undefined | null>('value', { required: true });

const props = defineProps<{
  hospitalList?: string[];
}>();

const hospitalOptions = ref<Api.Common.CommonOption[]>([]);
const loading = ref(false);

async function getHospitalList() {
  if (hospitalOptions.value.length > 0) return;

  try {
    loading.value = true;
    const { data, error } = await fetchPostHospitalList({
      pageNo: 1,
      pageSize: 1000
    });
    if (!error) {
      hospitalOptions.value = data.records
        .filter(item => item.status === 1)
        .map(item => ({
          label: item.name,
          value: item.id!
        }));
    }
  } catch (error) {
    console.error('获取医院列表失败:', error);
    window.$message?.error('获取医院列表失败');
  } finally {
    loading.value = false;
  }
}

// 组件挂载时预加载数据
onMounted(() => {
  getHospitalList();
});

function focus() {
  if (props.hospitalList && props.hospitalList.length) {
    hospitalOptions.value = hospitalOptions.value.filter(item => props.hospitalList!.includes(item.value));
  }
}
</script>

<template>
  <NSelect
    v-model:value="value"
    :options="hospitalOptions"
    :loading="loading"
    filterable
    clearable
    placeholder="请选择医院"
    @focus="focus"
  />
</template>

<style scoped></style>
