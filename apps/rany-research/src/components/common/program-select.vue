<script setup lang="ts">
import { onMounted } from 'vue';
import { useProgramStore } from '@/store/modules/program';

const value = defineModel<string | number | undefined | null>('value', { required: true });

const store = useProgramStore();
const { data, load, loading } = store.loadProgramOptions();

// 组件挂载时预加载数据
onMounted(() => load());
</script>

<template>
  <NSelect
    v-model:value="value"
    :options="data"
    :loading="loading"
    filterable
    clearable
    placeholder="输入方案名称搜索"
  />
</template>

<style scoped></style>
