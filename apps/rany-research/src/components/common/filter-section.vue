<script setup lang="ts">
import { ref } from 'vue';
import type { SelectOption } from 'naive-ui';
import SearchInput from './search-input.vue';

interface Filter {
  key: string;
  label: string;
  placeholder?: string;
  options?: SelectOption[]; // 使用 Naive UI 的 SelectOption 类型
  [key: string]: any; // 允许添加其他属性
}

defineProps({
  filters: {
    type: Array as () => Filter[],
    default: () => []
  },
  showSearch: {
    type: Boolean,
    default: true
  },
  searchPlaceholder: {
    type: String,
    default: '请输入搜索内容'
  }
});

const emit = defineEmits(['filter-change', 'search']);

const selectedValues = ref<Record<string, any>>({});

// 初始化选中值
// 默认有不选的情况
// props.filters.forEach((filter) => {
//   selectedValues.value[filter.key] = filter.options?.[0]?.value ?? "";
// });

const handleFilterChange = (key: string) => {
  emit('filter-change', {
    key,
    value: selectedValues.value[key]
  });
  // 筛选器变化时直接触发搜索
  emit('search');
};
const updateValue = (key: string, value: any) => {
  selectedValues.value[key] = value;
  handleFilterChange(key);
};

const handleSearch = (value: string) => {
  emit('search', value);
};
</script>

<template>
  <div class="mx-4 mt-2 flex flex-wrap items-center justify-between">
    <div class="flex flex-1 flex-wrap items-center gap-y-3 text-[0.875rem]">
      <template v-for="(filter, index) in filters" :key="index">
        <div class="flex items-center">
          <div class="mr-2 shrink-0 text-left">{{ filter.label }}：</div>
          <div class="filter-section-select w-32 shrink-0">
            <slot
              :name="filter.key"
              :filter="filter"
              :value="selectedValues[filter.key]"
              @update:value="updateValue(filter.key, $event)"
              @change="updateValue(filter.key, $event)"
            >
              <!-- 默认插槽内容：select 实现 -->
              <NSelect
                v-model:value="selectedValues[filter.key]"
                :placeholder="filter.placeholder || '请选择'"
                :options="filter.options"
                clearable
                @update:value="value => updateValue(filter.key, value)"
              />
            </slot>
          </div>
        </div>
      </template>
    </div>
    <div v-if="showSearch" class="ml-auto shrink-0">
      <SearchInput :placeholder="searchPlaceholder" @search="handleSearch" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.filter-section-select {
  // 确保选择框固定高度为28px
  :deep(.n-base-selection) {
    font-size: 0.875rem !important;
    height: 28px !important;
    min-height: 28px !important;
    max-height: 28px !important;
  }

  // 内部元素高度调整
  :deep(.n-base-selection .n-base-selection-label) {
    height: 26px !important;
    line-height: 26px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  :deep(.n-base-selection .n-base-selection__border) {
    border-radius: 4px;
  }

  :deep(.n-base-selection .n-base-suffix) {
    height: 26px !important;
    line-height: 26px !important;
  }

  // 输入框内的文字
  :deep(.n-base-selection-input) {
    height: 26px !important;
    line-height: 26px !important;
  }

  // 占位符样式
  :deep(.n-base-selection-placeholder) {
    height: 26px !important;
    line-height: 26px !important;
  }
}
</style>
