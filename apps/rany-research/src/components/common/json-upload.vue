<script setup lang="ts">
import { NButton, NUpload } from 'naive-ui';
import type { UploadFileInfo } from 'naive-ui';
import { useUserStore } from '@/store/modules/user';

export type OperateType = NaiveUI.TableOperateType;

const serviceBaseUrl = import.meta.env.VITE_SERVICE_BASE_URL;

const userStore = useUserStore();

const value = defineModel<string>('value');

const props = defineProps<{
  operateType: OperateType;
}>();

function handleUploadFinish(options: { file: UploadFileInfo; event?: Event }) {
  const target = options.event?.target as XMLHttpRequest;
  value.value = JSON.parse(target.response).data[0];
}

function handleUploadError() {
  window.$message?.error('上传失败,请重试!');
}

function handleDownload() {
  const link = document.createElement('a');
  link.href = '/src/assets/file/test.json';
  link.download = '示例.json';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}
</script>

<template>
  <NUpload
    :action="serviceBaseUrl + '/common/uploadFile'"
    :headers="{
      type: 'multipart/form-data',
      Authentication: userStore.token
    }"
    :max="1"
    accept=".json"
    @finish="handleUploadFinish"
    @error="handleUploadError"
  >
    <NButton>上传文件</NButton>
    <span v-if="props.operateType === 'edit'" class="ml-2 text-12px text-red-500">替换之前存在的文件</span>
  </NUpload>
  <NButton text text-color="blue" @click="handleDownload">示例下载</NButton>
</template>

<style scoped></style>
