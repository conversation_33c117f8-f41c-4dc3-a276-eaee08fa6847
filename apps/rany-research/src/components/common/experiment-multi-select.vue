<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useExperimentStore } from '@/store/modules/experiment';

const value = defineModel<string[]>('value', { required: true });

const experimentStore = useExperimentStore();
const experimentOptions = ref<Api.Common.CommonOption[]>([]);
const loading = ref(false);

async function getExperimentList() {
  if (experimentOptions.value.length > 0) return;

  try {
    loading.value = true;
    await experimentStore.getExperimentList();
    experimentOptions.value = experimentStore.experimentList;
  } catch (error) {
    console.error('获取试验列表失败:', error);
    window.$message?.error('获取试验列表失败');
  } finally {
    loading.value = false;
  }
}

// 组件挂载时预加载数据
onMounted(() => {
  getExperimentList();
});
</script>

<template>
  <NSelect
    v-model:value="value"
    :options="experimentOptions"
    :loading="loading"
    filterable
    clearable
    multiple
    placeholder="请选择试验"
  />
</template>

<style scoped></style>
