<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useDiseaseStore } from '@/store/modules/database/disease';

const value = defineModel<(string | number)[]>('value', { required: true });

const diseaseStore = useDiseaseStore();
const diseaseOptions = ref<Api.Common.CascaderOption[]>([]);
const loading = ref(false);

async function getDiseaseTree() {
  if (diseaseOptions.value.length > 0) return;

  try {
    loading.value = true;
    await diseaseStore.getDiseaseTree();
    diseaseOptions.value = diseaseStore.diseaseTree;
  } catch (error) {
    console.error('获取疾病树失败:', error);
  } finally {
    loading.value = false;
  }
}

// 组件挂载时预加载数据
onMounted(() => {
  getDiseaseTree();
});
</script>

<template>
  <NCascader
    v-model:value="value"
    :options="diseaseOptions"
    :loading="loading"
    multiple
    filterable
    clearable
    placeholder="请选择疾病"
  />
</template>

<style scoped></style>
