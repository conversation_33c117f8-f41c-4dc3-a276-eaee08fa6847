<template>
  <div class="button-group">
    <div class="button-background" :style="backgroundStyle"></div>
    <button
      class="button-item"
      :class="{ active: activeIndex === 0 }"
      @click="setActive(0)"
    >
      预览
    </button>
    <button
      class="button-item"
      :class="{ active: activeIndex === 1 }"
      @click="setActive(1)"
    >
      详情
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// 定义激活索引，默认为0（预览）
const activeIndex = defineModel('activeIndex', { default: 0 });

// 计算背景样式，根据激活索引动态调整位置
const backgroundStyle = computed(() => ({
  transform: `translateX(${activeIndex.value * 100}%)`,
  width: '49%'
}));

// 设置激活状态的方法
const setActive = (index: number) => {
  activeIndex.value = index;
};
</script>

<style scoped>
.button-group {
  position: relative;
  width: 108px;
  height: 28px;
  flex-shrink: 0;
  border-radius: 6px;
  box-shadow:
    0 0 0 0.5px var(--icon_Blue, #0085FF);
  display: flex;
  overflow: hidden;
}

.button-background {
  position: absolute;
  display: block;
  margin: 1px -1px 1px 1px;
  top: 0px;
  left: 0px;
  height: 26px;
  line-height: 26px;
  border-radius: 6px;
  background: var(--icon_Blue, #0085FF);
  transition: transform 0.3s ease;
  z-index: 1;
}

.button-item {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--icon_Blue, #0085FF);
  text-align: center;
  font-family: "Source Han Sans CN";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  cursor: pointer;
  z-index: 2;
  position: relative;
}

.button-item.active {
  color: white;
}
</style>
