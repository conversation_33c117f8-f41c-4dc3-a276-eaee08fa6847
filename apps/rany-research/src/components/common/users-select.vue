<script setup lang="ts">
import { ref } from 'vue';
import { fetchGetMemberList } from '@/service/api';

const value = defineModel<string | number | undefined | null | string[]>('value', { required: true });

const hospitalCollectionId = defineModel<string>('hospitalCollectionId', { required: true });

const userOptions = ref<Api.Common.CommonOption[]>([]);
const loading = ref(false);

async function getUserList() {
  if (userOptions.value.length > 0) return;

  try {
    loading.value = true;
    const { data, error } = await fetchGetMemberList({
      pageNo: 1,
      pageSize: 1000,
      hospitalCollectionId: hospitalCollectionId.value,
      userName: 'admin',
      userId: 'ed2bcf1d7a584a69bf99bd0df530475a'
    });
    if (!error) {
      userOptions.value = data.records.map(item => ({
        label: item.nickName,
        value: item.userId
      }));
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    window.$message?.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
}

// 组件挂载时预加载数据
// onMounted(() => {
//   getUserList();
// });
</script>

<template>
  <NSelect
    v-model:value="value"
    :options="userOptions"
    :loading="loading"
    filterable
    clearable
    multiple
    placeholder="请选择用户"
    @focus="getUserList"
  />
</template>

<style scoped></style>
