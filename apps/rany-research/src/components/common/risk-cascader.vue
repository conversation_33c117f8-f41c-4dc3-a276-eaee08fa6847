<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { fetchGetRiskTree } from '@/service/api/risk';

const value = defineModel<string[] | undefined | string>('value', { required: true });

const props = defineProps<{
  multiple?: boolean;
  diseaseId?: string;
}>();
const riskOptions = ref<Api.Common.CascaderOption[]>([]);
const loading = ref(false);

// 将转换函数提取到外部,避免每次调用getRiskTree时重新创建
function transformToOptions(items: Api.Risk.RiskListTree[]): Api.Common.CascaderOption[] {
  return (
    items?.map(item => ({
      value: item.id,
      label: item.name,
      children: item.childList?.length ? transformToOptions(item.childList) : [],
      isLeaf: item.childList?.length === 0
    })) || []
  );
}

// 使用async/await优化错误处理
async function getRiskTree() {
  // 已有数据则直接返回,避免重复请求
  if (riskOptions.value.length) return;

  loading.value = true;

  try {
    const { error, data } = await fetchGetRiskTree();

    if (!error && data) {
      riskOptions.value = transformToOptions(data);
    }
  } catch (err) {
    console.error('获取危险度分型失败:', err);
    window.$message?.error('获取危险度分型失败');
  } finally {
    loading.value = false;
  }
}

// 组件挂载时预加载数据
onMounted(getRiskTree);
</script>

<template>
  <NCascader
    v-model:value="value"
    :options="riskOptions"
    :loading="loading"
    size="small"
    :multiple="props.multiple"
    check-strategy="child"
    filterable
    clearable
    placeholder="请选择危险度分型"
  />
</template>

<style scoped></style>
