<script setup lang="ts">
defineProps<{
  title?: string;
  type?: 'default' | 'induction' | 'consolidation' | 'intensification1' | 'intensification2';
}>();

// 根据类型返回容器边框颜色
const getBorderColor = (type: string) => {
  switch (type) {
    case 'induction':
      return 'border-orange-400 dark:border-orange-600 shadow-orange-200/50';
    case 'consolidation':
      return 'border-blue-400 dark:border-blue-600 shadow-blue-200/50';
    case 'intensification1':
    case 'intensification2':
      return 'border-purple-400 dark:border-purple-600 shadow-purple-200/50';
    default:
      return 'border-gray-400 dark:border-gray-600 shadow-gray-200/50';
  }
};

// 根据类型返回标题背景色
const getTitleBackground = (type: string) => {
  switch (type) {
    case 'induction':
      return 'bg-gradient-to-r from-orange-400 to-orange-500 dark:from-orange-600 dark:to-orange-700 text-white shadow-lg';
    case 'consolidation':
      return 'bg-gradient-to-r from-blue-400 to-blue-500 dark:from-blue-600 dark:to-blue-700 text-white shadow-lg';
    case 'intensification1':
    case 'intensification2':
      return 'bg-gradient-to-r from-purple-400 to-purple-500 dark:from-purple-600 dark:to-purple-700 text-white shadow-lg';
    default:
      return 'bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-600 dark:to-gray-700 text-white shadow-lg';
  }
};
</script>

<template>
  <div
    v-if="$props.title"
    class="group-title mb-1 truncate rounded-full px-3 py-1 text-sm font-bold"
    :class="getTitleBackground($props.type || 'default')"
    :title="$props.title"
  >
    {{ $props.title }}
  </div>
  <div
    class="group-container relative border-2 rounded-xl p-5 shadow-lg"
    :class="getBorderColor($props.type || 'default')"
  >
    <div class="group-content pt-3">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
.group-container {
  width: 220px;
  min-height: 120px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
  backdrop-filter: blur(12px);
  padding: 18px;
  border-width: 2px;
  position: relative;
  overflow: hidden;
}

.group-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.dark .group-container {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
}

.group-title {
  max-width: 220px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 12px;
  font-size: 12px;
  font-weight: 800;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  position: relative;
  z-index: 1;
}

.group-content {
  padding-top: 12px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  z-index: 1;
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
