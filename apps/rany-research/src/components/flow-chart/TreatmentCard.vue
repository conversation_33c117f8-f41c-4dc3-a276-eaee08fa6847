<script setup lang="ts">
defineProps<{
  label: string;
  days?: string;
  type?: 'default' | 'induction' | 'consolidation' | 'intensification1' | 'intensification2';
  selected?: boolean;
  hasGroup?: boolean;
  groupTitle?: string;
}>();

// 根据类型返回卡片背景颜色
const getCardBackground = (type: string) => {
  switch (type) {
    case 'induction':
      return 'bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/40 dark:to-orange-800/40';
    case 'consolidation':
      return 'bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40';
    case 'intensification1':
    case 'intensification2':
      return 'bg-gradient-to-br from-purple-100 to-purple-200 dark:from-purple-900/40 dark:to-purple-800/40';
    default:
      return 'bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700';
  }
};

// 获取分组标题背景色
const getGroupTitleBackground = (type: string) => {
  switch (type) {
    case 'induction':
      return 'bg-gradient-to-r from-orange-300 to-orange-400 dark:from-orange-700 dark:to-orange-600 text-orange-900 dark:text-orange-100';
    case 'consolidation':
      return 'bg-gradient-to-r from-blue-300 to-blue-400 dark:from-blue-700 dark:to-blue-600 text-blue-900 dark:text-blue-100';
    case 'intensification1':
    case 'intensification2':
      return 'bg-gradient-to-r from-purple-300 to-purple-400 dark:from-purple-700 dark:to-purple-600 text-purple-900 dark:text-purple-100';
    default:
      return 'bg-gradient-to-r from-gray-300 to-gray-400 dark:from-gray-700 dark:to-gray-600 text-gray-900 dark:text-gray-100';
  }
};
</script>

<template>
  <div class="treatment-card-container">
    <!-- 分组标题 -->
    <div
      v-if="hasGroup && groupTitle"
      class="group-title w-full truncate rounded-t-md px-3 py-1 text-center text-xs font-bold"
      :class="getGroupTitleBackground(type || 'default')"
      :title="groupTitle"
    >
      {{ groupTitle }}
    </div>

    <!-- 卡片内容 -->
    <div
      class="treatment-card border border-white/50 rounded-lg p-3 shadow-md transition-all duration-300 hover:shadow-lg"
      :class="[
        getCardBackground(type || 'default'),
        selected ? 'ring-2 ring-blue-500 ring-offset-2 scale-105' : '',
        hasGroup && groupTitle ? 'rounded-t-none' : ''
      ]"
    >
      <div class="truncate text-sm text-gray-800 font-semibold dark:text-gray-100" :title="label">
        {{ label }}
      </div>
      <div v-if="days" class="mt-1 truncate text-xs text-gray-600 font-medium dark:text-gray-300" :title="days">
        {{ days }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.treatment-card {
  width: 160px;
  cursor: pointer;
  margin-bottom: 8px;
  backdrop-filter: blur(8px);
  position: relative;
  overflow: hidden;
}

.treatment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.treatment-card:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.15);
}

.treatment-card-container {
  display: inline-flex;
  flex-direction: column;
  width: 100%;
}

.group-title {
  font-size: 11px;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
