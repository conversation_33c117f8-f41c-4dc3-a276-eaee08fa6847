<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';

interface Props {
  x: number;
  y: number;
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'save', content: string): void;
  (e: 'position-change', position: { x: number; y: number }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const content = ref('');
const commentBoxRef = ref<HTMLElement>();
const textareaRef = ref<HTMLTextAreaElement>();
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });
const currentPosition = ref({ x: 0, y: 0 });

const handleSave = () => {
  if (content.value.trim()) {
    emit('save', content.value.trim());
    content.value = '';
  }
};

const handleClose = () => {
  emit('update:visible', false);
  content.value = '';
};

// 处理回车键保存
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    handleSave();
  }
};

// 初始化位置
onMounted(() => {
  // 如果传入的是页面坐标，需要转换为容器坐标
  const flowContainer = document.querySelector('.vue-flow-wrapper');
  if (flowContainer) {
    const flowRect = flowContainer.getBoundingClientRect();
    currentPosition.value = {
      x: Math.max(0, props.x - flowRect.left),
      y: Math.max(0, props.y - flowRect.top)
    };
  } else {
    currentPosition.value = { x: props.x, y: props.y };
  }

  // 自动聚焦到输入框
  if (textareaRef.value) {
    textareaRef.value.focus();
  }
});

// 监听props变化，更新位置
watch(
  () => [props.x, props.y],
  newValues => {
    const [newX, newY] = newValues;
    if (!isDragging.value) {
      // 同样需要处理坐标转换
      const flowContainer = document.querySelector('.vue-flow-wrapper');
      if (flowContainer && (newX > 1000 || newY > 1000)) {
        // 如果坐标很大，可能是页面坐标，需要转换
        const flowRect = flowContainer.getBoundingClientRect();
        currentPosition.value = {
          x: Math.max(0, newX - flowRect.left),
          y: Math.max(0, newY - flowRect.top)
        };
      } else {
        // 否则直接使用
        currentPosition.value = { x: newX, y: newY };
      }
    }
  }
);

// 拖拽功能
const handleMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return;

  // 获取VueFlow容器的边界
  const flowContainer = commentBoxRef.value?.closest('.vue-flow-wrapper');
  const flowRect = flowContainer?.getBoundingClientRect();

  if (flowRect) {
    const newX = event.clientX - flowRect.left - dragOffset.value.x;
    const newY = event.clientY - flowRect.top - dragOffset.value.y;

    // 限制在容器边界内
    const boxWidth = 200;
    const boxHeight = 120;
    const maxX = flowRect.width - boxWidth;
    const maxY = flowRect.height - boxHeight;

    currentPosition.value = {
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    };

    emit('position-change', currentPosition.value);
  }
};

const handleMouseUp = () => {
  isDragging.value = false;
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  document.body.style.cursor = '';
  document.body.style.userSelect = '';
};

const handleMouseDown = (event: MouseEvent) => {
  // 如果点击的是删除按钮或输入框，不执行拖拽
  const target = event.target as HTMLElement;
  if (target.classList.contains('delete-btn') || target.tagName === 'TEXTAREA') {
    return;
  }

  event.preventDefault();
  event.stopPropagation();

  isDragging.value = true;
  const rect = commentBoxRef.value?.getBoundingClientRect();
  const flowContainer = commentBoxRef.value?.closest('.vue-flow-wrapper');
  const flowRect = flowContainer?.getBoundingClientRect();

  if (rect && flowRect) {
    dragOffset.value = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };
  }

  // 设置拖拽样式
  document.body.style.cursor = 'grabbing';
  document.body.style.userSelect = 'none';

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', handleMouseUp);
  document.body.style.cursor = '';
  document.body.style.userSelect = '';
});
</script>

<template>
  <div
    v-if="visible"
    ref="commentBoxRef"
    class="comment-box absolute z-50 select-none"
    :class="{ dragging: isDragging }"
    :style="{
      left: `${currentPosition.x}px`,
      top: `${currentPosition.y}px`,
      width: '200px',
      minHeight: '120px',
      background: '#FFFBEF',
      border: '1px solid #FABF01',
      borderRadius: '8px',
      boxShadow: '2px 2px 12px 0px rgba(0, 0, 0, 0.15)',
      cursor: isDragging ? 'grabbing' : 'grab'
    }"
    @mousedown="handleMouseDown"
  >
    <!-- 删除按钮 -->
    <button
      class="delete-btn absolute right-2 top-2 h-5 w-5 flex items-center justify-center text-gray-400 transition-colors hover:text-red-500"
      title="关闭"
      @click="handleClose"
      @mousedown.stop
    >
      <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M9 3L3 9M3 3L9 9"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>

    <!-- 输入区域 -->
    <div class="p-3 pt-4">
      <textarea
        ref="textareaRef"
        v-model="content"
        placeholder="请输入评论内容..."
        class="h-20 w-full resize-none border-none bg-transparent text-sm text-gray-700 outline-none placeholder-gray-400"
        @keydown="handleKeyDown"
        @mousedown.stop
        @click.stop
      />
      <div class="mt-1 text-xs text-gray-400">按回车保存，Shift+回车换行</div>
    </div>
  </div>
</template>

<style scoped>
.comment-box:hover {
  box-shadow: 2px 2px 16px 0px rgba(0, 0, 0, 0.2);
}

.comment-box.dragging {
  cursor: grabbing !important;
}

.delete-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: 50%;
}

textarea::placeholder {
  color: #9ca3af;
  font-size: 14px;
}

textarea {
  font-family: inherit;
  line-height: 1.4;
}
</style>
