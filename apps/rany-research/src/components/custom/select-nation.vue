<script setup>
import { onMounted, ref, watch } from 'vue';
import { NSelect } from 'naive-ui';
import { fetchGetPatientNations } from '@/service/api';

// 定义 props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
});

// 定义 emit
const emit = defineEmits(['update:modelValue']);

// 选中的值
const selectedValue = ref(props.modelValue);

// 民族选项
const nationOptions = ref([]);

// 从 API 获取民族列表
const fetchEthnicityList = async () => {
  try {
    const response = await fetchGetPatientNations();
    const data = response.data;
    nationOptions.value = data.map(item => ({
      label: item.itemValue,
      value: item.id
    }));
  } catch (error) {
    window.$message.error('获取民族列表失败', error);
  }
};

// 更新选中的值并触发 v-model 更新
const onUpdateValue = value => {
  selectedValue.value = value;
  emit('update:modelValue', value);
};

// 组件挂载时获取民族列表
onMounted(() => {
  fetchEthnicityList();
});

// 监听 modelValue 的变化
watch(
  () => props.modelValue,
  newVal => {
    selectedValue.value = newVal;
  }
);
</script>

<template>
  <NSelect
    v-model:value="selectedValue"
    :options="nationOptions"
    placeholder="请选择民族"
    @update:value="onUpdateValue"
  />
</template>

<style scoped>
/* 添加样式 */
</style>
