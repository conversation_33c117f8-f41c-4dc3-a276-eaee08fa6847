import type { HospitalUser as HospitalUserType } from '@/types/hospital-user';

// 导出 HospitalUser 类型，用于 API 中的类型引用
export type HospitalUser = HospitalUserType;

// 表格列配置
export interface HospitalUserTableColumn {
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  fixed?: 'left' | 'right';
  render?: (row: HospitalUser) => any;
}

// 表格操作配置
export interface HospitalUserTableAction {
  label: string;
  key: string;
  type?: 'primary' | 'success' | 'warning' | 'error';
  disabled?: (row: HospitalUser) => boolean;
  show?: (row: HospitalUser) => boolean;
}

// 表格配置
export interface HospitalUserTableConfig {
  columns: HospitalUserTableColumn[];
  actions: HospitalUserTableAction[];
  pagination: {
    pageSize: number;
    showSizePicker: boolean;
    pageSizes: number[];
  };
}

// 默认表格配置
export const defaultTableConfig: HospitalUserTableConfig = {
  columns: [
    { key: 'userName', title: '用户名', width: 120 },
    { key: 'nickName', title: '昵称', width: 120 },
    { key: 'mobile', title: '手机号', width: 130 },
    { key: 'email', title: '邮箱', width: 180 },
    { key: 'status', title: '状态', width: 80 },
    { key: 'createTime', title: '创建时间', width: 160 },
    { key: 'lastLoginTime', title: '最后登录', width: 160 }
  ],
  actions: [
    { label: '编辑', key: 'edit', type: 'primary' },
    { label: '删除', key: 'delete', type: 'error' },
    { label: '重置密码', key: 'resetPassword', type: 'warning' }
  ],
  pagination: {
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100]
  }
};
