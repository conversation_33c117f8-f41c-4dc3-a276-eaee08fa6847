<script setup lang="ts">
import { ref } from 'vue';
import { $t } from '@/locales';

interface Props {
  itemAlign?: NaiveUI.Align;
  loading?: boolean;
  showSearch?: boolean;
  searchPlaceholder?: string;
  searchValue?: string;
}

const props = withDefaults(defineProps<Props>(), {
  itemAlign: 'center',
  loading: false,
  showSearch: true,
  searchPlaceholder: '请输入搜索关键词',
  searchValue: ''
});

interface Emits {
  (e: 'add'): void;
  (e: 'search', value: string): void;
  (e: 'update:searchValue', value: string): void;
}

const emit = defineEmits<Emits>();

// 内部搜索值
const internalSearchValue = ref(props.searchValue);

// 防抖搜索
const debouncedSearch = useDebounceFn((value: string) => {
  emit('update:searchValue', value);
  emit('search', value);
}, 300);

// 处理搜索输入变化
const handleSearchInput = () => {
  debouncedSearch(internalSearchValue.value);
};

// 处理搜索
const handleSearch = () => {
  emit('update:searchValue', internalSearchValue.value);
  emit('search', internalSearchValue.value);
};

// 新增功能
function add() {
  emit('add');
}

// 监听外部 searchValue 变化
watch(
  () => props.searchValue,
  newValue => {
    internalSearchValue.value = newValue;
  }
);
</script>

<template>
  <div class="mb-4 flex items-center justify-between gap-4">
    <!-- 左侧筛选区域 -->
    <div class="flex flex-1 flex-wrap items-center justify-between">
      <div class="flex flex-1 flex-wrap items-center gap-y-3 text-[0.875rem]">
        <slot name="filters">
          <!-- 默认为空，完全由外部定制 -->
        </slot>
      </div>

      <!-- 右侧搜索区域 -->
      <div v-if="showSearch" class="ml-auto shrink-0">
        <slot name="search">
          <NInput
            v-model:value="internalSearchValue"
            size="small"
            :placeholder="searchPlaceholder"
            clearable
            @input="handleSearchInput"
            @keyup.enter="handleSearch"
          >
            <template #suffix>
              <NIcon class="cursor-pointer" :size="16" @click="handleSearch">
                <icon-ic-round-search />
              </NIcon>
            </template>
          </NInput>
        </slot>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <NSpace :align="itemAlign" wrap justify="end" class="lt-sm:w-200px">
      <slot name="prefix"></slot>
      <slot name="default">
        <NButton size="small" ghost type="primary" @click="add">
          <template #icon>
            <icon-ic-round-plus class="text-icon" />
          </template>
          {{ $t('common.add') }}
        </NButton>
      </slot>
      <slot name="suffix"></slot>
    </NSpace>
  </div>
</template>

<style scoped lang="scss">
// 筛选器通用样式
:deep(.filter-item) {
  display: flex;
  align-items: center;
  margin-right: 16px;

  .filter-label {
    margin-right: 8px;
    shrink: 0;
    text-align: left;
    font-size: 0.875rem;
  }

  .filter-control {
    width: 128px;
    shrink: 0;
  }
}

// 选择框样式
:deep(.filter-select) {
  // 确保选择框固定高度为28px
  .n-base-selection {
    font-size: 0.875rem !important;
    height: 28px !important;
    min-height: 28px !important;
    max-height: 28px !important;
  }

  // 内部元素高度调整
  .n-base-selection .n-base-selection-label {
    height: 26px !important;
    line-height: 26px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .n-base-selection .n-base-selection__border {
    border-radius: 4px;
  }

  .n-base-selection .n-base-suffix {
    height: 26px !important;
    line-height: 26px !important;
  }

  // 输入框内的文字
  .n-base-selection-input {
    height: 26px !important;
    line-height: 26px !important;
  }

  // 占位符样式
  .n-base-selection-placeholder {
    height: 26px !important;
    line-height: 26px !important;
  }
}
</style>
