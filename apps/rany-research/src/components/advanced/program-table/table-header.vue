<script lang="tsx" setup>
import { ref, toRefs, watch } from 'vue';

interface Props {
  columnData: Api.Program.Header;
  editMode: boolean;
  resetSignal: number;
}
const props = defineProps<Props>();
const { columnData, editMode, resetSignal } = toRefs(props);

// 添加选中状态管理
const isSelected = ref(false);

// 监听重置信号
watch(resetSignal, () => {
  isSelected.value = false;
});

interface Emit {
  (e: 'editColumnItem', id: string): void;
  (e: 'deleteColumnItem', id: string): void;
}
const emit = defineEmits<Emit>();

const handleEditColumnItem = async (id?: string) => {
  if (id) {
    emit('editColumnItem', id);
  }
};

const handleDeleteColumnItem = (id?: string) => {
  if (id) {
    window.$dialog?.warning({
      title: '删除方案项',
      content: () => (
        <div class="text-center">
          <h1 class="font-700">确定要删除吗?</h1>
          <h1 class="mt-1 text-xs">删除后不可恢复！</h1>
        </div>
      ),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        emit('deleteColumnItem', id);
        isSelected.value = false;
      },
      onNegativeClick: () => {
        isSelected.value = false;
      }
    });
  }
};

const handleHeaderRightClick = (e: MouseEvent) => {
  if (editMode.value) {
    e.preventDefault();
    isSelected.value = !isSelected.value;
  } else {
    // 非编辑模式下阻止右键菜单
    e.preventDefault();
  }
};

const resetSelection = () => {
  isSelected.value = false;
};

defineExpose({
  resetSelection
});
</script>

<template>
  <div class="bg-[#f2f2f2]">
    <div
      :class="`relative rounded transition-all duration-300 ease-in-out cursor-pointer ${isSelected && editMode ? 'bg-[#E6F7FF]' : 'bg-transparent hover:bg-gray-50'}`"
      @contextmenu="handleHeaderRightClick"
    >
      <NFlex align="center" vertical size="small" style="gap: 0 !important">
        <p>
          {{ columnData.title }}
          <NPopover v-if="columnData.comment" trigger="hover" class="max-h-240px max-w-240px text-12px" scrollable>
            <template #trigger>
              <NTag size="small" round type="error" class="text-xs-1 ml-1 cursor-pointer">
                <span class="text-xs">注</span>
              </NTag>
            </template>
            {{ columnData.comment }}
          </NPopover>
        </p>

        <div class="flex items-center">
          <p class="text-xs color-blueGray">{{ columnData.unit || '次' }}</p>

          <div
            v-if="editMode"
            :class="`flex items-center transition-all duration-300 ease-in-out ${
              isSelected ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2 pointer-events-none'
            }`"
          >
            <NButton
              class="mx-1 bg-transparent p-0 transition-all duration-200 hover:scale-110"
              text
              title="删除"
              @click.stop="handleDeleteColumnItem(columnData.key)"
            >
              <SvgIcon
                icon="solar:minus-circle-linear"
                class="text-icon text-red-500 transition-transform duration-200 hover:rotate-90"
              />
            </NButton>
            <NButton
              class="bg-transparent p-0 transition-all duration-200 hover:scale-110"
              title="编辑"
              text
              @click.stop="handleEditColumnItem(columnData.key)"
            >
              <SvgIcon
                icon="solar:pen-line-duotone"
                class="text-icon text-coolGray transition-transform duration-200 hover:rotate-12"
              />
            </NButton>
          </div>
        </div>
      </NFlex>
    </div>
  </div>
</template>
