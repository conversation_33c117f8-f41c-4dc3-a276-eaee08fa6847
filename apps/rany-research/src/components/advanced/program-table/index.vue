<script setup lang="tsx">
import type { DataTableColumn, DataTableColumnGroup } from 'naive-ui';
import { computed, ref, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { useItemStore } from '@/store/modules/program/item';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { useIntersectionObserver } from '@/hooks/common/useIntersections';
import { useProgramStore } from '@/store/modules/program';
import { useAppStore } from '@/store/modules/app';
import ItemModal from './item-value/item-modal.vue';
import TableHeader from './table-header.vue';
import TableCell from './table-cell.vue';
import ActualItemModal from './actual-value/actual-item-modal.vue';

interface Props {
  editMode: boolean;
  clinicId?: string;
  soluId?: string;
  isBag?: boolean;
}
const props = defineProps<Props>();
const { clinicId, soluId, isBag } = toRefs(props);

const route = useRoute();
const store = useProgramStore();
const appStore = useAppStore();
const itemStore = useItemStore();
const { load, model } = itemStore.getProgramDetail();

// 添加右键菜单相关状态
const contextMenuRowIndex = ref<number>(-1);
const showContextMenu = ref(false);

// 添加表头重置信号
const headerResetSignal = ref(0);

const loadTableData = async () => {
  if (clinicId.value && soluId.value) {
    await load(soluId.value, clinicId.value);
  } else if (soluId.value || route.params.id) {
    await load(soluId.value || (route.params.id as string), clinicId.value);
  }
};

const { elementRef } = useIntersectionObserver(loadTableData, { threshold: 0 });

const rowProps = (_row: any, rowIndex: number) => {
  return {
    style: 'cursor: pointer;',
    onContextmenu: (e: MouseEvent) => {
      if (props.editMode) {
        e.preventDefault();
        contextMenuRowIndex.value = rowIndex;
        showContextMenu.value = true;
      } else {
        // 非编辑模式下阻止右键菜单
        e.preventDefault();
      }
    }
  };
};

/** 表头操作逻辑 */
const itemModalRef = ref<InstanceType<typeof ItemModal>>();
/**
 * 处理删除列项目
 *
 * @param id 项目ID
 */
const handleDeleteColumnItem = async (id: string) => {
  await itemStore.deleteItemToProgram(id);
  loadTableData();
  // 删除完成后重置表头选中状态
  headerResetSignal.value += 1;
};
/**
 * 处理编辑列项目
 *
 * @param id 项目ID
 */
const handleEditColumnItem = async (id: string) => {
  // store.programModal.id = id;
  await itemStore.getProgramItemDetail(id);
  itemModalRef.value?.show();
  // 编辑时重置表头选中状态
  headerResetSignal.value += 1;
};
const titleRender = (columnData: Api.Program.Header) => (
  <TableHeader
    columnData={columnData}
    editMode={props.editMode}
    resetSignal={headerResetSignal.value}
    onEditColumnItem={handleEditColumnItem}
    onDeleteColumnItem={handleDeleteColumnItem}
  />
);

/** 单元格渲染操作 */
const renderCellContent = (value: any, child: Api.Program.Header, index: number) => (
  <TableCell
    value={value}
    child={child}
    index={index}
    model={model.value}
    editMode={props.editMode}
    onEditItem={handleEditItem}
    class="h-full min-h-[28px] rounded-[4px] bg-white p-[4px]"
  />
);

const actualItemModalRef = ref<InstanceType<typeof ActualItemModal>>();
async function handleEditItem(isActualValue: boolean, id?: string) {
  if (isActualValue) {
    actualItemModalRef.value?.show(id);
  } else {
    if (isBag.value) return; // 专科袋实际值编辑暂不实现，专科袋暂不支持小项定制
    store.programModal.id = soluId.value;
    itemModalRef.value?.show(true);
  }
}

// 处理子列配置
const processChildColumn = (child: DataTableColumn) => ({
  ...child,
  align: 'center',
  title: () => titleRender(child as Api.Program.Header),
  render: (row: any, index: number) => {
    return renderCellContent(row[(child as Api.Program.Header).key as string], child as Api.Program.Header, index);
  }
});

// 处理主列配置
const processMainColumn = (column: DataTableColumnGroup): DataTableColumnGroup | DataTableColumn => {
  if (column.children) {
    return {
      ...column,
      align: 'center',
      width: column.title === '天数' ? 60 : undefined,
      children: column.children?.map(processChildColumn) as any
    };
  }

  // 如果是天数列且为编辑模式，添加删除按钮
  if (column.title === '天数' && props.editMode) {
    return {
      ...column,
      align: 'center',
      width: 100, // 增加宽度以容纳删除按钮
      render: (row: any, rowIndex: number) => (
        <div class="h-full min-h-[28px] rounded-[4px] bg-white px-[4px] py-[4px]">
          <div
            class={`relative rounded-[4px] flex items-center justify-center transition-all duration-300 ease-in-out ${contextMenuRowIndex.value === rowIndex && showContextMenu.value ? 'bg-[#E6F7FF]' : 'bg-white'}`}
          >
            <n-tooltip trigger="hover" placement="top">
              {{
                trigger: () => (
                  <n-button
                    class={`absolute left-1 top-1/2 p-0 align-middle transform -translate-y-1/2 transition-all duration-300 ease-in-out hover:scale-110 ${
                      contextMenuRowIndex.value === rowIndex && showContextMenu.value
                        ? 'opacity-100 translate-x-0'
                        : 'opacity-0 -translate-x-2 pointer-events-none'
                    }`}
                    type="text"
                    text
                    onClick={() => handleDeleteRowItem(rowIndex)}
                  >
                    <SvgIcon
                      icon="solar:minus-circle-linear"
                      class="text-icon text-red-500 transition-transform duration-200 hover:rotate-90"
                    />
                  </n-button>
                ),
                default: () => '删除一整行'
              }}
            </n-tooltip>
            <span
              class={`text-center transition-all duration-300 ease-in-out ${
                contextMenuRowIndex.value === rowIndex && showContextMenu.value
                  ? 'transform translate-x-2'
                  : 'transform translate-x-0'
              }`}
            >
              {row[column.key as string]}
            </span>
          </div>
        </div>
      )
    };
  }

  return { ...column, align: 'center', width: column.title === '天数' ? 60 : undefined };
};

const computedTableColumns = computed(() => {
  if (!model.value?.headers) return [];

  const columns = (model.value.headers as DataTableColumnGroup[]).map(processMainColumn);
  return columns;
});

/**
 * 处理删除行项目 弹出确认对话框,确认后删除指定行的项目并重新加载表格数据
 *
 * @param rowIndex - 要删除的行索引
 */
function handleDeleteRowItem(rowIndex: number) {
  window.$dialog?.warning({
    title: '删除方案项',
    content: () => (
      <div class="text-center">
        <h1 class="font-700">确定要删除吗?</h1>
        <h1 class="mt-1 text-xs">删除后不可恢复！</h1>
      </div>
    ),
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      if (soluId.value) {
        await itemStore.deleteRowItemToProgram(rowIndex, soluId.value);
        loadTableData();
        // 重置上下文菜单状态
        contextMenuRowIndex.value = -1;
        showContextMenu.value = false;
      }
    },
    onNegativeClick: () => {
      // 取消时也重置状态
      contextMenuRowIndex.value = -1;
      showContextMenu.value = false;
    }
  });
}

/** 处理添加项目 设置当前方案ID并显示项目编辑弹窗 */
function handleAddItem() {
  itemModalRef.value?.show();
}

// 点击其他地方隐藏右键菜单效果和重置表头选中状态
const handleClickOutside = () => {
  contextMenuRowIndex.value = -1;
  showContextMenu.value = false;
  // 更新重置信号来重置所有表头选中状态
  headerResetSignal.value += 1;
};

defineExpose({
  hasData: computed(() => Boolean(model.value?.tableData?.length))
});
</script>

<template>
  <div
    ref="elementRef"
    class="flex-col-stretch flex-1-hidden overflow-hidden lt-sm:overflow-auto"
    @click="handleClickOutside"
  >
    <NCard
      :bordered="false"
      size="small"
      class="sm:flex-1-hidden"
      content-style="padding:16px 24px;display:flex;flex-direction:column; "
    >
      <TableHeaderOperation v-if="editMode" :disabled-delete="true" :show-search="false" @add="handleAddItem">
        <template #filters>
          <h1 class="text-base text-black font-400">方案设置</h1>
        </template>
      </TableHeaderOperation>
      <NDataTable
        class="sm:h-full"
        :row-props="rowProps"
        :bordered="false"
        size="small"
        :single-line="false"
        :flex-height="!appStore.isMobile"
        :loading="store.actionLoading"
        :data="model?.tableData"
        :columns="computedTableColumns"
      />
    </NCard>

    <ItemModal ref="itemModalRef" @submitted="loadTableData()" />
    <ActualItemModal ref="actualItemModalRef" @submitted="loadTableData()" />
  </div>
</template>

<style lang="scss" scoped>
:deep(.n-data-table:not(.n-data-table--single-line) .n-data-table-th) {
  border-right: none !important;
}
:deep(.n-data-table .n-data-table-th) {
  border-bottom: none !important;
  background-color: #f2f2f2;
}
:deep(.n-data-table .n-data-table-td) {
  border-bottom: none !important;
  background-color: #f2f2f2;
  padding: 2px 3px;
}
:deep(.n-data-table .n-data-table-tr .n-data-table-td:first-child) {
  padding-left: 8px;
}
:deep(.n-data-table .n-data-table-tr .n-data-table-td:last-child) {
  padding-right: 8px;
}

:deep(.n-data-table:not(.n-data-table--single-line) .n-data-table-td) {
  border-right: none !important;
}
:deep(.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover > .n-data-table-td) {
  background-color: #f2f2f2 !important;
}
:deep(.n-data-table.n-data-table--bottom-bordered .n-data-table-td.n-data-table-td--last-row) {
  padding-bottom: 10px;
}
</style>
