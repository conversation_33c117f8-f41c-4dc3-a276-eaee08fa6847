<script lang="ts" setup>
import { usePlatformStore } from '@/store/modules/platform';

const platformStore = usePlatformStore();
const {
  loading: frequencyLoading,
  options: frequencyOptions,
  load: loadFrequency
} = platformStore.getMedicineFrequencyDictOptions();
const model = defineModel<string | string[] | undefined>();
</script>

<template>
  <NSelect
    v-model:value="model"
    :loading="frequencyLoading"
    :options="frequencyOptions"
    class="w-full"
    placeholder="请选择频次"
    clearable
    @focus="loadFrequency"
  ></NSelect>
</template>
