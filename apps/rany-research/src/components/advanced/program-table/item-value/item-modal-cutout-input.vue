<script setup lang="ts">
const modelValue = defineModel<number>();

const parseDay = (input: string) => {
  const nums = input.replace(/(,|天|\s)/g, '').trim();
  if (/^\d+(\.(\d+)?)?$/.test(nums)) return Number(nums);
  return nums === '' ? null : Number.NaN;
};

const formatDay = (value: number | null) => {
  if (value === null) return '';
  return `${value.toLocaleString('en-US')} 天`;
};
</script>

<template>
  <NInputNumber
    v-model:value="modelValue"
    class="w-full"
    :min="1"
    clearable
    placeholder="减停天数"
    :format="formatDay"
    :parse="parseDay"
  />
</template>
