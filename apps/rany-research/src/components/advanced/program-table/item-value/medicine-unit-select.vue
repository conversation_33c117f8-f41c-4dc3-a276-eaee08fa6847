<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import { useItemStore } from '@/store/modules/program/item';

const store = useItemStore();
const { unitOptions } = storeToRefs(store);
const model = defineModel<string | string[] | undefined>();
</script>

<template>
  <NSelect v-model:value="model" :options="unitOptions" class="w-120px">
    <template #default="{ option }">
      {{ option.label }}
      <NTag v-if="option.isDefault" size="small" round type="info" class="ml-1">1</NTag>
    </template>
  </NSelect>
</template>
