<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import type { TreeSelectOption } from 'naive-ui';
import { usePlatformStore } from '@/store/modules/platform';

const platformStore = usePlatformStore();
const options = ref<TreeSelectOption[]>([]);
const { loading: routeLoading, load: loadRoute } = platformStore.getMedicineRouteDictTree();

const model = defineModel<string[]>();

onMounted(async () => {
  options.value = await loadRoute();
});

async function handleLoad(node: TreeSelectOption) {
  const option = await loadRoute(node.key?.toString(), node.depth as any);
  node.children = option;
}
</script>

<template>
  <NTreeSelect
    v-model:value="model"
    allow-checking-not-loaded
    multiple
    checkable
    cascade
    show-path
    check-strategy="child"
    :loading="routeLoading"
    :options="options"
    :on-load="handleLoad"
  />
</template>
