<script lang="ts" setup>
import type { CascaderOption } from 'naive-ui';
import { onMounted } from 'vue';
import { usePlatformStore } from '@/store/modules/platform';

interface Props {
  editMode: boolean;
}

const props = defineProps<Props>();

const platformStore = usePlatformStore();
const { options: labourOptions, load: loadCheckOptions } = platformStore.getCheckOptions();

onMounted(() => {
  loadCheckOptions('10002', 1);
});

async function handleLoad(option: CascaderOption) {
  if (option.value) {
    await loadCheckOptions(option.value.toString(), (option.depth as number) + 1, option);
  }
}
</script>

<template>
  <NCascader
    placeholder="选择检验项目"
    expand-trigger="hover"
    :disabled="props.editMode"
    check-strategy="child"
    remote
    :ellipsis-tag-popover-props="{
      width: 800,
      trigger: 'hover'
    }"
    :options="labourOptions"
    :on-load="handleLoad"
    :show-path="true"
    :filterable="true"
    :get-column-style="
      detail => {
        if (detail.level === 1) return { minWidth: '300px' };
        return {};
      }
    "
  />
</template>
