<script setup lang="ts">
import { computed, ref, watchEffect } from 'vue';
import type { FormInst, FormRules } from 'naive-ui';
import { storeToRefs } from 'pinia';
import { useItemStore } from '@/store/modules/program/item';
import { usePlatformStore } from '@/store/modules/platform';
import { useSuiteStore } from '@/store/modules/database/suite';
import ItemModalCutoutInput from './item-modal-cutout-input.vue';
import ItemModalTypeCop from './item-modal-type-cop.vue';

const emit = defineEmits<{
  (e: 'submitted'): void;
}>();

const props = defineProps<{
  moduleId?: string | undefined;
}>();

const isCustom = ref(false);

const store = useItemStore();
const suiteStore = useSuiteStore();
const { itemModel, Type, unitOptions } = storeToRefs(store);
const platformStore = usePlatformStore();
const {
  loading: frequencyLoading,
  options: frequencyOptions,
  load: loadFrequency
} = platformStore.getMedicineFrequencyDictOptions();

const editMode = computed(() => Boolean(itemModel.value.id));
const title = computed(() => (editMode.value ? '编辑项目' : '新增项目'));

const visible = ref(false);
const formRef = ref<FormInst | null>(null);

const rules: FormRules = {
  name: {
    required: true,
    message: '请选择项目名称',
    trigger: 'blur'
  },
  singleDose: {
    validator: () => {
      const value = itemModel.value.singleDose;
      const type = itemModel.value.type;

      if (value === null || value === undefined) {
        return true;
      }

      // 用药类型(3)可以是大于0的小数
      if (type === Type.value.medicine) {
        if (value <= 0) {
          return new Error('请输入大于0的数字');
        }
        return true;
      }

      // 非用药类型只能是正整数
      if (!Number.isInteger(value) || value <= 0) {
        return new Error('请输入正整数');
      }
      return true;
    },
    trigger: ['blur', 'change']
  },
  mediPlan: [
    {
      required: true,
      message: '请输入用药计划',
      trigger: 'blur'
    },
    {
      validator: () => {
        const inputRegex = /^[0-9,-]*$/;
        const value = itemModel.value.mediPlan as string;
        // 基础格式检查
        if (!inputRegex.test(value)) {
          return new Error('请输入正确的格式, 例如 1,5-8 (英文逗号)');
        }

        // 检查连续的逗号或连字符
        if (value.includes(',,') || value.includes('--')) {
          return new Error('请不要输入连续的逗号或连字符');
        }

        // 分割并验证每个部分
        const parts = value.split(',');

        for (const part of parts) {
          if (!part) return false; // 防止空值

          if (part.includes('-')) {
            const [start, end] = part.split('-');

            // 确保只有一个连字符
            if (part.split('-').length > 2) return new Error('多个时间段请用逗号分隔');

            // 验证数字范围
            const startNum = Number.parseInt(start, 10);
            const endNum = Number.parseInt(end, 10);

            if (
              Number.isNaN(startNum) ||
              Number.isNaN(endNum) ||
              startNum <= 0 ||
              startNum >= 1000 ||
              endNum <= 0 ||
              endNum >= 1000 ||
              endNum <= startNum
            ) {
              return new Error('时间段范围错误,并且最多时间不能超过999天');
            }
          } else {
            // 单个数字验证
            const num = Number.parseInt(part, 10);
            if (Number.isNaN(num) || num <= 0 || num >= 1000) {
              return new Error('天数需要在 1 到 999 之间');
            }
          }
        }

        return true;
      },
      trigger: 'blur'
    }
  ]
};

const handleCancel = () => {
  visible.value = false;
  store.$reset();
};

watchEffect(() => {
  if (visible.value === false) {
    store.$reset();
  }
});

const handleConfirm = async () => {
  if (!formRef.value) return;
  const validated = await formRef.value.validate();
  if (validated) {
    let res = false;
    if (props.moduleId) {
      if (!itemModel.value.id) {
        res = await suiteStore.addSuiteItem({
          name: itemModel.value.name,
          moduleType: 0,
          orderNumber: 1,
          comment: itemModel.value.comment,
          singleDose: itemModel.value.singleDose,
          mediPlan: itemModel.value.mediPlan,
          type: itemModel.value.type,
          moduleId: props.moduleId,
          refId: itemModel.value.refId
        });
      } else {
        res = await suiteStore.updateSuiteItem({
          id: itemModel.value.id,
          name: itemModel.value.name,
          moduleType: 0,
          orderNumber: 1,
          comment: itemModel.value.comment,
          singleDose: itemModel.value.singleDose,
          mediPlan: itemModel.value.mediPlan,
          type: itemModel.value.type,
          moduleId: props.moduleId
        });
      }
    } else if (editMode.value && isCustom.value) {
      if (!itemModel.value.itemId) res = await store.addProgramCustomItem();
      else res = await store.updateProgramCustomItem();
    } else if (editMode.value) {
      res = await store.submitUpdateItemToProgram();
    } else {
      res = await store.submitAddItemToProgram();
    }
    if (res) {
      emit('submitted');
      visible.value = false;
    }
  }
};

const handleTypeSelectChange = () => {
  if (itemModel.value.type === Type.value.medicine) {
    itemModel.value.unit = unitOptions.value[0].value;
  } else {
    itemModel.value.unit = '次';
  }
};

defineExpose({
  show: (isCustomParam?: boolean) => {
    visible.value = true;
    isCustom.value = Boolean(isCustomParam);
  }
});
</script>

<template>
  <NModal v-model:show="visible" class="w-2xl" :title="title" :mask-closable="false" preset="card">
    <div class="px-6 py-4">
      <NForm
        ref="formRef"
        :model="itemModel"
        :rules="rules"
        label-placement="top"
        :label-width="100"
        require-mark-placement="right-hanging"
      >
        <!-- 项目名称 -->
        <NFormItem label="项目名称" path="name">
          <ItemModalTypeCop
            v-model:item-type="itemModel.type"
            v-model:ref-id="itemModel.refId"
            v-model:name="itemModel.name"
            :edit-mode="editMode"
            class="w-full"
            @select-change="handleTypeSelectChange"
          />
        </NFormItem>

        <!-- 单次用量 -->
        <NFlex>
          <NFormItem label="单次用量" path="singleDose">
            <NInputNumber v-model:value="itemModel.singleDose" clearable class="w-120px" />
          </NFormItem>

          <NFormItem label="单位" path="unit">
            <NSelect
              v-model:value="itemModel.unit"
              :disabled="editMode || itemModel.type !== Type.medicine"
              :options="unitOptions"
              class="w-120px"
            >
              <template #default="{ option }">
                {{ option.label }}
                <NTag v-if="option.isDefault" size="small" round type="info" class="ml-1">1</NTag>
              </template>
            </NSelect>
          </NFormItem>
        </NFlex>

        <!-- 给药途径 -->
        <NFlex v-show="itemModel.type === Type.medicine || itemModel.type === Type.stack">
          <NFormItem label="给药途径" path="mediRoute" class="flex-1">
            <MedicineRouteSelect v-model="itemModel.mediRoute as string[]" />
          </NFormItem>

          <!-- 频次 -->
          <NFormItem label="频次" path="frequency" class="flex-1">
            <NSelect
              v-model:value="itemModel.frequency"
              :loading="frequencyLoading"
              :options="frequencyOptions"
              class="w-full"
              placeholder="请选择频次"
              clearable
              @focus="loadFrequency"
            ></NSelect>
          </NFormItem>
        </NFlex>

        <!-- 用药计划 -->
        <NFlex>
          <NFormItem class="flex-1" label="用药计划" path="mediPlan">
            <div class="flex items-center gap-2">
              <span>第</span>
              <NInput
                v-model:value="itemModel.mediPlan"
                :disabled="editMode"
                placeholder="例如1-4,6,11-13"
                class="w-200px"
              />
              <span>天</span>
            </div>
          </NFormItem>
          <NFormItem v-show="itemModel.type === Type.medicine" class="flex-1" label="减停" path="cutOut">
            <ItemModalCutoutInput v-model="itemModel.cutOut" />
          </NFormItem>
        </NFlex>

        <!-- 注释 -->
        <NFormItem label="注释" path="comment">
          <NInput v-model:value="itemModel.comment" type="textarea" placeholder="请输入注释" class="w-full" />
        </NFormItem>
      </NForm>
    </div>

    <!-- 底部按钮 -->
    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" @click="handleCancel">取消</NButton>
      <NButton type="primary" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>
