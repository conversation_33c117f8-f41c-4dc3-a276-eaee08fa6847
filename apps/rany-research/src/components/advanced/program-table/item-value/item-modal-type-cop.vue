<script setup lang="ts">
import { storeToRefs } from 'pinia';
import type { SelectBaseOption } from 'naive-ui/es/select/src/interface';
import type { CascaderOption } from 'naive-ui';
import { onMounted, ref, watchEffect } from 'vue';
import { useItemStore } from '@/store/modules/program/item';
import { usePlatformStore } from '@/store/modules/platform';
import { useSuiteStore } from '@/store/modules/database/suite';
import LabourCascader from './labour-cascader.vue';
import ExaminationCascader from './examination-cascader.vue';
import OperationCascader from './operation-cascader.vue';

const emit = defineEmits<{
  (e: 'selectChange'): void;
}>();

const store = useItemStore();
const { Type } = storeToRefs(store);

const suiteStore = useSuiteStore();

const props = defineProps<{
  editMode: boolean;
}>();

const platformStore = usePlatformStore();

const itemType = defineModel<number>('itemType');
const refId = defineModel<string>('refId');
const name = defineModel<string>('name');

const handleSelectChange = (_: string, option: SelectBaseOption) => {
  name.value = option.label?.toString() ?? '';
  refId.value = _;
};

const cascaderValue = ref(name.value);
const handleCheckChange = (_: any, option: CascaderOption, pathValues: Array<CascaderOption | null>) => {
  cascaderValue.value = pathValues.map(p => p?.label).join('/');
  name.value = option.label;
  refId.value = option.value?.toString();
};

onMounted(() => {
  if (!props.editMode) {
    store.initTypeOptions(true);
  } else {
    store.initTypeOptions();
  }
});

const handelTypeChange = (type: number) => {
  itemType.value = type;
  cascaderValue.value = '';
  refId.value = '';
  emit('selectChange');
};

const {
  loading: medicineLoading,
  options: medicineOptions,
  load: loadMedicine
} = platformStore.getMedicineDictOptions();

const { loading: suiteLoading, options: suiteOptions, load: loadSuite } = suiteStore.getSuitOptions();

watchEffect(() => {
  if (props.editMode && itemType.value) {
    const loadFunctions: { [index: number]: any } = {
      [Type.value.medicine]: loadMedicine,
      [Type.value.stack]: loadSuite
    };
    const loadFunction = loadFunctions[itemType.value];
    if (loadFunction) {
      loadFunction();
    }
  }
});
</script>

<template>
  <div class="flex gap-2">
    <!-- 项目名称 -->
    <NSelect
      v-model:value="itemType"
      :disabled="editMode"
      :options="suiteStore.itemTypeList"
      class="w-120px"
      @update-value="handelTypeChange"
    />

    <!-- 西药列表 -->
    <NSelect
      v-show="itemType === Type.medicine"
      v-model:value="refId"
      :disabled="editMode"
      filterable
      :loading="medicineLoading"
      :options="medicineOptions"
      placeholder="请选择西成药"
      class="w-full"
      :on-update:value="handleSelectChange"
      @focus="loadMedicine"
    >
      <template #default="{ option }">
        {{ option.label }}
      </template>
    </NSelect>

    <NSelect
      v-show="itemType === Type.stack"
      v-model:value="refId"
      filterable
      :disabled="editMode"
      :loading="suiteLoading"
      :options="suiteOptions"
      placeholder="请选择组套项目"
      class="w-full"
      :on-update:value="handleSelectChange"
      @focus="loadSuite"
    >
      <template #default="{ option }">
        {{ option.label }}
      </template>
    </NSelect>

    <!-- 手术列表 -->
    <OperationCascader
      v-show="itemType === Type.operation"
      v-model:value="cascaderValue"
      :edit-mode="editMode"
      @update:value="handleCheckChange"
    />
    <!-- 检验列表 -->
    <LabourCascader
      v-show="itemType === Type.labour"
      v-model:value="cascaderValue"
      class="w-200px w-full"
      :edit-mode="editMode"
      @update:value="handleCheckChange"
    />
    <!-- 检查列表 -->
    <ExaminationCascader
      v-show="itemType === Type.examination"
      v-model:value="cascaderValue"
      :edit-mode="editMode"
      @update:value="handleCheckChange"
    />
  </div>
</template>
