<script lang="ts" setup>
import type { CascaderOption } from 'naive-ui';
import { onMounted } from 'vue';
import { usePlatformStore } from '@/store/modules/platform';

interface Props {
  editMode: boolean;
}

const props = defineProps<Props>();

const platformStore = usePlatformStore();
const { options, load } = platformStore.getOperationOptions();

onMounted(() => {
  load();
});

async function handleLoad(option: CascaderOption) {
  if (option.value) {
    await load(option.value.toString(), (option.depth as number) + 1, option);
  }
}
</script>

<template>
  <NCascader
    placeholder="选择手术项目"
    expand-trigger="hover"
    :disabled="props.editMode"
    check-strategy="child"
    remote
    :options="options"
    :on-load="handleLoad"
    :show-path="true"
    :filterable="true"
  />
</template>
