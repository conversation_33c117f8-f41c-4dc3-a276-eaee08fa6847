<script setup lang="ts">
import { ref, toRefs, watch } from 'vue';

interface Props {
  content?: string;
}

const props = defineProps<Props>();
const { content } = toRefs(props);
const labourContents = ref<DataPlatformApi.CheckItem.ItemRecord[]>([]);
watch(
  content,
  () => {
    if (content.value) {
      labourContents.value = JSON.parse(content.value);
    }
  },
  { immediate: true }
);

defineExpose({
  labourContents
});
</script>

<template>
  <NFlex class="gap-2">
    <div v-for="item in labourContents" :key="item.id" class="flex gap-2">
      <p class="w-120px">{{ item.itemShort }}:</p>
      <div class="w-120px">
        <NInput v-model:value="item.value" size="small" />
      </div>
    </div>
  </NFlex>
</template>
