<script lang="ts" setup>
import type { FormInst, FormRules } from 'naive-ui';
import { storeToRefs } from 'pinia';
import { ref, watch } from 'vue';
import { useActualStore } from '@/store/modules/bag/actual';
import { ItemType } from '@/store/modules/program/item';
import LabourContent from './labour-content.vue';

const emit = defineEmits<{
  (e: 'submitted'): void;
}>();
const store = useActualStore();
const { model, loading } = storeToRefs(store);

const visible = ref(false);
watch(visible, () => {
  if (!visible.value) {
    store.$reset();
  }
});

const labourRef = ref<InstanceType<typeof LabourContent>>();
const itemId = ref();

const formRef = ref<FormInst | null>(null);
const rules: FormRules = {
  actualDay: {
    required: true,
    message: '请选择日期',
    trigger: 'blur'
  }
};

const handleCancel = () => {
  visible.value = false;
  store.$reset();
};

const handleConfirm = async () => {
  if (!formRef.value) return;
  const validated = await formRef.value.validate();
  if (validated) {
    model.value.sbagItemId = itemId.value;
    if (labourRef.value) {
      model.value.singleDose = labourRef.value?.labourContents;
    }
    if (model.value.id) {
      await store.updateActualValue(model.value as any);
    } else {
      await store.addActualValue(model.value as any);
    }
    emit('submitted');
    visible.value = false;
  }
};

defineExpose({
  show: (id?: string) => {
    visible.value = true;
    itemId.value = id;
  }
});
</script>

<template>
  <NModal v-model:show="visible" class="w-2xl" title="填写专科袋" :mask-closable="false" preset="card">
    <div class="px-6 py-4" :loading="loading">
      <NForm
        ref="formRef"
        :model="model"
        :rules="rules"
        label-placement="top"
        :label-width="100"
        require-mark-placement="right-hanging"
      >
        <NFormItem label="日期" path="actualDay">
          <NDatePicker v-model:formatted-value="model.actualDay" value-format="yyyy-MM-dd HH:mm:ss" type="date" />
        </NFormItem>
        <NFormItem label="项目名称" path="name">
          <ItemModalTypeCop
            v-model:item-type="store.currentItem.type"
            v-model:ref-id="store.currentItem.refId"
            v-model:name="store.currentItem.name"
            :edit-mode="true"
            class="w-full"
          />
        </NFormItem>

        <NFlex v-if="store.currentItem.type === ItemType.medicine">
          <NFormItem label="单次用量" path="singleDose" class="flex-1">
            <NInput v-model:value="model.singleDose" :min="0" clearable class="w-120px" />
          </NFormItem>

          <NFormItem label="单位" path="unit">
            <MedicineUnitSelect v-model:value="model.unit" :disabled="true" />
          </NFormItem>

          <!-- 给药途径 -->
          <NFormItem label="给药途径" path="mediRoute" class="flex-1">
            <MedicineRouteSelect v-model:value="model.mediRoute" />
          </NFormItem>

          <!-- 频次 -->
          <NFormItem label="频次" path="frequency" class="flex-1">
            <MedicineFrequencySelect v-model:value="model.frequency" />
          </NFormItem>
        </NFlex>

        <LabourContent
          v-else-if="store.currentItem.type === ItemType.labour"
          ref="labourRef"
          :content="model.singleDose"
        />
      </NForm>
    </div>

    <div class="flex justify-end gap-3 px-6 pb-4">
      <NButton class="w-24" @click="handleCancel">取消</NButton>
      <NButton type="primary" :loading="loading" class="w-24" @click="handleConfirm">确定</NButton>
    </div>
  </NModal>
</template>
