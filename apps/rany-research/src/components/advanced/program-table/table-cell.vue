<script lang="ts" setup>
import { computed, toRefs } from 'vue';
import { useItemStore } from '@/store/modules/program/item';
import { useActualStore } from '@/store/modules/bag/actual';

const emit = defineEmits<{
  (e: 'editItem', isActualValue: boolean, id?: string, day?: number): void;
}>();

interface Props {
  value: any;
  child: Api.Program.Header;
  index: number;
  editMode: boolean;
  model?: Api.Program.ItemResponseData;
}
const itemStore = useItemStore();
const props = defineProps<Props>();
const { value, child, index, editMode, model } = toRefs(props);

const actualItemStore = useActualStore();

// 动态获取comment值，优先使用itemCustomMap中的comment，如果没有则不显示
const dynamicComment = computed(() => {
  if (!model.value?.tableData || !child.value.key) return '';

  const currentRow = model.value.tableData[index.value];
  if (!currentRow?.itemCustomMap) return '';

  const itemCustom = currentRow.itemCustomMap[child.value.key];
  return itemCustom?.comment || '';
});

async function handleEditItem() {
  if (!editMode.value && child.value.key && model.value) {
    actualItemStore.getActualValueByItemId(child.value.key, model.value, index.value);
    emit('editItem', true, child.value.key);
    return;
  }
  if (model.value?.tableData && child.value.key) {
    await itemStore.setItemDetailById(child.value.key, model.value, index.value);
    emit('editItem', false);
  }
}
</script>

<template>
  <div>
    <NPopover v-if="typeof value === 'object'" trigger="hover" :delay="500" :duration="500">
      <template #trigger>
        <span class="hover:text-gray" scoped @click="handleEditItem">
          {{ value.value || value }}
        </span>
      </template>
      <span class="text-12px">{{ value.popContent || '无' }}</span>
    </NPopover>
    <div v-else class="flex">
      <NPopover trigger="hover" class="max-w-400px text-12px" scrollable>
        <template #trigger>
          <span
            class="w-full flex items-center overflow-hidden text-ellipsis whitespace-nowrap hover:text-blue-500"
            scoped
          >
            <p class="m-0 flex-1 overflow-hidden text-ellipsis whitespace-nowrap" @click="handleEditItem">
              {{ value.value || value }}
            </p>
          </span>
        </template>
        <div>
          <div>{{ value.value || value }}</div>
        </div>
      </NPopover>
      <NPopover v-if="dynamicComment" trigger="hover" class="max-h-240px max-w-240px text-12px" scrollable>
        <template #trigger>
          <NTag size="small" round type="error" class="text-xs-1 ml-1 cursor-pointer">
            <span class="text-xs">注</span>
          </NTag>
        </template>
        {{ dynamicComment }}
      </NPopover>
    </div>
  </div>
</template>

<style lang="scss"></style>
