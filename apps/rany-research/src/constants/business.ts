import { transformRecordToOption } from '@/utils/common';
export const enableStatusRecord: Record<Api.Common.EnableStatus, App.I18n.I18nKey> = {
  1: 'page.hospital.common.status.enable',
  0: 'page.hospital.common.status.disable'
};
export const enableStatusOptions = transformRecordToOption(enableStatusRecord);

export const registerStatusRecord: Record<Api.Common.EnableStatus, App.I18n.I18nKey> = {
  1: 'page.database.register.common.status.enable',
  0: 'page.database.register.common.status.disable'
};
export const registerStatusOptions = transformRecordToOption(registerStatusRecord);

// 研究状态分为0-就绪，1-进行中，2-终止
export const experimentStatusRecord: Record<Api.Experiment.ExperimentStatus, App.I18n.I18nKey> = {
  0: 'page.experiment.common.status.ready',
  1: 'page.experiment.common.status.ongoing',
  2: 'page.experiment.common.status.stop'
};
export const experimentStatusOptions = transformRecordToOption(experimentStatusRecord);

// 登记首页- 0-公共 1-私有
export const registerTypeRecord: Record<Api.Common.EnableStatus, App.I18n.I18nKey> = {
  0: 'page.database.register.common.type.public',
  1: 'page.database.register.common.type.private'
};
export const registerTypeOptions = transformRecordToOption(registerTypeRecord);
