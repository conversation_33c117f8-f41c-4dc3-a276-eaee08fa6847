// useIntersectionObserver.js
import { onMounted, onUnmounted, ref } from 'vue';

export function useIntersectionObserver(callback: any, options = {}) {
  const elementRef = ref(null);
  const isIntersecting = ref(false);
  let observer: IntersectionObserver | null = null;

  onMounted(() => {
    if (!elementRef.value) return;

    observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        // 如果是第一个元素或者元素进入视图，都触发回调
        if (entry.target === elementRef.value || entry.isIntersecting) {
          isIntersecting.value = true;
          callback(entry);
          if (observer) {
            observer.disconnect(); // 加载后停止观察
          }
        }
      });
    }, options);

    observer.observe(elementRef.value);
  });

  onUnmounted(() => {
    if (elementRef.value && observer) observer.unobserve(elementRef.value);
  });

  return { elementRef, isIntersecting };
}
