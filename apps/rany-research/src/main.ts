import { createApp } from 'vue';
import './plugins/assets';
import {
  NButton,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NPopover,
  NSelect
  // 这里可以按需导入其他更多的Naive UI组件，比如NCheckbox、NSelect等等，根据你实际需要使用的组件来添加
} from 'naive-ui';
import componentLibrary from 'component-library';
import { setupDayjs, setupIconifyOffline, setupLoading, setupNProgress } from './plugins';
import { setupStore } from './store';
import 'component-library/dist/styles/style.css';
import { router, setupRouter } from './router';
import { setupI18n } from './locales';
import App from './App.vue';
async function setupApp() {
  setupLoading();

  setupNProgress();

  setupIconifyOffline();

  setupDayjs();

  const app = createApp(App);
  setupStore(app);

  await setupRouter(app);

  setupI18n(app);

  // setupAppVersionNotification();
  app.component('NButton', NButton);
  app.component('NForm', NForm);
  app.component('NFormItem', NFormItem);
  app.component('NInput', NInput);
  app.component('NInputNumber', NInputNumber);
  app.component('NSelect', NSelect);
  app.component('NDatePicker', NDatePicker);
  app.component('NPopover', NPopover);
  app.use(componentLibrary, { router: router.getRoutes(), routerIns: router });
  app.mount('#app');
}

setupApp();
