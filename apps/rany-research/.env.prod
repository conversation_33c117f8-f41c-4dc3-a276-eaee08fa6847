# backend service base url, test environment
VITE_SERVICE_BASE_URL=/ly/api
VITE_BASE_URL=/ly/web/

# the prefix of the icon name
VITE_ICON_PREFIX=icon

# the prefix of the local svg icon component, must include VITE_ICON_PREFIX
# format {VITE_ICON_PREFIX}-{local icon name}
VITE_ICON_LOCAL_PREFIX=icon-local

VITE_AUTH_LOGIN_URL=/login

# the prefix of the icon name
VITE_ICON_PREFIX=icon

# the prefix of the local svg icon component, must include VITE_ICON_PREFIX
# format {VITE_ICON_PREFIX}-{local icon name}
VITE_ICON_LOCAL_PREFIX=icon-local

# other backend service base url, test environment
VITE_OTHER_SERVICE_BASE_URL= `{
  "data_platform": "/tpi",
  "file_path": "/filestorage/",
  "/cf/api/": "/cf/api",
  "/portal/api":"/portal/api",
}`
