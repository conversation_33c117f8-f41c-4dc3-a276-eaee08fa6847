import { defineConfig } from '@unocss/vite';
import transformerDirectives from '@unocss/transformer-directives';
import transformerVariantGroup from '@unocss/transformer-variant-group';
import presetUno from '@unocss/preset-uno';
import type { Theme } from '@unocss/preset-uno';
import { presetSoybeanAdmin } from '@sa/uno-preset';
import { themeVars } from './src/theme/vars';

export default defineConfig<Theme>({
  content: {
    pipeline: {
      exclude: ['node_modules', 'dist', '*.timestamp-*.mjs']
    }
  },
  theme: {
    ...themeVars,
    fontSize: {
      'icon-xs': '0.875rem',
      'icon-small': '1rem',
      icon: '1.125rem',
      'icon-large': '1.5rem',
      'icon-xl': '2rem'
    }
  },
  shortcuts: {
    'card-wrapper': 'shadow-sm',
    // 表格相关快捷类
    'table-container': 'w-full overflow-auto',
    'table-cell-center': 'text-center align-middle',
    'table-cell-left': 'text-left align-middle',
    'table-cell-right': 'text-right align-middle',
    'table-header': 'font-semibold bg-gray-50 dark:bg-gray-800',
    'table-row-hover': 'hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors',
    'table-row-selected': 'bg-blue-50 dark:bg-blue-900/20',
    'table-action-btn': 'p-1 min-w-8 text-xs',
    'table-status-dot': 'w-2 h-2 rounded-full inline-block mr-2'
  },
  transformers: [
    transformerDirectives({
      // 确保与PostCSS兼容
      enforceUnknownAtrules: false
    }),
    transformerVariantGroup()
  ],
  presets: [presetUno({ dark: 'class' }), presetSoybeanAdmin()]
});
